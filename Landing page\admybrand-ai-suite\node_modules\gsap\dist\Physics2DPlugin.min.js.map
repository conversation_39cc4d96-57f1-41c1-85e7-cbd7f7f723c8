{"version": 3, "file": "Physics2DPlugin.min.js", "sources": ["../src/Physics2DPlugin.js"], "sourcesContent": ["/*!\n * Physics2DPlugin 3.13.0\n * https://gsap.com\n *\n * @license Copyright 2008-2025, GreenSock. All rights reserved.\n * Subject to the terms at https://gsap.com/standard-license\n * @author: <PERSON>, <EMAIL>\n*/\n/* eslint-disable */\n\nlet gsap, _coreInitted, _getUnit, _getStyleSaver, _reverting,\n\t_DEG2RAD = Math.PI / 180,\n\t_getGSAP = () => gsap || (typeof(window) !== \"undefined\" && (gsap = window.gsap) && gsap.registerPlugin && gsap),\n\t_round = value => Math.round(value * 10000) / 10000,\n\t_bonusValidated = 1, //<name>Physics2DPlugin</name>\n\t_initCore = core => {\n\t\tgsap = core || _getGSAP();\n\t\tif (!_coreInitted) {\n\t\t\t_getUnit = gsap.utils.getUnit;\n\t\t\t_getStyleSaver = gsap.core.getStyleSaver;\n\t\t\t_reverting = gsap.core.reverting || function() {};\n\t\t\t_coreInitted = 1;\n\t\t}\n\t};\n\nclass PhysicsProp {\n\n\tconstructor(target, p, velocity, acceleration, stepsPerTimeUnit) {\n\t\tlet cache = target._gsap,\n\t\t\tcurVal = cache.get(target, p);\n\t\tthis.p = p;\n\t\tthis.set = cache.set(target, p); //setter\n\t\tthis.s = this.val = parseFloat(curVal);\n\t\tthis.u = _getUnit(curVal) || 0;\n\t\tthis.vel = velocity || 0;\n\t\tthis.v = this.vel / stepsPerTimeUnit;\n\t\tif (acceleration || acceleration === 0) {\n\t\t\tthis.acc = acceleration;\n\t\t\tthis.a = this.acc / (stepsPerTimeUnit * stepsPerTimeUnit);\n\t\t} else {\n\t\t\tthis.acc = this.a = 0;\n\t\t}\n\t}\n\n}\n\n\nexport const Physics2DPlugin = {\n\tversion:\"3.13.0\",\n\tname:\"physics2D\",\n\tregister: _initCore,\n\tinit(target, value, tween) {\n\t\t_coreInitted || _initCore();\n\t\tlet data = this,\n\t\t\tangle = +value.angle || 0,\n\t\t\tvelocity = +value.velocity || 0,\n\t\t\tacceleration = +value.acceleration || 0,\n\t\t\txProp = value.xProp || \"x\",\n\t\t\tyProp = value.yProp || \"y\",\n\t\t\taAngle = (value.accelerationAngle || value.accelerationAngle === 0) ? +value.accelerationAngle : angle;\n\t\tdata.styles = _getStyleSaver && _getStyleSaver(target, value.xProp && value.xProp !== \"x\" ? value.xProp + \",\" + value.yProp : \"transform\");\n\t\tdata.target = target;\n\t\tdata.tween = tween;\n\t\tdata.step = 0;\n\t\tdata.sps = 30; //steps per second\n\t\tif (value.gravity) {\n\t\t\tacceleration = +value.gravity;\n\t\t\taAngle = 90;\n\t\t}\n\t\tangle *= _DEG2RAD;\n\t\taAngle *= _DEG2RAD;\n\t\tdata.fr = 1 - (+value.friction || 0);\n\t\tdata._props.push(xProp, yProp);\n\n\t\tdata.xp = new PhysicsProp(target, xProp, Math.cos(angle) * velocity, Math.cos(aAngle) * acceleration, data.sps);\n\t\tdata.yp = new PhysicsProp(target, yProp, Math.sin(angle) * velocity, Math.sin(aAngle) * acceleration, data.sps);\n\t\tdata.skipX = data.skipY = 0;\n\t},\n\trender(ratio, data) {\n\t\tlet { xp, yp, tween, target, step, sps, fr, skipX, skipY } = data,\n\t\t\ttime = tween._from ? tween._dur - tween._time : tween._time,\n\t\t\tx, y, tt, steps, remainder, i;\n\t\tif (tween._time || !_reverting()) {\n\t\t\tif (fr === 1) {\n\t\t\t\ttt = time * time * 0.5;\n\t\t\t\tx = xp.s + xp.vel * time + xp.acc * tt;\n\t\t\t\ty = yp.s + yp.vel * time + yp.acc * tt;\n\t\t\t} else {\n\t\t\t\ttime *= sps;\n\t\t\t\tsteps = i = (time | 0) - step;\n\t\t\t\t/*\n\t\t\t\tNote: rounding errors build up if we walk the calculations backward which we used to do like this to maximize performance:\n\t\t\t\t\ti = -i;\n\t\t\t\t\twhile (i--) {\n\t\t\t\t\t\txp.val -= xp.v;\n\t\t\t\t\t\typ.val -= yp.v;\n\t\t\t\t\t\txp.v /= fr;\n\t\t\t\t\t\typ.v /= fr;\n\t\t\t\t\t\txp.v -= xp.a;\n\t\t\t\t\t\typ.v -= yp.a;\n\t\t\t\t\t}\n\t\t\t\tbut now for the sake of accuracy (to ensure rewinding always goes back to EXACTLY the same spot), we force the calculations to go forward every time. So if the tween is going backward, we just start from the beginning and iterate. This is only necessary with friction.\n\t\t\t\t */\n\t\t\t\tif (i < 0) {\n\t\t\t\t\txp.v = xp.vel / sps;\n\t\t\t\t\typ.v = yp.vel / sps;\n\t\t\t\t\txp.val = xp.s;\n\t\t\t\t\typ.val = yp.s;\n\t\t\t\t\tdata.step = 0;\n\t\t\t\t\tsteps = i = time | 0;\n\t\t\t\t}\n\t\t\t\tremainder = (time % 1) * fr;\n\t\t\t\twhile (i--) {\n\t\t\t\t\txp.v += xp.a;\n\t\t\t\t\typ.v += yp.a;\n\t\t\t\t\txp.v *= fr;\n\t\t\t\t\typ.v *= fr;\n\t\t\t\t\txp.val += xp.v;\n\t\t\t\t\typ.val += yp.v;\n\t\t\t\t}\n\t\t\t\tx = xp.val + xp.v * remainder;\n\t\t\t\ty = yp.val + yp.v * remainder;\n\t\t\t\tdata.step += steps;\n\t\t\t}\n\t\t\tskipX || xp.set(target, xp.p, _round(x) + xp.u);\n\t\t\tskipY || yp.set(target, yp.p, _round(y) + yp.u);\n\t\t} else {\n\t\t\tdata.styles.revert();\n\t\t}\n\t},\n\tkill(property) {\n\t\tif (this.xp.p === property) {\n\t\t\tthis.skipX = 1;\n\t\t}\n\t\tif (this.yp.p === property) {\n\t\t\tthis.skipY = 1;\n\t\t}\n\t}\n};\n\n\n_getGSAP() && gsap.registerPlugin(Physics2DPlugin);\n\nexport { Physics2DPlugin as default };"], "names": ["_getGSAP", "gsap", "window", "registerPlugin", "_round", "value", "Math", "round", "_initCore", "core", "_coreInitted", "_getUnit", "utils", "getUnit", "_getStyleSaver", "getStyleSaver", "_reverting", "reverting", "PhysicsProp", "target", "p", "velocity", "acceleration", "stepsPerTimeUnit", "cache", "_gsap", "curVal", "get", "set", "s", "this", "val", "parseFloat", "u", "vel", "v", "acc", "a", "_DEG2RAD", "PI", "Physics2DPlugin", "version", "name", "register", "init", "tween", "data", "angle", "xProp", "yProp", "aAngle", "accelerationAngle", "styles", "step", "sps", "gravity", "fr", "friction", "_props", "push", "xp", "cos", "yp", "sin", "skipX", "skipY", "render", "ratio", "x", "y", "tt", "steps", "remainder", "i", "time", "_from", "_dur", "_time", "revert", "kill", "property"], "mappings": ";;;;;;;;;6MAYY,SAAXA,WAAiBC,GAA4B,oBAAZC,SAA4BD,EAAOC,OAAOD,OAASA,EAAKE,gBAAkBF,EAClG,SAATG,EAASC,UAASC,KAAKC,MAAc,IAARF,GAAiB,IAElC,SAAZG,EAAYC,GACXR,EAAOQ,GAAQT,IACVU,IACJC,EAAWV,EAAKW,MAAMC,QACtBC,EAAiBb,EAAKQ,KAAKM,cAC3BC,EAAaf,EAAKQ,KAAKQ,WAAa,aACpCP,EAAe,GAMjB,SAFKQ,EAEOC,EAAQC,EAAGC,EAAUC,EAAcC,OAC1CC,EAAQL,EAAOM,MAClBC,EAASF,EAAMG,IAAIR,EAAQC,QACvBA,EAAIA,OACJQ,IAAMJ,EAAMI,IAAIT,EAAQC,QACxBS,EAAIC,KAAKC,IAAMC,WAAWN,QAC1BO,EAAItB,EAASe,IAAW,OACxBQ,IAAMb,GAAY,OAClBc,EAAIL,KAAKI,IAAMX,EAChBD,GAAiC,IAAjBA,QACdc,IAAMd,OACNe,EAAIP,KAAKM,KAAOb,EAAmBA,SAEnCa,IAAMN,KAAKO,EAAI,MA9BnBpC,EAAMS,EAAcC,EAAUG,EAAgBE,EACjDsB,EAAWhC,KAAKiC,GAAK,IAoCTC,EAAkB,CAC9BC,QAAQ,SACRC,KAAK,YACLC,SAAUnC,EACVoC,mBAAKzB,EAAQd,EAAOwC,GACnBnC,GAAgBF,QACZsC,EAAOhB,KACViB,GAAS1C,EAAM0C,OAAS,EACxB1B,GAAYhB,EAAMgB,UAAY,EAC9BC,GAAgBjB,EAAMiB,cAAgB,EACtC0B,EAAQ3C,EAAM2C,OAAS,IACvBC,EAAQ5C,EAAM4C,OAAS,IACvBC,EAAU7C,EAAM8C,mBAAiD,IAA5B9C,EAAM8C,mBAA4B9C,EAAM8C,kBAAoBJ,EAClGD,EAAKM,OAAStC,GAAkBA,EAAeK,EAAQd,EAAM2C,OAAyB,MAAhB3C,EAAM2C,MAAgB3C,EAAM2C,MAAQ,IAAM3C,EAAM4C,MAAQ,aAC9HH,EAAK3B,OAASA,EACd2B,EAAKD,MAAQA,EACbC,EAAKO,KAAO,EACZP,EAAKQ,IAAM,GACPjD,EAAMkD,UACTjC,GAAgBjB,EAAMkD,QACtBL,EAAS,IAEVH,GAAST,EACTY,GAAUZ,EACVQ,EAAKU,GAAK,IAAMnD,EAAMoD,UAAY,GAClCX,EAAKY,OAAOC,KAAKX,EAAOC,GAExBH,EAAKc,GAAK,IAAI1C,EAAYC,EAAQ6B,EAAO1C,KAAKuD,IAAId,GAAS1B,EAAUf,KAAKuD,IAAIX,GAAU5B,EAAcwB,EAAKQ,KAC3GR,EAAKgB,GAAK,IAAI5C,EAAYC,EAAQ8B,EAAO3C,KAAKyD,IAAIhB,GAAS1B,EAAUf,KAAKyD,IAAIb,GAAU5B,EAAcwB,EAAKQ,KAC3GR,EAAKkB,MAAQlB,EAAKmB,MAAQ,GAE3BC,uBAAOC,EAAOrB,OAGZsB,EAAGC,EAAGC,EAAIC,EAAOC,EAAWC,EAFvBb,EAAuDd,EAAvDc,GAAIE,EAAmDhB,EAAnDgB,GAAIjB,EAA+CC,EAA/CD,MAAO1B,EAAwC2B,EAAxC3B,OAAQkC,EAAgCP,EAAhCO,KAAMC,EAA0BR,EAA1BQ,IAAKE,EAAqBV,EAArBU,GAAIQ,EAAiBlB,EAAjBkB,MAAOC,EAAUnB,EAAVmB,MAClDS,EAAO7B,EAAM8B,MAAQ9B,EAAM+B,KAAO/B,EAAMgC,MAAQhC,EAAMgC,SAEnDhC,EAAMgC,QAAU7D,IAAc,IACtB,IAAPwC,EACHc,EAAKI,EAAOA,EAAO,GACnBN,EAAIR,EAAG/B,EAAI+B,EAAG1B,IAAMwC,EAAOd,EAAGxB,IAAMkC,EACpCD,EAAIP,EAAGjC,EAAIiC,EAAG5B,IAAMwC,EAAOZ,EAAG1B,IAAMkC,MAC9B,KAENC,EAAQE,GAAY,GADpBC,GAAQpB,IACiBD,EAcrBoB,EAAI,IACPb,EAAGzB,EAAIyB,EAAG1B,IAAMoB,EAChBQ,EAAG3B,EAAI2B,EAAG5B,IAAMoB,EAChBM,EAAG7B,IAAM6B,EAAG/B,EACZiC,EAAG/B,IAAM+B,EAAGjC,EAEZ0C,EAAQE,GADR3B,EAAKO,KAAO,GACAqB,GAEbF,EAAaE,EAAO,EAAKlB,EAClBiB,KACNb,EAAGzB,GAAKyB,EAAGvB,EACXyB,EAAG3B,GAAK2B,EAAGzB,EACXuB,EAAGzB,GAAKqB,EACRM,EAAG3B,GAAKqB,EACRI,EAAG7B,KAAO6B,EAAGzB,EACb2B,EAAG/B,KAAO+B,EAAG3B,EAEdiC,EAAIR,EAAG7B,IAAM6B,EAAGzB,EAAIqC,EACpBH,EAAIP,EAAG/B,IAAM+B,EAAG3B,EAAIqC,EACpB1B,EAAKO,MAAQkB,EAEdP,GAASJ,EAAGhC,IAAIT,EAAQyC,EAAGxC,EAAGhB,EAAOgE,GAAKR,EAAG3B,GAC7CgC,GAASH,EAAGlC,IAAIT,EAAQ2C,EAAG1C,EAAGhB,EAAOiE,GAAKP,EAAG7B,QAE7Ca,EAAKM,OAAO0B,UAGdC,mBAAKC,GACAlD,KAAK8B,GAAGxC,IAAM4D,SACZhB,MAAQ,GAEVlC,KAAKgC,GAAG1C,IAAM4D,SACZf,MAAQ,KAMhBjE,KAAcC,EAAKE,eAAeqC"}