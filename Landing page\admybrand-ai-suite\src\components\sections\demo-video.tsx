'use client'

import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { motion } from 'framer-motion'
import { Clock, Maximize, Play, TrendingUp, Users, Volume2 } from 'lucide-react'
import { useState } from 'react'

const DemoVideo = () => {
  const [isPlaying, setIsPlaying] = useState(false)

  const stats = [
    { icon: Clock, value: '2 min', label: 'Quick Setup' },
    { icon: Users, value: '50K+', label: 'Happy Users' },
    { icon: TrendingUp, value: '300%', label: 'ROI Increase' }
  ]

  return (
    <section className="py-24 bg-gradient-to-br from-black via-gray-900 to-black">
      <div className="max-w-7xl mx-auto px-2 sm:px-4 lg:px-6">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <Badge className="mb-4 bg-white/10 text-white border-white/20 backdrop-blur-sm">
            <Play className="w-4 h-4 mr-2" />
            Product Demo
          </Badge>
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
            See ADmyBRAND in
            <span className="bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent"> action</span>
          </h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            Watch how our AI-powered marketing suite transforms your workflow in just 2 minutes.
          </p>
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-16">
          {/* Video Player */}
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            whileInView={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="lg:col-span-2"
          >
            <div className="relative aspect-video rounded-2xl overflow-hidden bg-white border border-gray-200 shadow-2xl">
              {/* Video Header */}
              <div className="absolute top-0 left-0 right-0 bg-white border-b border-gray-200 p-4 z-10">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-semibold text-gray-800">Video Ad Preview</h3>
                  <div className="bg-red-500 text-white px-3 py-1 rounded-full text-sm font-medium">
                    Live Demo
                  </div>
                </div>
              </div>

              {/* Video Content */}
              <div className="absolute inset-0 pt-16 bg-gradient-to-br from-blue-500 to-cyan-400">
                {/* Play Button */}
                {!isPlaying && (
                  <motion.button
                    onClick={() => setIsPlaying(true)}
                    className="absolute inset-0 flex items-center justify-center group"
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <div className="w-20 h-20 bg-white/20 backdrop-blur-md border border-white/30 rounded-full flex items-center justify-center group-hover:bg-white/30 transition-all duration-300">
                      <Play className="w-8 h-8 text-white ml-1" fill="currentColor" />
                    </div>
                  </motion.button>
                )}

                {/* Video Content */}
                <div className="absolute inset-0 flex flex-col items-center justify-center text-white p-8">
                  <h2 className="text-3xl md:text-4xl font-bold mb-4 text-center">
                    Transform Your Business
                  </h2>
                  <p className="text-lg md:text-xl text-center opacity-90">
                    AI-powered marketing that delivers results
                  </p>
                </div>

                {/* Duration Badge */}
                <div className="absolute top-20 right-4 bg-black/50 text-white px-2 py-1 rounded text-sm font-medium">
                  0:30
                </div>

                {/* Video Controls (when playing) */}
                {isPlaying && (
                  <motion.div
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    className="absolute bottom-4 left-4 right-4 bg-black/50 backdrop-blur-md rounded-lg p-4"
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-4">
                        <button
                          onClick={() => setIsPlaying(false)}
                          className="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center hover:bg-white/30 transition-colors"
                        >
                          <Play className="w-4 h-4 text-white" fill="currentColor" />
                        </button>
                        <span className="text-white text-sm">0:45 / 2:15</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Volume2 className="w-4 h-4 text-white" />
                        <Maximize className="w-4 h-4 text-white" />
                      </div>
                    </div>
                    <div className="mt-2 bg-white/20 rounded-full h-1">
                      <motion.div
                        className="bg-blue-400 h-1 rounded-full"
                        initial={{ width: "0%" }}
                        animate={{ width: "35%" }}
                        transition={{ duration: 2 }}
                      />
                    </div>
                  </motion.div>
                )}

                {/* Video Thumbnail Content */}
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="text-center text-white">
                    <motion.div
                      animate={{ 
                        scale: [1, 1.1, 1],
                        opacity: [0.7, 1, 0.7]
                      }}
                      transition={{ 
                        duration: 2,
                        repeat: Infinity,
                        ease: "easeInOut"
                      }}
                      className="text-6xl mb-4"
                    >
                      🎬
                    </motion.div>
                    <h3 className="text-2xl font-bold mb-2">ADmyBRAND Demo</h3>
                    <p className="text-gray-300">See the magic happen</p>
                  </div>
                </div>
              </div>

              {/* Floating elements */}
              <motion.div
                className="absolute top-4 left-4 bg-red-500 text-white px-3 py-1 rounded-full text-sm font-medium"
                animate={{ scale: [1, 1.1, 1] }}
                transition={{ duration: 2, repeat: Infinity }}
              >
                LIVE
              </motion.div>
            </div>
          </motion.div>

          {/* Stats & Features */}
          <motion.div
            initial={{ opacity: 0, x: 30 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="space-y-6"
          >
            {/* Stats */}
            <div className="bg-white/5 backdrop-blur-xl border border-white/10 rounded-2xl p-6">
              <h3 className="text-xl font-bold text-white mb-6">Why Choose ADmyBRAND?</h3>
              <div className="space-y-4">
                {stats.map((stat, index) => (
                  <motion.div
                    key={stat.label}
                    initial={{ opacity: 0, x: -20 }}
                    whileInView={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.5, delay: index * 0.1 }}
                    viewport={{ once: true }}
                    className="flex items-center gap-4"
                  >
                    <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-500 rounded-xl flex items-center justify-center">
                      <stat.icon className="w-6 h-6 text-white" />
                    </div>
                    <div>
                      <div className="text-2xl font-bold text-white">{stat.value}</div>
                      <div className="text-gray-400 text-sm">{stat.label}</div>
                    </div>
                  </motion.div>
                ))}
              </div>
            </div>

            {/* CTA */}
            <div className="bg-gradient-to-br from-blue-500/10 to-purple-500/10 backdrop-blur-xl border border-white/20 rounded-2xl p-6">
              <h4 className="text-lg font-bold text-white mb-3">Ready to get started?</h4>
              <p className="text-gray-300 mb-4 text-sm">
                Join thousands of businesses already using ADmyBRAND to scale their marketing.
              </p>
              <Button className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white py-3 rounded-xl font-semibold transition-all duration-300">
                Start Free Trial
              </Button>
            </div>
          </motion.div>
        </div>

        {/* Feature Cards */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="mt-16"
        >
          <h3 className="text-2xl font-bold text-white mb-8 text-center">Featured Solutions</h3>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {[
              {
                title: "Meals Made For Your Baby",
                description: "Custom nutrition for your child",
                details: "Simple, Easy-to-use app",
                icon: "$5",
                iconBg: "bg-gradient-to-br from-orange-500 to-red-500",
                buttonText: "Learn More",
                buttonStyle: "bg-orange-500 hover:bg-orange-600 text-white"
              },
              {
                title: "Investing Made For You",
                description: "Simple, Easy-to-use app",
                details: "Join happy pet families today",
                icon: "+12.5%",
                iconBg: "bg-gradient-to-br from-green-500 to-emerald-500",
                buttonText: "Learn More",
                buttonStyle: "bg-green-500 hover:bg-green-600 text-white"
              },
              {
                title: "Trusted by Local Pet Owners",
                description: "Join happy pet families today",
                details: "Miss it, miss the journey",
                icon: "4.8★",
                iconBg: "bg-gradient-to-br from-blue-500 to-purple-500",
                buttonText: "Learn More",
                buttonStyle: "bg-blue-500 hover:bg-blue-600 text-white"
              },
              {
                title: "Adventure Awaits",
                description: "Miss it, miss the journey",
                details: "LIMITED",
                icon: "⚡",
                iconBg: "bg-gradient-to-br from-yellow-600 to-orange-700",
                buttonText: "Learn More",
                buttonStyle: "bg-yellow-600 hover:bg-yellow-700 text-white"
              }
            ].map((feature, index) => (
              <motion.div
                key={feature.title}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer group"
                onClick={() => setIsPlaying(true)}
              >
                <div className={`w-16 h-16 rounded-xl flex items-center justify-center text-white font-bold text-lg mb-4 ${feature.iconBg}`}>
                  {feature.icon}
                </div>
                <h4 className="font-bold text-gray-800 mb-2 text-lg">{feature.title}</h4>
                <p className="text-gray-600 text-sm mb-2">{feature.description}</p>
                <p className="text-gray-500 text-xs mb-4">{feature.details}</p>
                <button className={`px-4 py-2 rounded-lg font-medium text-sm transition-all duration-300 ${feature.buttonStyle}`}>
                  {feature.buttonText}
                </button>
              </motion.div>
            ))}
        </motion.div>
      </div>
    </section>
  )
}

export default DemoVideo
