{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Landing%20page/admybrand-ai-suite/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatPrice(price: number): string {\n  return new Intl.NumberFormat('en-US', {\n    style: 'currency',\n    currency: 'USD',\n  }).format(price)\n}\n\nexport function formatNumber(num: number): string {\n  return new Intl.NumberFormat('en-US').format(num)\n}\n\nexport function slugify(text: string): string {\n  return text\n    .toLowerCase()\n    .replace(/[^\\w ]+/g, '')\n    .replace(/ +/g, '-')\n}\n\nexport function truncate(text: string, length: number): string {\n  if (text.length <= length) return text\n  return text.slice(0, length) + '...'\n}\n\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout)\n    timeout = setTimeout(() => func(...args), wait)\n  }\n}\n\nexport function throttle<T extends (...args: any[]) => any>(\n  func: T,\n  limit: number\n): (...args: Parameters<T>) => void {\n  let inThrottle: boolean\n  return (...args: Parameters<T>) => {\n    if (!inThrottle) {\n      func(...args)\n      inThrottle = true\n      setTimeout(() => (inThrottle = false), limit)\n    }\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,YAAY,KAAa;IACvC,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU;IACZ,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,aAAa,GAAW;IACtC,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS,MAAM,CAAC;AAC/C;AAEO,SAAS,QAAQ,IAAY;IAClC,OAAO,KACJ,WAAW,GACX,OAAO,CAAC,YAAY,IACpB,OAAO,CAAC,OAAO;AACpB;AAEO,SAAS,SAAS,IAAY,EAAE,MAAc;IACnD,IAAI,KAAK,MAAM,IAAI,QAAQ,OAAO;IAClC,OAAO,KAAK,KAAK,CAAC,GAAG,UAAU;AACjC;AAEO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAEO,SAAS,SACd,IAAO,EACP,KAAa;IAEb,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,IAAI,CAAC,YAAY;YACf,QAAQ;YACR,aAAa;YACb,WAAW,IAAO,aAAa,OAAQ;QACzC;IACF;AACF", "debugId": null}}, {"offset": {"line": 58, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Landing%20page/admybrand-ai-suite/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n        gradient: \"bg-gradient-to-r from-purple-600 to-blue-600 text-white hover:from-purple-700 hover:to-blue-700 shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200\",\n        glass: \"glass text-white hover:bg-white/20 backdrop-blur-md border border-white/20\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        xl: \"h-12 rounded-lg px-10 text-base\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n  loading?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, loading = false, children, disabled, ...props }, ref) => {\n    return (\n      <button\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        disabled={disabled || loading}\n        {...props}\n      >\n        {loading && (\n          <svg\n            className=\"animate-spin -ml-1 mr-2 h-4 w-4\"\n            xmlns=\"http://www.w3.org/2000/svg\"\n            fill=\"none\"\n            viewBox=\"0 0 24 24\"\n          >\n            <circle\n              className=\"opacity-25\"\n              cx=\"12\"\n              cy=\"12\"\n              r=\"10\"\n              stroke=\"currentColor\"\n              strokeWidth=\"4\"\n            ></circle>\n            <path\n              className=\"opacity-75\"\n              fill=\"currentColor\"\n              d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n            ></path>\n          </svg>\n        )}\n        {children}\n      </button>\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,0RACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;YACN,UAAU;YACV,OAAO;QACT;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAUF,MAAM,uBAAS,qMAAA,CAAA,aAAgB,CAC7B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,UAAU,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IAC7F,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACL,UAAU,YAAY;QACrB,GAAG,KAAK;;YAER,yBACC,8OAAC;gBACC,WAAU;gBACV,OAAM;gBACN,MAAK;gBACL,SAAQ;;kCAER,8OAAC;wBACC,WAAU;wBACV,IAAG;wBACH,IAAG;wBACH,GAAE;wBACF,QAAO;wBACP,aAAY;;;;;;kCAEd,8OAAC;wBACC,WAAU;wBACV,MAAK;wBACL,GAAE;;;;;;;;;;;;YAIP;;;;;;;AAGP;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 153, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Landing%20page/admybrand-ai-suite/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst inputVariants = cva(\n  \"flex w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n  {\n    variants: {\n      variant: {\n        default: \"\",\n        glass: \"glass border-white/20 backdrop-blur-md placeholder:text-white/70\",\n        filled: \"bg-muted border-transparent focus-visible:bg-background\",\n      },\n      inputSize: {\n        default: \"h-10\",\n        sm: \"h-9 px-2 text-xs\",\n        lg: \"h-11 px-4\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      inputSize: \"default\",\n    },\n  }\n)\n\nexport interface InputProps\n  extends React.InputHTMLAttributes<HTMLInputElement>,\n    VariantProps<typeof inputVariants> {\n  label?: string\n  error?: string\n  icon?: React.ReactNode\n}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ className, variant, inputSize, type, label, error, icon, ...props }, ref) => {\n    return (\n      <div className=\"w-full\">\n        {label && (\n          <label className=\"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 mb-2 block\">\n            {label}\n          </label>\n        )}\n        <div className=\"relative\">\n          {icon && (\n            <div className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground\">\n              {icon}\n            </div>\n          )}\n          <input\n            type={type}\n            className={cn(\n              inputVariants({ variant, inputSize }),\n              icon && \"pl-10\",\n              error && \"border-destructive focus-visible:ring-destructive\",\n              className\n            )}\n            ref={ref}\n            {...props}\n          />\n        </div>\n        {error && (\n          <p className=\"text-sm text-destructive mt-1\">{error}</p>\n        )}\n      </div>\n    )\n  }\n)\nInput.displayName = \"Input\"\n\nexport { Input, inputVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,2VACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,OAAO;YACP,QAAQ;QACV;QACA,WAAW;YACT,SAAS;YACT,IAAI;YACJ,IAAI;QACN;IACF;IACA,iBAAiB;QACf,SAAS;QACT,WAAW;IACb;AACF;AAWF,MAAM,sBAAQ,qMAAA,CAAA,aAAgB,CAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IACtE,qBACE,8OAAC;QAAI,WAAU;;YACZ,uBACC,8OAAC;gBAAM,WAAU;0BACd;;;;;;0BAGL,8OAAC;gBAAI,WAAU;;oBACZ,sBACC,8OAAC;wBAAI,WAAU;kCACZ;;;;;;kCAGL,8OAAC;wBACC,MAAM;wBACN,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,cAAc;4BAAE;4BAAS;wBAAU,IACnC,QAAQ,SACR,SAAS,qDACT;wBAEF,KAAK;wBACJ,GAAG,KAAK;;;;;;;;;;;;YAGZ,uBACC,8OAAC;gBAAE,WAAU;0BAAiC;;;;;;;;;;;;AAItD;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 246, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Landing%20page/admybrand-ai-suite/src/components/layout/footer.tsx"], "sourcesContent": ["'use client'\n\nimport { But<PERSON> } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { motion } from 'framer-motion'\nimport {\n    ArrowRight,\n    Github,\n    Linkedin,\n    Mail,\n    MapPin,\n    Phone,\n    Sparkles,\n    Twitter,\n    Youtube\n} from 'lucide-react'\n\nconst footerLinks = {\n  product: [\n    { name: 'Features', href: '#features' },\n    { name: 'Pricing', href: '#pricing' },\n    { name: 'Integrations', href: '#integrations' },\n    { name: 'API Documentation', href: '#api' },\n    { name: 'Changelog', href: '#changelog' }\n  ],\n  company: [\n    { name: 'About Us', href: '#about' },\n    { name: 'Careers', href: '#careers' },\n    { name: 'Press Kit', href: '#press' },\n    { name: 'Contact', href: '#contact' },\n    { name: 'Partners', href: '#partners' }\n  ],\n  resources: [\n    { name: 'Blog', href: '#blog' },\n    { name: 'Help Center', href: '#help' },\n    { name: 'Community', href: '#community' },\n    { name: 'Webinars', href: '#webinars' },\n    { name: 'Case Studies', href: '#cases' }\n  ],\n  legal: [\n    { name: 'Privacy Policy', href: '#privacy' },\n    { name: 'Terms of Service', href: '#terms' },\n    { name: 'Cookie Policy', href: '#cookies' },\n    { name: 'GDPR', href: '#gdpr' },\n    { name: 'Security', href: '#security' }\n  ]\n}\n\nconst socialLinks = [\n  { name: 'Twitter', icon: Twitter, href: '#twitter' },\n  { name: 'LinkedIn', icon: Linkedin, href: '#linkedin' },\n  { name: 'GitHub', icon: Github, href: '#github' },\n  { name: 'YouTube', icon: Youtube, href: '#youtube' }\n]\n\nconst Footer = () => {\n  return (\n    <footer className=\"bg-gray-900 text-white\">\n      {/* Newsletter Section */}\n      <div className=\"border-b border-gray-800\">\n        <div className=\"max-w-7xl mx-auto px-2 sm:px-4 lg:px-6 py-16\">\n          <motion.div\n            initial={{ opacity: 0, y: 30 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8 }}\n            viewport={{ once: true }}\n            className=\"text-center max-w-2xl mx-auto\"\n          >\n            <div className=\"flex items-center justify-center mb-4\">\n              <Sparkles className=\"w-8 h-8 text-purple-400 mr-3\" />\n              <h3 className=\"text-3xl font-bold\">Stay Updated</h3>\n            </div>\n            <p className=\"text-gray-400 mb-8\">\n              Get the latest AI marketing insights, product updates, and exclusive tips delivered to your inbox.\n            </p>\n            <div className=\"flex flex-col sm:flex-row gap-4 max-w-md mx-auto\">\n              <Input\n                placeholder=\"Enter your email\"\n                className=\"flex-1 bg-gray-800 border-gray-700 text-white placeholder:text-gray-400\"\n              />\n              <Button variant=\"gradient\" className=\"group\">\n                Subscribe\n                <ArrowRight className=\"ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform\" />\n              </Button>\n            </div>\n            <p className=\"text-xs text-gray-500 mt-4\">\n              No spam. Unsubscribe at any time.\n            </p>\n          </motion.div>\n        </div>\n      </div>\n\n      {/* Main Footer Content */}\n      <div className=\"max-w-7xl mx-auto px-2 sm:px-4 lg:px-6 py-16\">\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-8\">\n          {/* Brand Section */}\n          <motion.div\n            initial={{ opacity: 0, y: 30 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8 }}\n            viewport={{ once: true }}\n            className=\"lg:col-span-2\"\n          >\n            <div className=\"flex items-center mb-4\">\n              <div className=\"w-10 h-10 bg-gradient-to-r from-purple-500 to-blue-500 rounded-lg flex items-center justify-center mr-3\">\n                <Sparkles className=\"w-6 h-6 text-white\" />\n              </div>\n              <span className=\"text-2xl font-bold\">ADmyBRAND</span>\n            </div>\n            <p className=\"text-gray-400 mb-6 max-w-sm\">\n              Transform your marketing with AI-powered tools that create, optimize, and scale your campaigns automatically.\n            </p>\n            \n            {/* Contact Info */}\n            <div className=\"space-y-3\">\n              <div className=\"flex items-center text-gray-400\">\n                <Mail className=\"w-4 h-4 mr-3\" />\n                <span className=\"text-sm\"><EMAIL></span>\n              </div>\n              <div className=\"flex items-center text-gray-400\">\n                <Phone className=\"w-4 h-4 mr-3\" />\n                <span className=\"text-sm\">+****************</span>\n              </div>\n              <div className=\"flex items-center text-gray-400\">\n                <MapPin className=\"w-4 h-4 mr-3\" />\n                <span className=\"text-sm\">San Francisco, CA</span>\n              </div>\n            </div>\n          </motion.div>\n\n          {/* Product Links */}\n          <motion.div\n            initial={{ opacity: 0, y: 30 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8, delay: 0.1 }}\n            viewport={{ once: true }}\n          >\n            <h4 className=\"text-lg font-semibold mb-4\">Product</h4>\n            <ul className=\"space-y-3\">\n              {footerLinks.product.map((link) => (\n                <li key={link.name}>\n                  <a\n                    href={link.href}\n                    className=\"text-gray-400 hover:text-white transition-colors duration-200 text-sm\"\n                  >\n                    {link.name}\n                  </a>\n                </li>\n              ))}\n            </ul>\n          </motion.div>\n\n          {/* Company Links */}\n          <motion.div\n            initial={{ opacity: 0, y: 30 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8, delay: 0.2 }}\n            viewport={{ once: true }}\n          >\n            <h4 className=\"text-lg font-semibold mb-4\">Company</h4>\n            <ul className=\"space-y-3\">\n              {footerLinks.company.map((link) => (\n                <li key={link.name}>\n                  <a\n                    href={link.href}\n                    className=\"text-gray-400 hover:text-white transition-colors duration-200 text-sm\"\n                  >\n                    {link.name}\n                  </a>\n                </li>\n              ))}\n            </ul>\n          </motion.div>\n\n          {/* Resources Links */}\n          <motion.div\n            initial={{ opacity: 0, y: 30 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8, delay: 0.3 }}\n            viewport={{ once: true }}\n          >\n            <h4 className=\"text-lg font-semibold mb-4\">Resources</h4>\n            <ul className=\"space-y-3\">\n              {footerLinks.resources.map((link) => (\n                <li key={link.name}>\n                  <a\n                    href={link.href}\n                    className=\"text-gray-400 hover:text-white transition-colors duration-200 text-sm\"\n                  >\n                    {link.name}\n                  </a>\n                </li>\n              ))}\n            </ul>\n          </motion.div>\n\n          {/* Legal Links */}\n          <motion.div\n            initial={{ opacity: 0, y: 30 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8, delay: 0.4 }}\n            viewport={{ once: true }}\n          >\n            <h4 className=\"text-lg font-semibold mb-4\">Legal</h4>\n            <ul className=\"space-y-3\">\n              {footerLinks.legal.map((link) => (\n                <li key={link.name}>\n                  <a\n                    href={link.href}\n                    className=\"text-gray-400 hover:text-white transition-colors duration-200 text-sm\"\n                  >\n                    {link.name}\n                  </a>\n                </li>\n              ))}\n            </ul>\n          </motion.div>\n        </div>\n      </div>\n\n      {/* Bottom Section */}\n      <div className=\"border-t border-gray-800\">\n        <div className=\"max-w-7xl mx-auto px-2 sm:px-4 lg:px-6 py-8\">\n          <div className=\"flex flex-col md:flex-row justify-between items-center\">\n            <motion.div\n              initial={{ opacity: 0, x: -30 }}\n              whileInView={{ opacity: 1, x: 0 }}\n              transition={{ duration: 0.8 }}\n              viewport={{ once: true }}\n              className=\"text-gray-400 text-sm mb-4 md:mb-0\"\n            >\n              © 2025 ADmyBRAND AI Suite. All rights reserved.\n            </motion.div>\n\n            {/* Social Links */}\n            <motion.div\n              initial={{ opacity: 0, x: 30 }}\n              whileInView={{ opacity: 1, x: 0 }}\n              transition={{ duration: 0.8 }}\n              viewport={{ once: true }}\n              className=\"flex space-x-4\"\n            >\n              {socialLinks.map((social) => (\n                <a\n                  key={social.name}\n                  href={social.href}\n                  className=\"w-10 h-10 bg-gray-800 rounded-lg flex items-center justify-center hover:bg-gray-700 transition-colors duration-200 group\"\n                  aria-label={social.name}\n                >\n                  <social.icon className=\"w-5 h-5 text-gray-400 group-hover:text-white transition-colors duration-200\" />\n                </a>\n              ))}\n            </motion.div>\n          </div>\n        </div>\n      </div>\n    </footer>\n  )\n}\n\nexport default Footer\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AALA;;;;;;AAiBA,MAAM,cAAc;IAClB,SAAS;QACP;YAAE,MAAM;YAAY,MAAM;QAAY;QACtC;YAAE,MAAM;YAAW,MAAM;QAAW;QACpC;YAAE,MAAM;YAAgB,MAAM;QAAgB;QAC9C;YAAE,MAAM;YAAqB,MAAM;QAAO;QAC1C;YAAE,MAAM;YAAa,MAAM;QAAa;KACzC;IACD,SAAS;QACP;YAAE,MAAM;YAAY,MAAM;QAAS;QACnC;YAAE,MAAM;YAAW,MAAM;QAAW;QACpC;YAAE,MAAM;YAAa,MAAM;QAAS;QACpC;YAAE,MAAM;YAAW,MAAM;QAAW;QACpC;YAAE,MAAM;YAAY,MAAM;QAAY;KACvC;IACD,WAAW;QACT;YAAE,MAAM;YAAQ,MAAM;QAAQ;QAC9B;YAAE,MAAM;YAAe,MAAM;QAAQ;QACrC;YAAE,MAAM;YAAa,MAAM;QAAa;QACxC;YAAE,MAAM;YAAY,MAAM;QAAY;QACtC;YAAE,MAAM;YAAgB,MAAM;QAAS;KACxC;IACD,OAAO;QACL;YAAE,MAAM;YAAkB,MAAM;QAAW;QAC3C;YAAE,MAAM;YAAoB,MAAM;QAAS;QAC3C;YAAE,MAAM;YAAiB,MAAM;QAAW;QAC1C;YAAE,MAAM;YAAQ,MAAM;QAAQ;QAC9B;YAAE,MAAM;YAAY,MAAM;QAAY;KACvC;AACH;AAEA,MAAM,cAAc;IAClB;QAAE,MAAM;QAAW,MAAM,wMAAA,CAAA,UAAO;QAAE,MAAM;IAAW;IACnD;QAAE,MAAM;QAAY,MAAM,0MAAA,CAAA,WAAQ;QAAE,MAAM;IAAY;IACtD;QAAE,MAAM;QAAU,MAAM,sMAAA,CAAA,SAAM;QAAE,MAAM;IAAU;IAChD;QAAE,MAAM;QAAW,MAAM,wMAAA,CAAA,UAAO;QAAE,MAAM;IAAW;CACpD;AAED,MAAM,SAAS;IACb,qBACE,8OAAC;QAAO,WAAU;;0BAEhB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,UAAU;4BAAE,MAAM;wBAAK;wBACvB,WAAU;;0CAEV,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,0MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,8OAAC;wCAAG,WAAU;kDAAqB;;;;;;;;;;;;0CAErC,8OAAC;gCAAE,WAAU;0CAAqB;;;;;;0CAGlC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,iIAAA,CAAA,QAAK;wCACJ,aAAY;wCACZ,WAAU;;;;;;kDAEZ,8OAAC,kIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAW,WAAU;;4CAAQ;0DAE3C,8OAAC,kNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;;;;;;;;;;;;;0CAG1B,8OAAC;gCAAE,WAAU;0CAA6B;;;;;;;;;;;;;;;;;;;;;;0BAQhD,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,UAAU;gCAAE,MAAM;4BAAK;4BACvB,WAAU;;8CAEV,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,0MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;sDAEtB,8OAAC;4CAAK,WAAU;sDAAqB;;;;;;;;;;;;8CAEvC,8OAAC;oCAAE,WAAU;8CAA8B;;;;;;8CAK3C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;8DAChB,8OAAC;oDAAK,WAAU;8DAAU;;;;;;;;;;;;sDAE5B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;8DACjB,8OAAC;oDAAK,WAAU;8DAAU;;;;;;;;;;;;sDAE5B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,0MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,8OAAC;oDAAK,WAAU;8DAAU;;;;;;;;;;;;;;;;;;;;;;;;sCAMhC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,UAAU;gCAAE,MAAM;4BAAK;;8CAEvB,8OAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,8OAAC;oCAAG,WAAU;8CACX,YAAY,OAAO,CAAC,GAAG,CAAC,CAAC,qBACxB,8OAAC;sDACC,cAAA,8OAAC;gDACC,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,IAAI;;;;;;2CALL,KAAK,IAAI;;;;;;;;;;;;;;;;sCAaxB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,UAAU;gCAAE,MAAM;4BAAK;;8CAEvB,8OAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,8OAAC;oCAAG,WAAU;8CACX,YAAY,OAAO,CAAC,GAAG,CAAC,CAAC,qBACxB,8OAAC;sDACC,cAAA,8OAAC;gDACC,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,IAAI;;;;;;2CALL,KAAK,IAAI;;;;;;;;;;;;;;;;sCAaxB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,UAAU;gCAAE,MAAM;4BAAK;;8CAEvB,8OAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,8OAAC;oCAAG,WAAU;8CACX,YAAY,SAAS,CAAC,GAAG,CAAC,CAAC,qBAC1B,8OAAC;sDACC,cAAA,8OAAC;gDACC,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,IAAI;;;;;;2CALL,KAAK,IAAI;;;;;;;;;;;;;;;;sCAaxB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,UAAU;gCAAE,MAAM;4BAAK;;8CAEvB,8OAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,8OAAC;oCAAG,WAAU;8CACX,YAAY,KAAK,CAAC,GAAG,CAAC,CAAC,qBACtB,8OAAC;sDACC,cAAA,8OAAC;gDACC,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,IAAI;;;;;;2CALL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAe5B,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG,CAAC;gCAAG;gCAC9B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,UAAU;gCAAI;gCAC5B,UAAU;oCAAE,MAAM;gCAAK;gCACvB,WAAU;0CACX;;;;;;0CAKD,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,UAAU;gCAAI;gCAC5B,UAAU;oCAAE,MAAM;gCAAK;gCACvB,WAAU;0CAET,YAAY,GAAG,CAAC,CAAC,uBAChB,8OAAC;wCAEC,MAAM,OAAO,IAAI;wCACjB,WAAU;wCACV,cAAY,OAAO,IAAI;kDAEvB,cAAA,8OAAC,OAAO,IAAI;4CAAC,WAAU;;;;;;uCALlB,OAAO,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAclC;uCAEe", "debugId": null}}, {"offset": {"line": 961, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Landing%20page/admybrand-ai-suite/src/components/layout/header.tsx"], "sourcesContent": ["'use client'\n\nimport { But<PERSON> } from '@/components/ui/button'\nimport { AnimatePresence, motion } from 'framer-motion'\nimport { Menu, X } from 'lucide-react'\nimport { useEffect, useState } from 'react'\n\nconst navigationItems = [\n  { name: 'Features', href: '#features' },\n  { name: 'Pricing', href: '#pricing' },\n  { name: 'Testimonials', href: '#testimonials' },\n  { name: 'FAQ', href: '#faq' },\n  { name: 'Contact', href: '#contact' }\n]\n\nconst Header = () => {\n  const [isScrolled, setIsScrolled] = useState(false)\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)\n\n  useEffect(() => {\n    const handleScroll = () => {\n      setIsScrolled(window.scrollY > 50)\n    }\n\n    window.addEventListener('scroll', handleScroll)\n    return () => window.removeEventListener('scroll', handleScroll)\n  }, [])\n\n  const scrollToSection = (href: string) => {\n    const element = document.querySelector(href)\n    if (element) {\n      element.scrollIntoView({ behavior: 'smooth' })\n    }\n    setIsMobileMenuOpen(false)\n  }\n\n  return (\n    <>\n      <motion.header\n        initial={{ y: -100 }}\n        animate={{ y: 0 }}\n        transition={{ duration: 0.6 }}\n        className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${\n          isScrolled\n            ? 'bg-black/90 backdrop-blur-md shadow-lg border-b border-gray-800'\n            : 'bg-black/50 backdrop-blur-sm'\n        }`}\n      >\n        <div className=\"w-full px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex items-center justify-between h-16 lg:h-20\">\n            {/* Logo */}\n            <motion.div\n              whileHover={{ scale: 1.05 }}\n              className=\"flex items-center cursor-pointer\"\n              onClick={() => window.scrollTo({ top: 0, behavior: 'smooth' })}\n            >\n              <div className=\"flex items-center gap-2\">\n                <div className=\"w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg flex items-center justify-center\">\n                  <span className=\"text-white font-bold text-sm\">A</span>\n                </div>\n                <span className=\"text-xl font-bold bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent\">\n                  ADmyBRAND\n                </span>\n              </div>\n            </motion.div>\n\n            {/* Desktop Navigation */}\n            <nav className=\"hidden lg:flex items-center space-x-8\">\n              {navigationItems.map((item) => (\n                <motion.button\n                  key={item.name}\n                  onClick={() => scrollToSection(item.href)}\n                  className=\"relative text-sm font-medium text-gray-300 hover:text-white transition-colors duration-200 px-3 py-2 rounded-lg hover:bg-white/5\"\n                  whileHover={{ y: -2 }}\n                  whileTap={{ y: 0 }}\n                >\n                  {item.name}\n                  <motion.div\n                    className=\"absolute bottom-0 left-0 right-0 h-0.5 bg-gradient-to-r from-blue-400 to-purple-400 rounded-full\"\n                    initial={{ scaleX: 0 }}\n                    whileHover={{ scaleX: 1 }}\n                    transition={{ duration: 0.2 }}\n                  />\n                </motion.button>\n              ))}\n            </nav>\n\n            {/* Desktop CTA Button */}\n            <div className=\"hidden lg:flex items-center\">\n              <Button className=\"bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-6 py-2 rounded-xl font-medium shadow-lg hover:shadow-blue-500/25 transition-all duration-300 border border-white/10\">\n                BOOK A DEMO\n              </Button>\n            </div>\n\n            {/* Mobile Menu Button */}\n            <motion.button\n              whileTap={{ scale: 0.95 }}\n              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}\n              className=\"lg:hidden p-2 rounded-lg text-white hover:bg-white/10 transition-colors duration-200\"\n            >\n              {isMobileMenuOpen ? (\n                <X className=\"w-6 h-6\" />\n              ) : (\n                <Menu className=\"w-6 h-6\" />\n              )}\n            </motion.button>\n          </div>\n        </div>\n      </motion.header>\n\n      {/* Mobile Menu */}\n      <AnimatePresence>\n        {isMobileMenuOpen && (\n          <motion.div\n            initial={{ opacity: 0, height: 0 }}\n            animate={{ opacity: 1, height: 'auto' }}\n            exit={{ opacity: 0, height: 0 }}\n            transition={{ duration: 0.3 }}\n            className=\"fixed top-16 lg:top-20 left-0 right-0 z-40 bg-black/95 backdrop-blur-md border-b border-gray-800 lg:hidden\"\n          >\n            <div className=\"max-w-5xl mx-auto px-4 sm:px-6 py-6\">\n              <nav className=\"space-y-4\">\n                {navigationItems.map((item, index) => (\n                  <motion.button\n                    key={item.name}\n                    initial={{ opacity: 0, x: -20 }}\n                    animate={{ opacity: 1, x: 0 }}\n                    transition={{ duration: 0.3, delay: index * 0.1 }}\n                    onClick={() => scrollToSection(item.href)}\n                    className=\"block w-full text-left text-lg font-medium text-gray-300 hover:text-white transition-colors duration-200 py-2\"\n                  >\n                    {item.name}\n                  </motion.button>\n                ))}\n              </nav>\n              \n              <div className=\"mt-6 pt-6 border-t border-gray-800\">\n                <Button className=\"w-full justify-center bg-blue-600 hover:bg-blue-700 text-white\">\n                  BOOK A DEMO\n                </Button>\n              </div>\n            </div>\n          </motion.div>\n        )}\n      </AnimatePresence>\n\n      {/* Overlay for mobile menu */}\n      <AnimatePresence>\n        {isMobileMenuOpen && (\n          <motion.div\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            exit={{ opacity: 0 }}\n            transition={{ duration: 0.3 }}\n            className=\"fixed inset-0 bg-black/20 backdrop-blur-sm z-30 lg:hidden\"\n            onClick={() => setIsMobileMenuOpen(false)}\n          />\n        )}\n      </AnimatePresence>\n    </>\n  )\n}\n\nexport default Header\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AACA;AALA;;;;;;AAOA,MAAM,kBAAkB;IACtB;QAAE,MAAM;QAAY,MAAM;IAAY;IACtC;QAAE,MAAM;QAAW,MAAM;IAAW;IACpC;QAAE,MAAM;QAAgB,MAAM;IAAgB;IAC9C;QAAE,MAAM;QAAO,MAAM;IAAO;IAC5B;QAAE,MAAM;QAAW,MAAM;IAAW;CACrC;AAED,MAAM,SAAS;IACb,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe;YACnB,cAAc,OAAO,OAAO,GAAG;QACjC;QAEA,OAAO,gBAAgB,CAAC,UAAU;QAClC,OAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;IACpD,GAAG,EAAE;IAEL,MAAM,kBAAkB,CAAC;QACvB,MAAM,UAAU,SAAS,aAAa,CAAC;QACvC,IAAI,SAAS;YACX,QAAQ,cAAc,CAAC;gBAAE,UAAU;YAAS;QAC9C;QACA,oBAAoB;IACtB;IAEA,qBACE;;0BACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;gBACZ,SAAS;oBAAE,GAAG,CAAC;gBAAI;gBACnB,SAAS;oBAAE,GAAG;gBAAE;gBAChB,YAAY;oBAAE,UAAU;gBAAI;gBAC5B,WAAW,CAAC,4DAA4D,EACtE,aACI,oEACA,gCACJ;0BAEF,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,YAAY;oCAAE,OAAO;gCAAK;gCAC1B,WAAU;gCACV,SAAS,IAAM,OAAO,QAAQ,CAAC;wCAAE,KAAK;wCAAG,UAAU;oCAAS;0CAE5D,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DAA+B;;;;;;;;;;;sDAEjD,8OAAC;4CAAK,WAAU;sDAA0F;;;;;;;;;;;;;;;;;0CAO9G,8OAAC;gCAAI,WAAU;0CACZ,gBAAgB,GAAG,CAAC,CAAC,qBACpB,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;wCAEZ,SAAS,IAAM,gBAAgB,KAAK,IAAI;wCACxC,WAAU;wCACV,YAAY;4CAAE,GAAG,CAAC;wCAAE;wCACpB,UAAU;4CAAE,GAAG;wCAAE;;4CAEhB,KAAK,IAAI;0DACV,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,WAAU;gDACV,SAAS;oDAAE,QAAQ;gDAAE;gDACrB,YAAY;oDAAE,QAAQ;gDAAE;gDACxB,YAAY;oDAAE,UAAU;gDAAI;;;;;;;uCAXzB,KAAK,IAAI;;;;;;;;;;0CAkBpB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;oCAAC,WAAU;8CAAyN;;;;;;;;;;;0CAM7O,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;gCACZ,UAAU;oCAAE,OAAO;gCAAK;gCACxB,SAAS,IAAM,oBAAoB,CAAC;gCACpC,WAAU;0CAET,iCACC,8OAAC,4LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;6FAEb,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQ1B,8OAAC,yLAAA,CAAA,kBAAe;0BACb,kCACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,QAAQ;oBAAE;oBACjC,SAAS;wBAAE,SAAS;wBAAG,QAAQ;oBAAO;oBACtC,MAAM;wBAAE,SAAS;wBAAG,QAAQ;oBAAE;oBAC9B,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,WAAU;8BAEV,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACZ,gBAAgB,GAAG,CAAC,CAAC,MAAM,sBAC1B,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;wCAEZ,SAAS;4CAAE,SAAS;4CAAG,GAAG,CAAC;wCAAG;wCAC9B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,UAAU;4CAAK,OAAO,QAAQ;wCAAI;wCAChD,SAAS,IAAM,gBAAgB,KAAK,IAAI;wCACxC,WAAU;kDAET,KAAK,IAAI;uCAPL,KAAK,IAAI;;;;;;;;;;0CAYpB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;oCAAC,WAAU;8CAAiE;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAU7F,8OAAC,yLAAA,CAAA,kBAAe;0BACb,kCACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;oBAAE;oBACtB,SAAS;wBAAE,SAAS;oBAAE;oBACtB,MAAM;wBAAE,SAAS;oBAAE;oBACnB,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,WAAU;oBACV,SAAS,IAAM,oBAAoB;;;;;;;;;;;;;AAM/C;uCAEe", "debugId": null}}, {"offset": {"line": 1291, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Landing%20page/admybrand-ai-suite/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        destructive:\n          \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\n        outline: \"text-foreground\",\n        success:\n          \"border-transparent bg-green-500 text-white hover:bg-green-600\",\n        warning:\n          \"border-transparent bg-yellow-500 text-white hover:bg-yellow-600\",\n        info:\n          \"border-transparent bg-blue-500 text-white hover:bg-blue-600\",\n        gradient:\n          \"border-transparent bg-gradient-to-r from-purple-500 to-blue-500 text-white\",\n        glass:\n          \"glass border-white/20 text-white backdrop-blur-md\",\n      },\n      size: {\n        default: \"px-2.5 py-0.5 text-xs\",\n        sm: \"px-2 py-0.5 text-xs\",\n        lg: \"px-3 py-1 text-sm\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface BadgeProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof badgeVariants> {}\n\nfunction Badge({ className, variant, size, ...props }: BadgeProps) {\n  return (\n    <div className={cn(badgeVariants({ variant, size }), className)} {...props} />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,0KACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SAAS;YACT,SACE;YACF,SACE;YACF,MACE;YACF,UACE;YACF,OACE;QACJ;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;QACN;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAOF,SAAS,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,GAAG,OAAmB;IAC/D,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;YAAS;QAAK,IAAI;QAAa,GAAG,KAAK;;;;;;AAE9E", "debugId": null}}, {"offset": {"line": 1343, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Landing%20page/admybrand-ai-suite/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst cardVariants = cva(\n  \"rounded-lg border bg-card text-card-foreground shadow-sm\",\n  {\n    variants: {\n      variant: {\n        default: \"border-border\",\n        glass: \"glass border-white/20 backdrop-blur-md\",\n        gradient: \"bg-gradient-to-br from-white to-gray-50 border-gray-200\",\n        elevated: \"shadow-lg hover:shadow-xl transition-shadow duration-300\",\n        interactive: \"hover:shadow-md transition-all duration-200 cursor-pointer hover:scale-[1.02]\",\n      },\n      padding: {\n        none: \"p-0\",\n        sm: \"p-4\",\n        default: \"p-6\",\n        lg: \"p-8\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      padding: \"default\",\n    },\n  }\n)\n\nexport interface CardProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof cardVariants> {}\n\nconst Card = React.forwardRef<HTMLDivElement, CardProps>(\n  ({ className, variant, padding, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn(cardVariants({ variant, padding, className }))}\n      {...props}\n    />\n  )\n)\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,eAAe,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACrB,4DACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,OAAO;YACP,UAAU;YACV,UAAU;YACV,aAAa;QACf;QACA,SAAS;YACP,MAAM;YACN,IAAI;YACJ,SAAS;YACT,IAAI;QACN;IACF;IACA,iBAAiB;QACf,SAAS;QACT,SAAS;IACX;AACF;AAOF,MAAM,qBAAO,qMAAA,CAAA,aAAgB,CAC3B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,OAAO,EAAE,oBAC1C,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,aAAa;YAAE;YAAS;YAAS;QAAU;QACxD,GAAG,KAAK;;;;;;AAIf,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,qMAAA,CAAA,aAAgB,CAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,qMAAA,CAAA,aAAgB,CAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,qMAAA,CAAA,aAAgB,CAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,qMAAA,CAAA,aAAgB,CAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,qMAAA,CAAA,aAAgB,CAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 1449, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Landing%20page/admybrand-ai-suite/src/components/sections/contact.tsx"], "sourcesContent": ["'use client'\n\nimport { Badge } from '@/components/ui/badge'\nimport { Button } from '@/components/ui/button'\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Input } from '@/components/ui/input'\nimport { motion } from 'framer-motion'\nimport { CheckCircle, Mail, MapPin, Phone, Send } from 'lucide-react'\nimport { useState } from 'react'\n\ninterface FormData {\n  name: string\n  email: string\n  company: string\n  message: string\n}\n\ninterface FormErrors {\n  name?: string\n  email?: string\n  company?: string\n  message?: string\n}\n\nconst Contact = () => {\n  const [formData, setFormData] = useState<FormData>({\n    name: '',\n    email: '',\n    company: '',\n    message: ''\n  })\n  const [errors, setErrors] = useState<FormErrors>({})\n  const [isSubmitting, setIsSubmitting] = useState(false)\n  const [isSubmitted, setIsSubmitted] = useState(false)\n\n  const validateForm = (): boolean => {\n    const newErrors: FormErrors = {}\n\n    if (!formData.name.trim()) {\n      newErrors.name = 'Name is required'\n    }\n\n    if (!formData.email.trim()) {\n      newErrors.email = 'Email is required'\n    } else if (!/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(formData.email)) {\n      newErrors.email = 'Please enter a valid email address'\n    }\n\n    if (!formData.company.trim()) {\n      newErrors.company = 'Company is required'\n    }\n\n    if (!formData.message.trim()) {\n      newErrors.message = 'Message is required'\n    } else if (formData.message.trim().length < 10) {\n      newErrors.message = 'Message must be at least 10 characters long'\n    }\n\n    setErrors(newErrors)\n    return Object.keys(newErrors).length === 0\n  }\n\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {\n    const { name, value } = e.target\n    setFormData(prev => ({ ...prev, [name]: value }))\n    \n    // Clear error when user starts typing\n    if (errors[name as keyof FormErrors]) {\n      setErrors(prev => ({ ...prev, [name]: undefined }))\n    }\n  }\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n    \n    if (!validateForm()) return\n\n    setIsSubmitting(true)\n\n    // Simulate API call\n    try {\n      await new Promise(resolve => setTimeout(resolve, 2000))\n      setIsSubmitted(true)\n      setFormData({ name: '', email: '', company: '', message: '' })\n    } catch (error) {\n      console.error('Error submitting form:', error)\n    } finally {\n      setIsSubmitting(false)\n    }\n  }\n\n  if (isSubmitted) {\n    return (\n      <section className=\"py-24 bg-black\">\n        <div className=\"max-w-6xl mx-auto px-2 sm:px-4 lg:px-6\">\n          <motion.div\n            initial={{ opacity: 0, scale: 0.8 }}\n            animate={{ opacity: 1, scale: 1 }}\n            transition={{ duration: 0.6 }}\n            className=\"text-center\"\n          >\n            <div className=\"w-20 h-20 bg-green-500/20 backdrop-blur-sm border border-green-500/30 rounded-full flex items-center justify-center mx-auto mb-6\">\n              <CheckCircle className=\"w-10 h-10 text-green-400\" />\n            </div>\n            <h2 className=\"text-3xl font-bold text-white mb-4\">\n              Thank you for reaching out!\n            </h2>\n            <p className=\"text-xl text-gray-300 mb-8\">\n              We've received your message and will get back to you within 24 hours.\n            </p>\n            <Button\n              onClick={() => setIsSubmitted(false)}\n              className=\"bg-white/10 hover:bg-white/20 text-white border-white/20\"\n            >\n              Send Another Message\n            </Button>\n          </motion.div>\n        </div>\n      </section>\n    )\n  }\n\n  return (\n    <section className=\"py-24 bg-black\">\n      <div className=\"max-w-7xl mx-auto px-2 sm:px-4 lg:px-6\">\n        {/* Section Header */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          viewport={{ once: true }}\n          className=\"text-center mb-16\"\n        >\n          <Badge className=\"mb-4 bg-white/10 text-white border-white/20 backdrop-blur-sm\">\n            Get in Touch\n          </Badge>\n          <h2 className=\"text-4xl md:text-5xl font-bold text-white mb-6\">\n            Ready to transform your\n            <span className=\"bg-gradient-to-r from-blue-400 to-cyan-400 bg-clip-text text-transparent\"> marketing?</span>\n          </h2>\n          <p className=\"text-xl text-gray-300 max-w-3xl mx-auto\">\n            Let's discuss how ADmyBRAND AI Suite can help you achieve your marketing goals.\n            Our team is here to answer your questions and provide a personalized demo.\n          </p>\n        </motion.div>\n\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-12\">\n          {/* Contact Form */}\n          <motion.div\n            initial={{ opacity: 0, x: -30 }}\n            whileInView={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.8 }}\n            viewport={{ once: true }}\n          >\n            <Card className=\"shadow-xl border border-white/10 bg-white/5 backdrop-blur-md\">\n              <CardHeader>\n                <CardTitle className=\"text-2xl font-bold text-white\">\n                  Send us a message\n                </CardTitle>\n              </CardHeader>\n              <CardContent>\n                <form onSubmit={handleSubmit} className=\"space-y-6\">\n                  <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                    <Input\n                      label=\"Full Name\"\n                      name=\"name\"\n                      value={formData.name}\n                      onChange={handleInputChange}\n                      error={errors.name}\n                      placeholder=\"John Doe\"\n                    />\n                    <Input\n                      label=\"Email Address\"\n                      name=\"email\"\n                      type=\"email\"\n                      value={formData.email}\n                      onChange={handleInputChange}\n                      error={errors.email}\n                      placeholder=\"<EMAIL>\"\n                    />\n                  </div>\n                  \n                  <Input\n                    label=\"Company\"\n                    name=\"company\"\n                    value={formData.company}\n                    onChange={handleInputChange}\n                    error={errors.company}\n                    placeholder=\"Your Company Name\"\n                  />\n\n                  <div>\n                    <label className=\"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 mb-2 block\">\n                      Message\n                    </label>\n                    <textarea\n                      name=\"message\"\n                      value={formData.message}\n                      onChange={handleInputChange}\n                      placeholder=\"Tell us about your marketing goals and how we can help...\"\n                      rows={5}\n                      className={`flex w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 resize-none ${\n                        errors.message ? 'border-destructive focus-visible:ring-destructive' : ''\n                      }`}\n                    />\n                    {errors.message && (\n                      <p className=\"text-sm text-destructive mt-1\">{errors.message}</p>\n                    )}\n                  </div>\n\n                  <Button\n                    type=\"submit\"\n                    className=\"w-full\"\n                    variant=\"gradient\"\n                    loading={isSubmitting}\n                    disabled={isSubmitting}\n                  >\n                    {isSubmitting ? 'Sending...' : 'Send Message'}\n                    <Send className=\"ml-2 h-4 w-4\" />\n                  </Button>\n                </form>\n              </CardContent>\n            </Card>\n          </motion.div>\n\n          {/* Contact Information */}\n          <motion.div\n            initial={{ opacity: 0, x: 30 }}\n            whileInView={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.8 }}\n            viewport={{ once: true }}\n            className=\"space-y-8\"\n          >\n            <div>\n              <h3 className=\"text-2xl font-bold text-white mb-6\">\n                Get in touch\n              </h3>\n              <p className=\"text-gray-300 mb-8\">\n                We're here to help you succeed. Reach out to us through any of these channels,\n                and we'll respond as quickly as possible.\n              </p>\n            </div>\n\n            <div className=\"space-y-6\">\n              <div className=\"flex items-start space-x-4\">\n                <div className=\"w-12 h-12 bg-purple-500/20 backdrop-blur-sm border border-purple-500/30 rounded-lg flex items-center justify-center flex-shrink-0\">\n                  <Mail className=\"w-6 h-6 text-purple-400\" />\n                </div>\n                <div>\n                  <h4 className=\"font-semibold text-white mb-1\">Email us</h4>\n                  <p className=\"text-blue-400\"><EMAIL></p>\n                  <p className=\"text-sm text-gray-400\">We'll respond within 24 hours</p>\n                </div>\n              </div>\n\n              <div className=\"flex items-start space-x-4\">\n                <div className=\"w-12 h-12 bg-blue-500/20 backdrop-blur-sm border border-blue-500/30 rounded-lg flex items-center justify-center flex-shrink-0\">\n                  <Phone className=\"w-6 h-6 text-blue-400\" />\n                </div>\n                <div>\n                  <h4 className=\"font-semibold text-white mb-1\">Call us</h4>\n                  <p className=\"text-blue-400\">+****************</p>\n                  <p className=\"text-sm text-gray-400\">Mon-Fri 9am-6pm PST</p>\n                </div>\n              </div>\n\n              <div className=\"flex items-start space-x-4\">\n                <div className=\"w-12 h-12 bg-green-500/20 backdrop-blur-sm border border-green-500/30 rounded-lg flex items-center justify-center flex-shrink-0\">\n                  <MapPin className=\"w-6 h-6 text-green-400\" />\n                </div>\n                <div>\n                  <h4 className=\"font-semibold text-white mb-1\">Visit us</h4>\n                  <p className=\"text-gray-300\">\n                    123 Innovation Drive<br />\n                    San Francisco, CA 94105\n                  </p>\n                </div>\n              </div>\n            </div>\n\n            {/* Quick Stats */}\n            <div className=\"bg-white/5 backdrop-blur-xl border border-white/10 rounded-xl p-6 mt-8\">\n              <h4 className=\"font-semibold text-white mb-4\">Why choose ADmyBRAND?</h4>\n              <div className=\"grid grid-cols-2 gap-4\">\n                <div className=\"text-center\">\n                  <div className=\"text-2xl font-bold text-purple-400\">24/7</div>\n                  <div className=\"text-sm text-gray-400\">Support</div>\n                </div>\n                <div className=\"text-center\">\n                  <div className=\"text-2xl font-bold text-blue-400\">99.9%</div>\n                  <div className=\"text-sm text-gray-400\">Uptime</div>\n                </div>\n                <div className=\"text-center\">\n                  <div className=\"text-2xl font-bold text-green-400\">50K+</div>\n                  <div className=\"text-sm text-gray-400\">Customers</div>\n                </div>\n                <div className=\"text-center\">\n                  <div className=\"text-2xl font-bold text-orange-400\">5★</div>\n                  <div className=\"text-sm text-gray-400\">Rating</div>\n                </div>\n              </div>\n            </div>\n          </motion.div>\n        </div>\n      </div>\n    </section>\n  )\n}\n\nexport default Contact\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AARA;;;;;;;;;AAwBA,MAAM,UAAU;IACd,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY;QACjD,MAAM;QACN,OAAO;QACP,SAAS;QACT,SAAS;IACX;IACA,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc,CAAC;IAClD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,eAAe;QACnB,MAAM,YAAwB,CAAC;QAE/B,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,IAAI;YACzB,UAAU,IAAI,GAAG;QACnB;QAEA,IAAI,CAAC,SAAS,KAAK,CAAC,IAAI,IAAI;YAC1B,UAAU,KAAK,GAAG;QACpB,OAAO,IAAI,CAAC,6BAA6B,IAAI,CAAC,SAAS,KAAK,GAAG;YAC7D,UAAU,KAAK,GAAG;QACpB;QAEA,IAAI,CAAC,SAAS,OAAO,CAAC,IAAI,IAAI;YAC5B,UAAU,OAAO,GAAG;QACtB;QAEA,IAAI,CAAC,SAAS,OAAO,CAAC,IAAI,IAAI;YAC5B,UAAU,OAAO,GAAG;QACtB,OAAO,IAAI,SAAS,OAAO,CAAC,IAAI,GAAG,MAAM,GAAG,IAAI;YAC9C,UAAU,OAAO,GAAG;QACtB;QAEA,UAAU;QACV,OAAO,OAAO,IAAI,CAAC,WAAW,MAAM,KAAK;IAC3C;IAEA,MAAM,oBAAoB,CAAC;QACzB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;QAChC,YAAY,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,KAAK,EAAE;YAAM,CAAC;QAE/C,sCAAsC;QACtC,IAAI,MAAM,CAAC,KAAyB,EAAE;YACpC,UAAU,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,CAAC,KAAK,EAAE;gBAAU,CAAC;QACnD;IACF;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,CAAC,gBAAgB;QAErB,gBAAgB;QAEhB,oBAAoB;QACpB,IAAI;YACF,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YACjD,eAAe;YACf,YAAY;gBAAE,MAAM;gBAAI,OAAO;gBAAI,SAAS;gBAAI,SAAS;YAAG;QAC9D,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;QAC1C,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,IAAI,aAAa;QACf,qBACE,8OAAC;YAAQ,WAAU;sBACjB,cAAA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,OAAO;oBAAI;oBAClC,SAAS;wBAAE,SAAS;wBAAG,OAAO;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,WAAU;;sCAEV,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,2NAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;;;;;;sCAEzB,8OAAC;4BAAG,WAAU;sCAAqC;;;;;;sCAGnD,8OAAC;4BAAE,WAAU;sCAA6B;;;;;;sCAG1C,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAS,IAAM,eAAe;4BAC9B,WAAU;sCACX;;;;;;;;;;;;;;;;;;;;;;IAOX;IAEA,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAEV,8OAAC,iIAAA,CAAA,QAAK;4BAAC,WAAU;sCAA+D;;;;;;sCAGhF,8OAAC;4BAAG,WAAU;;gCAAiD;8CAE7D,8OAAC;oCAAK,WAAU;8CAA2E;;;;;;;;;;;;sCAE7F,8OAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;8BAMzD,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG,CAAC;4BAAG;4BAC9B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,UAAU;gCAAE,MAAM;4BAAK;sCAEvB,cAAA,8OAAC,gIAAA,CAAA,OAAI;gCAAC,WAAU;;kDACd,8OAAC,gIAAA,CAAA,aAAU;kDACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;4CAAC,WAAU;sDAAgC;;;;;;;;;;;kDAIvD,8OAAC,gIAAA,CAAA,cAAW;kDACV,cAAA,8OAAC;4CAAK,UAAU;4CAAc,WAAU;;8DACtC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,iIAAA,CAAA,QAAK;4DACJ,OAAM;4DACN,MAAK;4DACL,OAAO,SAAS,IAAI;4DACpB,UAAU;4DACV,OAAO,OAAO,IAAI;4DAClB,aAAY;;;;;;sEAEd,8OAAC,iIAAA,CAAA,QAAK;4DACJ,OAAM;4DACN,MAAK;4DACL,MAAK;4DACL,OAAO,SAAS,KAAK;4DACrB,UAAU;4DACV,OAAO,OAAO,KAAK;4DACnB,aAAY;;;;;;;;;;;;8DAIhB,8OAAC,iIAAA,CAAA,QAAK;oDACJ,OAAM;oDACN,MAAK;oDACL,OAAO,SAAS,OAAO;oDACvB,UAAU;oDACV,OAAO,OAAO,OAAO;oDACrB,aAAY;;;;;;8DAGd,8OAAC;;sEACC,8OAAC;4DAAM,WAAU;sEAAwG;;;;;;sEAGzH,8OAAC;4DACC,MAAK;4DACL,OAAO,SAAS,OAAO;4DACvB,UAAU;4DACV,aAAY;4DACZ,MAAM;4DACN,WAAW,CAAC,oSAAoS,EAC9S,OAAO,OAAO,GAAG,sDAAsD,IACvE;;;;;;wDAEH,OAAO,OAAO,kBACb,8OAAC;4DAAE,WAAU;sEAAiC,OAAO,OAAO;;;;;;;;;;;;8DAIhE,8OAAC,kIAAA,CAAA,SAAM;oDACL,MAAK;oDACL,WAAU;oDACV,SAAQ;oDACR,SAAS;oDACT,UAAU;;wDAET,eAAe,eAAe;sEAC/B,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQ1B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,UAAU;gCAAE,MAAM;4BAAK;4BACvB,WAAU;;8CAEV,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAAqC;;;;;;sDAGnD,8OAAC;4CAAE,WAAU;sDAAqB;;;;;;;;;;;;8CAMpC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;;;;;;8DAElB,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAAgC;;;;;;sEAC9C,8OAAC;4DAAE,WAAU;sEAAgB;;;;;;sEAC7B,8OAAC;4DAAE,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;sDAIzC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;;;;;;8DAEnB,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAAgC;;;;;;sEAC9C,8OAAC;4DAAE,WAAU;sEAAgB;;;;;;sEAC7B,8OAAC;4DAAE,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;sDAIzC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,0MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;;;;;;8DAEpB,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAAgC;;;;;;sEAC9C,8OAAC;4DAAE,WAAU;;gEAAgB;8EACP,8OAAC;;;;;gEAAK;;;;;;;;;;;;;;;;;;;;;;;;;8CAQlC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAgC;;;;;;sDAC9C,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEAAqC;;;;;;sEACpD,8OAAC;4DAAI,WAAU;sEAAwB;;;;;;;;;;;;8DAEzC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEAAmC;;;;;;sEAClD,8OAAC;4DAAI,WAAU;sEAAwB;;;;;;;;;;;;8DAEzC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEAAoC;;;;;;sEACnD,8OAAC;4DAAI,WAAU;sEAAwB;;;;;;;;;;;;8DAEzC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEAAqC;;;;;;sEACpD,8OAAC;4DAAI,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASzD;uCAEe", "debugId": null}}, {"offset": {"line": 2210, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Landing%20page/admybrand-ai-suite/src/components/sections/faq.tsx"], "sourcesContent": ["'use client'\n\nimport { Badge } from '@/components/ui/badge'\nimport { Card, CardContent } from '@/components/ui/card'\nimport { AnimatePresence, motion } from 'framer-motion'\nimport { ChevronDown, HelpCircle } from 'lucide-react'\nimport { useState } from 'react'\n\nconst faqs = [\n  {\n    question: \"How does ADmyBRAND AI understand my brand voice?\",\n    answer: \"Our AI analyzes your existing content, brand guidelines, and communication style to create a unique brand voice profile. It learns from your tone, vocabulary, messaging patterns, and values to ensure all generated content maintains consistency with your brand identity.\"\n  },\n  {\n    question: \"Can I integrate ADmyBRAND with my existing marketing tools?\",\n    answer: \"Yes! ADmyBRAND integrates with over 100+ popular marketing tools including HubSpot, Salesforce, Mailchimp, Hootsuite, Google Analytics, Facebook Ads Manager, and many more. We also provide API access for custom integrations.\"\n  },\n  {\n    question: \"What kind of content can the AI generate?\",\n    answer: \"Our AI can create blog posts, social media content, email campaigns, ad copy, product descriptions, press releases, video scripts, landing page copy, and much more. It adapts to different formats, lengths, and platforms while maintaining your brand voice.\"\n  },\n  {\n    question: \"How accurate is the AI-generated content?\",\n    answer: \"Our AI achieves 95%+ accuracy in brand voice matching and content quality. All content goes through multiple AI validation layers, and you always have full editorial control. The AI learns and improves from your feedback and edits.\"\n  },\n  {\n    question: \"Is my data secure with ADmyBRAND?\",\n    answer: \"Absolutely. We use enterprise-grade security with SOC 2 Type II compliance, end-to-end encryption, and GDPR compliance. Your data is never used to train models for other customers, and you maintain full ownership of all content and data.\"\n  },\n  {\n    question: \"Can I cancel my subscription anytime?\",\n    answer: \"Yes, you can cancel your subscription at any time with no cancellation fees. Your account will remain active until the end of your current billing period, and you'll retain access to all your generated content and data.\"\n  },\n  {\n    question: \"Do you offer custom AI training for enterprise clients?\",\n    answer: \"Yes! Our Enterprise plan includes custom AI model training specifically for your brand, industry, and use cases. We can train the AI on your proprietary data, create custom workflows, and provide dedicated support for implementation.\"\n  },\n  {\n    question: \"How quickly can I see results with ADmyBRAND?\",\n    answer: \"Most customers see immediate improvements in content creation speed (80% faster) and start seeing engagement improvements within the first week. Full ROI optimization typically occurs within 30-60 days as the AI learns and optimizes your campaigns.\"\n  }\n]\n\nconst FAQ = () => {\n  const [openIndex, setOpenIndex] = useState<number | null>(0)\n\n  const toggleFAQ = (index: number) => {\n    setOpenIndex(openIndex === index ? null : index)\n  }\n\n  return (\n    <section className=\"py-24 bg-black\">\n      <div className=\"max-w-6xl mx-auto px-2 sm:px-4 lg:px-6\">\n        {/* Section Header */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          viewport={{ once: true }}\n          className=\"text-center mb-16\"\n        >\n          <Badge className=\"mb-4 bg-white/10 text-white border-white/20 backdrop-blur-sm\">\n            <HelpCircle className=\"w-4 h-4 mr-2\" />\n            FAQ\n          </Badge>\n          <h2 className=\"text-4xl md:text-5xl font-bold text-white mb-6\">\n            Frequently asked\n            <span className=\"bg-gradient-to-r from-blue-400 to-cyan-400 bg-clip-text text-transparent\"> questions</span>\n          </h2>\n          <p className=\"text-xl text-gray-300 max-w-2xl mx-auto\">\n            Everything you need to know about ADmyBRAND AI Suite. Can't find the answer you're looking for?\n            <span className=\"text-blue-400 font-medium\"> Contact our support team.</span>\n          </p>\n        </motion.div>\n\n        {/* FAQ Items - Compact Grid Layout */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-3\">\n          {faqs.map((faq, index) => (\n            <motion.div\n              key={index}\n              initial={{ opacity: 0, y: 20 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.5, delay: index * 0.05 }}\n              viewport={{ once: true }}\n            >\n              <Card className=\"border border-white/10 hover:border-white/20 transition-colors duration-300 bg-white/5 backdrop-blur-md hover:bg-white/10 h-full\">\n                <CardContent className=\"p-0\">\n                  <button\n                    onClick={() => toggleFAQ(index)}\n                    className=\"w-full text-left p-4 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-inset\"\n                  >\n                    <div className=\"flex items-center justify-between\">\n                      <h3 className=\"text-sm font-semibold text-white pr-3 leading-tight\">\n                        {faq.question}\n                      </h3>\n                      <motion.div\n                        animate={{ rotate: openIndex === index ? 180 : 0 }}\n                        transition={{ duration: 0.3 }}\n                        className=\"flex-shrink-0\"\n                      >\n                        <ChevronDown className=\"w-4 h-4 text-gray-400\" />\n                      </motion.div>\n                    </div>\n                  </button>\n\n                  <AnimatePresence>\n                    {openIndex === index && (\n                      <motion.div\n                        initial={{ height: 0, opacity: 0 }}\n                        animate={{ height: \"auto\", opacity: 1 }}\n                        exit={{ height: 0, opacity: 0 }}\n                        transition={{ duration: 0.3 }}\n                        className=\"overflow-hidden\"\n                      >\n                        <div className=\"px-4 pb-4\">\n                          <p className=\"text-gray-300 leading-relaxed text-sm\">\n                            {faq.answer}\n                          </p>\n                        </div>\n                      </motion.div>\n                    )}\n                  </AnimatePresence>\n                </CardContent>\n              </Card>\n            </motion.div>\n          ))}\n        </div>\n\n        {/* Bottom CTA */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8, delay: 0.4 }}\n          viewport={{ once: true }}\n          className=\"text-center mt-16\"\n        >\n          <div className=\"bg-gradient-to-r from-purple-50 to-blue-50 rounded-2xl p-8\">\n            <h3 className=\"text-2xl font-bold text-gray-900 mb-4\">\n              Still have questions?\n            </h3>\n            <p className=\"text-gray-600 mb-6\">\n              Our support team is available 24/7 to help you get the most out of ADmyBRAND AI Suite.\n            </p>\n            <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n              <motion.button\n                whileHover={{ scale: 1.05 }}\n                whileTap={{ scale: 0.95 }}\n                className=\"px-6 py-3 bg-gradient-to-r from-purple-600 to-blue-600 text-white rounded-lg font-medium shadow-lg hover:shadow-xl transition-all duration-300\"\n              >\n                Contact Support\n              </motion.button>\n              <motion.button\n                whileHover={{ scale: 1.05 }}\n                whileTap={{ scale: 0.95 }}\n                className=\"px-6 py-3 border border-gray-300 text-gray-700 rounded-lg font-medium hover:bg-gray-50 transition-all duration-300\"\n              >\n                Schedule Demo\n              </motion.button>\n            </div>\n          </div>\n        </motion.div>\n      </div>\n    </section>\n  )\n}\n\nexport default FAQ\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AACA;AAAA;AACA;AANA;;;;;;;AAQA,MAAM,OAAO;IACX;QACE,UAAU;QACV,QAAQ;IACV;IACA;QACE,UAAU;QACV,QAAQ;IACV;IACA;QACE,UAAU;QACV,QAAQ;IACV;IACA;QACE,UAAU;QACV,QAAQ;IACV;IACA;QACE,UAAU;QACV,QAAQ;IACV;IACA;QACE,UAAU;QACV,QAAQ;IACV;IACA;QACE,UAAU;QACV,QAAQ;IACV;IACA;QACE,UAAU;QACV,QAAQ;IACV;CACD;AAED,MAAM,MAAM;IACV,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAE1D,MAAM,YAAY,CAAC;QACjB,aAAa,cAAc,QAAQ,OAAO;IAC5C;IAEA,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAEV,8OAAC,iIAAA,CAAA,QAAK;4BAAC,WAAU;;8CACf,8OAAC,8NAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;sCAGzC,8OAAC;4BAAG,WAAU;;gCAAiD;8CAE7D,8OAAC;oCAAK,WAAU;8CAA2E;;;;;;;;;;;;sCAE7F,8OAAC;4BAAE,WAAU;;gCAA0C;8CAErD,8OAAC;oCAAK,WAAU;8CAA4B;;;;;;;;;;;;;;;;;;8BAKhD,8OAAC;oBAAI,WAAU;8BACZ,KAAK,GAAG,CAAC,CAAC,KAAK,sBACd,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO,QAAQ;4BAAK;4BACjD,UAAU;gCAAE,MAAM;4BAAK;sCAEvB,cAAA,8OAAC,gIAAA,CAAA,OAAI;gCAAC,WAAU;0CACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;oCAAC,WAAU;;sDACrB,8OAAC;4CACC,SAAS,IAAM,UAAU;4CACzB,WAAU;sDAEV,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEACX,IAAI,QAAQ;;;;;;kEAEf,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wDACT,SAAS;4DAAE,QAAQ,cAAc,QAAQ,MAAM;wDAAE;wDACjD,YAAY;4DAAE,UAAU;wDAAI;wDAC5B,WAAU;kEAEV,cAAA,8OAAC,oNAAA,CAAA,cAAW;4DAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;sDAK7B,8OAAC,yLAAA,CAAA,kBAAe;sDACb,cAAc,uBACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,SAAS;oDAAE,QAAQ;oDAAG,SAAS;gDAAE;gDACjC,SAAS;oDAAE,QAAQ;oDAAQ,SAAS;gDAAE;gDACtC,MAAM;oDAAE,QAAQ;oDAAG,SAAS;gDAAE;gDAC9B,YAAY;oDAAE,UAAU;gDAAI;gDAC5B,WAAU;0DAEV,cAAA,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAE,WAAU;kEACV,IAAI,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2BArCpB;;;;;;;;;;8BAkDX,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;8BAEV,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAwC;;;;;;0CAGtD,8OAAC;gCAAE,WAAU;0CAAqB;;;;;;0CAGlC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;wCACZ,YAAY;4CAAE,OAAO;wCAAK;wCAC1B,UAAU;4CAAE,OAAO;wCAAK;wCACxB,WAAU;kDACX;;;;;;kDAGD,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;wCACZ,YAAY;4CAAE,OAAO;wCAAK;wCAC1B,UAAU;4CAAE,OAAO;wCAAK;wCACxB,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf;uCAEe", "debugId": null}}, {"offset": {"line": 2581, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Landing%20page/admybrand-ai-suite/src/components/sections/feature-showcase.tsx"], "sourcesContent": ["'use client'\n\nimport { Badge } from '@/components/ui/badge'\nimport { But<PERSON> } from '@/components/ui/button'\nimport { motion } from 'framer-motion'\nimport { BarChart3, Mail, Monitor, PenTool } from 'lucide-react'\nimport { useState } from 'react'\n\nconst FeatureShowcase = () => {\n  const [activeFeature, setActiveFeature] = useState(0)\n\n  const features = [\n    {\n      id: 0,\n      icon: Monitor,\n      name: \"AI Ad Creation\",\n      title: \"Effortless, On-Brand AI Ads — In Minutes\",\n      description: \"Turn any URL into stunning, brand-aligned video and image ads. Just paste your link, and <PERSON>mne<PERSON> instantly generates 5-10 ready-to-launch creatives tailored to your brand.\",\n      categories: [\"General\", \"Announcements\", \"Discounts\", \"Lifestyle Visuals\", \"Product Launches\", \"Testimonial Ads\", \"With People\"],\n      mockupType: \"ads\",\n      color: \"from-pink-500 to-rose-500\"\n    },\n    {\n      id: 1,\n      icon: PenTool,\n      name: \"AI Content Generator\",\n      title: \"Create Compelling Content — Instantly\",\n      description: \"Generate blog posts, social media content, and marketing copy that converts. Our AI understands your brand voice and creates content that resonates with your audience.\",\n      categories: [\"Blog Posts\", \"Social Media\", \"Email Copy\", \"Product Descriptions\", \"Headlines\", \"CTAs\"],\n      mockupType: \"content\",\n      color: \"from-purple-500 to-pink-500\"\n    },\n    {\n      id: 2,\n      icon: Mail,\n      name: \"Email Marketing AI\",\n      title: \"Personalized Email Campaigns — At Scale\",\n      description: \"Create email campaigns that convert. Generate subject lines, content, and optimal send times that increase open rates and drive revenue.\",\n      categories: [\"Welcome Series\", \"Product Updates\", \"Newsletters\", \"Promotional\", \"Abandoned Cart\", \"Re-engagement\"],\n      mockupType: \"email\",\n      color: \"from-indigo-500 to-purple-500\"\n    },\n    {\n      id: 3,\n      icon: BarChart3,\n      name: \"Analytics Dashboard\",\n      title: \"AI-Powered Marketing Insights — Real-Time\",\n      description: \"Get deep insights into your marketing performance with AI-powered analytics that provide actionable recommendations to improve your ROI.\",\n      categories: [\"Performance\", \"Audience\", \"Conversion\", \"Revenue\", \"Trends\", \"Predictions\"],\n      mockupType: \"analytics\",\n      color: \"from-yellow-500 to-orange-500\"\n    }\n  ]\n\n  const renderMockup = (feature) => {\n    switch (feature.mockupType) {\n      case 'ads':\n        return (\n          <div className=\"space-y-6\">\n            {/* Video Ad Preview */}\n            <div className=\"bg-white/90 backdrop-blur-sm rounded-xl p-6 shadow-2xl border border-white/30\">\n              <div className=\"flex items-center justify-between mb-4\">\n                <h4 className=\"font-semibold text-gray-800\">Video Ad Preview</h4>\n                <Badge className=\"bg-red-100 text-red-600\">Live Demo</Badge>\n              </div>\n              <div className=\"relative w-full h-48 bg-gradient-to-br from-purple-600 via-blue-600 to-cyan-500 rounded-lg overflow-hidden group cursor-pointer\">\n                {/* Video placeholder with play button */}\n                <div className=\"absolute inset-0 flex items-center justify-center\">\n                  <motion.div\n                    className=\"w-16 h-16 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center group-hover:bg-white/30 transition-all duration-300\"\n                    whileHover={{ scale: 1.1 }}\n                    whileTap={{ scale: 0.95 }}\n                  >\n                    <div className=\"w-0 h-0 border-l-[12px] border-l-white border-t-[8px] border-t-transparent border-b-[8px] border-b-transparent ml-1\"></div>\n                  </motion.div>\n                </div>\n                {/* Video content overlay */}\n                <div className=\"absolute bottom-4 left-4 right-4\">\n                  <h3 className=\"text-white font-bold text-lg mb-1\">Transform Your Business</h3>\n                  <p className=\"text-white/90 text-sm\">AI-powered marketing that delivers results</p>\n                </div>\n                {/* Video duration */}\n                <div className=\"absolute top-4 right-4 bg-black/50 text-white text-xs px-2 py-1 rounded\">0:30</div>\n              </div>\n            </div>\n\n            {/* Static Ad Examples Grid */}\n            <div className=\"grid grid-cols-2 lg:grid-cols-4 gap-4\">\n              {/* Baby Food Ad */}\n              <motion.div\n                className=\"bg-white/95 backdrop-blur-sm rounded-xl p-4 shadow-lg hover:shadow-xl transition-all duration-300 border border-white/40\"\n                whileHover={{ y: -5 }}\n              >\n                <div className=\"relative w-full h-32 bg-gradient-to-br from-orange-400 to-pink-500 rounded-lg mb-3 overflow-hidden\">\n                  <div className=\"absolute inset-0 bg-black/20\"></div>\n                  <div className=\"absolute bottom-2 left-2 right-2\">\n                    <span className=\"text-white font-bold text-sm block\">Meals Made For Your Baby</span>\n                  </div>\n                  <div className=\"absolute top-2 right-2 w-6 h-6 bg-orange-500 rounded-full flex items-center justify-center\">\n                    <span className=\"text-white text-xs font-bold\">$5</span>\n                  </div>\n                </div>\n                <p className=\"text-xs text-gray-600 mb-2\">Custom nutrition for your child</p>\n                <Button size=\"sm\" className=\"w-full bg-orange-500 hover:bg-orange-600 text-white text-xs\">Learn More</Button>\n              </motion.div>\n\n              {/* Investment App Ad */}\n              <motion.div\n                className=\"bg-white/95 backdrop-blur-sm rounded-xl p-4 shadow-lg hover:shadow-xl transition-all duration-300 border border-white/40\"\n                whileHover={{ y: -5 }}\n              >\n                <div className=\"relative w-full h-32 bg-gradient-to-br from-green-600 to-blue-600 rounded-lg mb-3 overflow-hidden\">\n                  <div className=\"absolute inset-0 bg-black/20\"></div>\n                  <div className=\"absolute bottom-2 left-2 right-2\">\n                    <span className=\"text-white font-bold text-sm block\">Investing Made For You</span>\n                  </div>\n                  <div className=\"absolute top-2 left-2 text-white text-xs\">📈 +12.5%</div>\n                </div>\n                <p className=\"text-xs text-gray-600 mb-2\">Simple, Easy-to-use app</p>\n                <Button size=\"sm\" className=\"w-full bg-green-600 hover:bg-green-700 text-white text-xs\">Learn More</Button>\n              </motion.div>\n\n              {/* Pet Services Ad */}\n              <motion.div\n                className=\"bg-white/95 backdrop-blur-sm rounded-xl p-4 shadow-lg hover:shadow-xl transition-all duration-300 border border-white/40\"\n                whileHover={{ y: -5 }}\n              >\n                <div className=\"relative w-full h-32 bg-gradient-to-br from-blue-400 to-purple-500 rounded-lg mb-3 overflow-hidden\">\n                  <div className=\"absolute inset-0 bg-black/20\"></div>\n                  <div className=\"absolute bottom-2 left-2 right-2\">\n                    <span className=\"text-white font-bold text-sm block\">Trusted by Local Pet Owners</span>\n                  </div>\n                  <div className=\"absolute top-2 right-2 text-white text-xs\">🐕 4.9★</div>\n                </div>\n                <p className=\"text-xs text-gray-600 mb-2\">Join happy pet families today</p>\n                <Button size=\"sm\" className=\"w-full bg-blue-600 hover:bg-blue-700 text-white text-xs\">Learn More</Button>\n              </motion.div>\n\n              {/* Adventure/Clothing Ad */}\n              <motion.div\n                className=\"bg-white/95 backdrop-blur-sm rounded-xl p-4 shadow-lg hover:shadow-xl transition-all duration-300 border border-white/40\"\n                whileHover={{ y: -5 }}\n              >\n                <div className=\"relative w-full h-32 bg-gradient-to-br from-gray-800 to-yellow-500 rounded-lg mb-3 overflow-hidden\">\n                  <div className=\"absolute inset-0 bg-black/30\"></div>\n                  <div className=\"absolute bottom-2 left-2 right-2\">\n                    <span className=\"text-white font-bold text-sm block\">Adventure Awaits</span>\n                  </div>\n                  <div className=\"absolute top-2 left-2 text-yellow-400 text-xs\">⚡ LIMITED</div>\n                </div>\n                <p className=\"text-xs text-gray-600 mb-2\">Miss it, miss the journey</p>\n                <Button size=\"sm\" className=\"w-full bg-gray-800 hover:bg-gray-900 text-white text-xs\">Learn More</Button>\n              </motion.div>\n            </div>\n          </div>\n        )\n      \n      case 'content':\n        return (\n          <div className=\"space-y-6\">\n            {/* Content Generation Interface */}\n            <div className=\"bg-white rounded-xl p-6 shadow-2xl\">\n              <div className=\"flex items-center justify-between mb-4\">\n                <h4 className=\"font-semibold text-gray-800\">AI Content Generator</h4>\n                <Badge className=\"bg-green-100 text-green-600\">Generating...</Badge>\n              </div>\n\n              {/* Input Section */}\n              <div className=\"bg-gray-50 rounded-lg p-4 mb-4\">\n                <label className=\"text-sm font-medium text-gray-700 block mb-2\">Topic:</label>\n                <div className=\"bg-white border rounded-lg p-3 text-sm text-gray-600\">\n                  \"AI marketing trends for small businesses\"\n                </div>\n              </div>\n\n              {/* Generated Content */}\n              <div className=\"space-y-4\">\n                <motion.div\n                  className=\"border-l-4 border-blue-500 pl-4 bg-blue-50 p-3 rounded-r-lg\"\n                  initial={{ opacity: 0, x: -20 }}\n                  animate={{ opacity: 1, x: 0 }}\n                  transition={{ delay: 0.2 }}\n                >\n                  <div className=\"flex items-center justify-between mb-2\">\n                    <h4 className=\"font-semibold text-gray-800\">Blog Post Title</h4>\n                    <span className=\"text-xs text-blue-600 bg-blue-100 px-2 py-1 rounded\">SEO Score: 95</span>\n                  </div>\n                  <p className=\"text-sm text-gray-700\">\"10 AI Marketing Trends That Will Transform Small Businesses in 2024\"</p>\n                </motion.div>\n\n                <motion.div\n                  className=\"border-l-4 border-green-500 pl-4 bg-green-50 p-3 rounded-r-lg\"\n                  initial={{ opacity: 0, x: -20 }}\n                  animate={{ opacity: 1, x: 0 }}\n                  transition={{ delay: 0.4 }}\n                >\n                  <div className=\"flex items-center justify-between mb-2\">\n                    <h4 className=\"font-semibold text-gray-800\">Social Media Caption</h4>\n                    <span className=\"text-xs text-green-600 bg-green-100 px-2 py-1 rounded\">Engagement: High</span>\n                  </div>\n                  <p className=\"text-sm text-gray-700\">\"🚀 Ready to 10x your marketing results? AI is changing the game for small businesses! Here's what you need to know... #AIMarketing #SmallBusiness\"</p>\n                </motion.div>\n\n                <motion.div\n                  className=\"border-l-4 border-purple-500 pl-4 bg-purple-50 p-3 rounded-r-lg\"\n                  initial={{ opacity: 0, x: -20 }}\n                  animate={{ opacity: 1, x: 0 }}\n                  transition={{ delay: 0.6 }}\n                >\n                  <div className=\"flex items-center justify-between mb-2\">\n                    <h4 className=\"font-semibold text-gray-800\">Email Subject Line</h4>\n                    <span className=\"text-xs text-purple-600 bg-purple-100 px-2 py-1 rounded\">Open Rate: 47%</span>\n                  </div>\n                  <p className=\"text-sm text-gray-700\">\"[Name], your competitors are using AI (are you?)\"</p>\n                </motion.div>\n              </div>\n            </div>\n          </div>\n        )\n      \n      case 'email':\n        return (\n          <div className=\"space-y-6\">\n            {/* Email Campaign Builder */}\n            <div className=\"bg-white rounded-xl p-6 shadow-2xl\">\n              <div className=\"flex items-center justify-between mb-4\">\n                <h4 className=\"font-semibold text-gray-800\">Email Campaign Builder</h4>\n                <Badge className=\"bg-blue-100 text-blue-600\">AI Optimized</Badge>\n              </div>\n\n              {/* Email Preview */}\n              <div className=\"border rounded-lg overflow-hidden mb-4\">\n                {/* Email Header */}\n                <div className=\"bg-gradient-to-r from-blue-600 to-purple-600 p-4 text-white\">\n                  <div className=\"flex items-center justify-between\">\n                    <span className=\"font-semibold\">ADmyBRAND AI Suite</span>\n                    <span className=\"text-xs bg-white/20 px-2 py-1 rounded\">Premium</span>\n                  </div>\n                </div>\n\n                {/* Email Content */}\n                <div className=\"p-4 bg-gray-50\">\n                  <h3 className=\"font-bold text-lg text-gray-800 mb-2\">Hi Sarah! 👋</h3>\n                  <p className=\"text-sm text-gray-700 mb-3\">\n                    Your marketing campaigns could be performing 250% better with AI optimization...\n                  </p>\n                  <div className=\"bg-white p-3 rounded-lg border mb-3\">\n                    <div className=\"flex items-center justify-between\">\n                      <span className=\"text-sm font-medium\">Your Current Performance:</span>\n                      <span className=\"text-xs text-gray-500\">Last 30 days</span>\n                    </div>\n                    <div className=\"grid grid-cols-3 gap-2 mt-2\">\n                      <div className=\"text-center\">\n                        <div className=\"text-lg font-bold text-red-600\">2.3%</div>\n                        <div className=\"text-xs text-gray-600\">Open Rate</div>\n                      </div>\n                      <div className=\"text-center\">\n                        <div className=\"text-lg font-bold text-orange-600\">0.8%</div>\n                        <div className=\"text-xs text-gray-600\">Click Rate</div>\n                      </div>\n                      <div className=\"text-center\">\n                        <div className=\"text-lg font-bold text-gray-600\">$1.2K</div>\n                        <div className=\"text-xs text-gray-600\">Revenue</div>\n                      </div>\n                    </div>\n                  </div>\n                  <Button className=\"w-full bg-gradient-to-r from-blue-600 to-purple-600 text-white\">\n                    Boost My Results with AI\n                  </Button>\n                </div>\n              </div>\n\n              {/* Performance Metrics */}\n              <div className=\"grid grid-cols-3 gap-4 text-center\">\n                <div className=\"bg-green-50 p-3 rounded-lg\">\n                  <div className=\"text-lg font-bold text-green-600\">47.8%</div>\n                  <div className=\"text-xs text-gray-600\">Predicted Open Rate</div>\n                </div>\n                <div className=\"bg-blue-50 p-3 rounded-lg\">\n                  <div className=\"text-lg font-bold text-blue-600\">12.4%</div>\n                  <div className=\"text-xs text-gray-600\">Predicted Click Rate</div>\n                </div>\n                <div className=\"bg-purple-50 p-3 rounded-lg\">\n                  <div className=\"text-lg font-bold text-purple-600\">$8.7K</div>\n                  <div className=\"text-xs text-gray-600\">Projected Revenue</div>\n                </div>\n              </div>\n            </div>\n          </div>\n        )\n      \n      case 'analytics':\n        return (\n          <div className=\"space-y-6\">\n            {/* Analytics Dashboard */}\n            <div className=\"bg-white rounded-xl p-6 shadow-2xl\">\n              <div className=\"flex items-center justify-between mb-6\">\n                <h4 className=\"font-semibold text-gray-800\">AI Analytics Dashboard</h4>\n                <div className=\"flex items-center gap-2\">\n                  <div className=\"w-2 h-2 bg-green-500 rounded-full animate-pulse\"></div>\n                  <span className=\"text-xs text-gray-600\">Live Data</span>\n                </div>\n              </div>\n\n              {/* Key Metrics */}\n              <div className=\"grid grid-cols-2 lg:grid-cols-4 gap-4 mb-6\">\n                <motion.div\n                  className=\"bg-gradient-to-br from-green-50 to-green-100 p-4 rounded-xl border border-green-200\"\n                  initial={{ opacity: 0, y: 20 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ delay: 0.1 }}\n                >\n                  <div className=\"text-2xl font-bold text-green-600\">+247%</div>\n                  <div className=\"text-sm text-gray-600\">Conversion Rate</div>\n                  <div className=\"text-xs text-green-600 mt-1\">↗ +12% this week</div>\n                </motion.div>\n\n                <motion.div\n                  className=\"bg-gradient-to-br from-blue-50 to-blue-100 p-4 rounded-xl border border-blue-200\"\n                  initial={{ opacity: 0, y: 20 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ delay: 0.2 }}\n                >\n                  <div className=\"text-2xl font-bold text-blue-600\">$127K</div>\n                  <div className=\"text-sm text-gray-600\">Revenue Generated</div>\n                  <div className=\"text-xs text-blue-600 mt-1\">↗ +34% this month</div>\n                </motion.div>\n\n                <motion.div\n                  className=\"bg-gradient-to-br from-purple-50 to-purple-100 p-4 rounded-xl border border-purple-200\"\n                  initial={{ opacity: 0, y: 20 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ delay: 0.3 }}\n                >\n                  <div className=\"text-2xl font-bold text-purple-600\">89%</div>\n                  <div className=\"text-sm text-gray-600\">Cost Reduction</div>\n                  <div className=\"text-xs text-purple-600 mt-1\">↗ Optimized</div>\n                </motion.div>\n\n                <motion.div\n                  className=\"bg-gradient-to-br from-orange-50 to-orange-100 p-4 rounded-xl border border-orange-200\"\n                  initial={{ opacity: 0, y: 20 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ delay: 0.4 }}\n                >\n                  <div className=\"text-2xl font-bold text-orange-600\">15min</div>\n                  <div className=\"text-sm text-gray-600\">Setup Time</div>\n                  <div className=\"text-xs text-orange-600 mt-1\">⚡ Instant</div>\n                </motion.div>\n              </div>\n\n              {/* Chart Placeholder */}\n              <div className=\"bg-gray-50 rounded-lg p-4 mb-4\">\n                <div className=\"flex items-center justify-between mb-3\">\n                  <span className=\"text-sm font-medium text-gray-700\">Performance Trend</span>\n                  <span className=\"text-xs text-gray-500\">Last 30 days</span>\n                </div>\n                <div className=\"h-32 bg-gradient-to-r from-blue-100 via-purple-100 to-pink-100 rounded-lg flex items-end justify-between p-4\">\n                  {/* Simulated chart bars */}\n                  {[40, 65, 45, 80, 60, 90, 75, 95].map((height, index) => (\n                    <motion.div\n                      key={index}\n                      className=\"bg-gradient-to-t from-blue-500 to-purple-500 rounded-t\"\n                      style={{ height: `${height}%`, width: '8%' }}\n                      initial={{ height: 0 }}\n                      animate={{ height: `${height}%` }}\n                      transition={{ delay: index * 0.1, duration: 0.5 }}\n                    />\n                  ))}\n                </div>\n              </div>\n\n              {/* AI Recommendations */}\n              <div className=\"bg-blue-50 border border-blue-200 rounded-lg p-4\">\n                <div className=\"flex items-center gap-2 mb-2\">\n                  <div className=\"w-4 h-4 bg-blue-500 rounded-full flex items-center justify-center\">\n                    <span className=\"text-white text-xs\">AI</span>\n                  </div>\n                  <span className=\"text-sm font-medium text-blue-800\">Smart Recommendations</span>\n                </div>\n                <p className=\"text-sm text-blue-700\">\n                  Increase your ad spend on Facebook by 23% and reduce Google Ads budget by 15% to optimize ROI.\n                </p>\n              </div>\n            </div>\n          </div>\n        )\n      \n      default:\n        return null\n    }\n  }\n\n  return (\n    <section className=\"py-24 bg-gradient-to-br from-gray-900 via-black to-gray-900\">\n      <div className=\"max-w-7xl mx-auto px-2 sm:px-4 lg:px-6\">\n        {/* Section Header */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          viewport={{ once: true }}\n          className=\"text-center mb-16\"\n        >\n          <Badge className=\"mb-4 bg-white/10 text-white border-white/20 backdrop-blur-sm\">\n            Feature Showcase\n          </Badge>\n          <h2 className=\"text-4xl md:text-5xl font-bold text-white mb-6\">\n            See Our AI Tools\n            <span className=\"bg-gradient-to-r from-blue-400 to-cyan-400 bg-clip-text text-transparent\"> In Action</span>\n          </h2>\n          <p className=\"text-xl text-gray-300 max-w-3xl mx-auto\">\n            Experience how our AI-powered features transform your marketing workflow with real examples and live demonstrations.\n          </p>\n        </motion.div>\n\n        {/* Glass Morphism Wrapper */}\n        <div className=\"relative bg-white/5 backdrop-blur-xl border border-white/10 rounded-3xl p-8 md:p-12 shadow-2xl\">\n          {/* Glass effect overlay */}\n          <div className=\"absolute inset-0 bg-gradient-to-br from-white/10 via-white/5 to-transparent rounded-3xl pointer-events-none\"></div>\n\n          {/* Content */}\n          <div className=\"relative z-10\">\n            {/* Feature Navigation */}\n            <div className=\"flex flex-wrap justify-center gap-4 mb-12\">\n              {features.map((feature, index) => (\n                <motion.button\n                  key={feature.id}\n                  onClick={() => setActiveFeature(index)}\n                  className={`flex items-center gap-2 px-6 py-3 rounded-full transition-all duration-300 ${\n                    activeFeature === index\n                      ? 'bg-white text-black shadow-lg'\n                      : 'bg-white/10 text-white hover:bg-white/20 border border-white/20'\n                  }`}\n                  whileHover={{ scale: 1.05 }}\n                  whileTap={{ scale: 0.95 }}\n                >\n                  <feature.icon className=\"w-4 h-4\" />\n                  <span className=\"font-medium\">{feature.name}</span>\n                </motion.button>\n              ))}\n            </div>\n\n            {/* Active Feature Display */}\n            <motion.div\n              key={activeFeature}\n              initial={{ opacity: 0, y: 30 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.5 }}\n              className=\"grid lg:grid-cols-2 gap-12 items-center\"\n            >\n              {/* Feature Info */}\n              <div className=\"space-y-6\">\n                <div>\n                  <h3 className=\"text-3xl md:text-4xl font-bold text-white mb-4\">\n                    {features[activeFeature].title}\n                  </h3>\n                  <p className=\"text-lg text-gray-300 leading-relaxed\">\n                    {features[activeFeature].description}\n                  </p>\n                </div>\n\n                <Button className=\"bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 text-lg\">\n                  Try Now for Free\n                </Button>\n              </div>\n\n              {/* Feature Mockup */}\n              <div className=\"relative\">\n                <motion.div\n                  initial={{ opacity: 0, scale: 0.9 }}\n                  animate={{ opacity: 1, scale: 1 }}\n                  transition={{ duration: 0.5, delay: 0.2 }}\n                  className=\"relative\"\n                >\n                  {/* Glass Morphism Container */}\n                  <div className=\"relative bg-white/10 backdrop-blur-xl border border-white/20 rounded-3xl p-8 shadow-2xl\">\n                    {/* Glass effect overlay */}\n                    <div className=\"absolute inset-0 bg-gradient-to-br from-white/20 via-white/10 to-transparent rounded-3xl\"></div>\n\n                    {/* Content */}\n                    <div className=\"relative z-10\">\n                      {renderMockup(features[activeFeature])}\n                    </div>\n\n                    {/* Additional glass shine effect */}\n                    <div className=\"absolute top-0 left-0 w-full h-full bg-gradient-to-br from-white/30 via-transparent to-transparent rounded-3xl opacity-50 pointer-events-none\"></div>\n                  </div>\n                </motion.div>\n              </div>\n            </motion.div>\n          </div>\n\n          {/* Additional glass shine effect for wrapper */}\n          <div className=\"absolute top-0 left-0 w-full h-full bg-gradient-to-br from-white/20 via-transparent to-transparent rounded-3xl opacity-30 pointer-events-none\"></div>\n        </div>\n      </div>\n    </section>\n  )\n}\n\nexport default FeatureShowcase\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AANA;;;;;;;AAQA,MAAM,kBAAkB;IACtB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,MAAM,WAAW;QACf;YACE,IAAI;YACJ,MAAM,wMAAA,CAAA,UAAO;YACb,MAAM;YACN,OAAO;YACP,aAAa;YACb,YAAY;gBAAC;gBAAW;gBAAiB;gBAAa;gBAAqB;gBAAoB;gBAAmB;aAAc;YAChI,YAAY;YACZ,OAAO;QACT;QACA;YACE,IAAI;YACJ,MAAM,4MAAA,CAAA,UAAO;YACb,MAAM;YACN,OAAO;YACP,aAAa;YACb,YAAY;gBAAC;gBAAc;gBAAgB;gBAAc;gBAAwB;gBAAa;aAAO;YACrG,YAAY;YACZ,OAAO;QACT;QACA;YACE,IAAI;YACJ,MAAM,kMAAA,CAAA,OAAI;YACV,MAAM;YACN,OAAO;YACP,aAAa;YACb,YAAY;gBAAC;gBAAkB;gBAAmB;gBAAe;gBAAe;gBAAkB;aAAgB;YAClH,YAAY;YACZ,OAAO;QACT;QACA;YACE,IAAI;YACJ,MAAM,kNAAA,CAAA,YAAS;YACf,MAAM;YACN,OAAO;YACP,aAAa;YACb,YAAY;gBAAC;gBAAe;gBAAY;gBAAc;gBAAW;gBAAU;aAAc;YACzF,YAAY;YACZ,OAAO;QACT;KACD;IAED,MAAM,eAAe,CAAC;QACpB,OAAQ,QAAQ,UAAU;YACxB,KAAK;gBACH,qBACE,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAA8B;;;;;;sDAC5C,8OAAC,iIAAA,CAAA,QAAK;4CAAC,WAAU;sDAA0B;;;;;;;;;;;;8CAE7C,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,WAAU;gDACV,YAAY;oDAAE,OAAO;gDAAI;gDACzB,UAAU;oDAAE,OAAO;gDAAK;0DAExB,cAAA,8OAAC;oDAAI,WAAU;;;;;;;;;;;;;;;;sDAInB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAoC;;;;;;8DAClD,8OAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;sDAGvC,8OAAC;4CAAI,WAAU;sDAA0E;;;;;;;;;;;;;;;;;;sCAK7F,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,WAAU;oCACV,YAAY;wCAAE,GAAG,CAAC;oCAAE;;sDAEpB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;;;;;8DACf,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAK,WAAU;kEAAqC;;;;;;;;;;;8DAEvD,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAK,WAAU;kEAA+B;;;;;;;;;;;;;;;;;sDAGnD,8OAAC;4CAAE,WAAU;sDAA6B;;;;;;sDAC1C,8OAAC,kIAAA,CAAA,SAAM;4CAAC,MAAK;4CAAK,WAAU;sDAA8D;;;;;;;;;;;;8CAI5F,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,WAAU;oCACV,YAAY;wCAAE,GAAG,CAAC;oCAAE;;sDAEpB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;;;;;8DACf,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAK,WAAU;kEAAqC;;;;;;;;;;;8DAEvD,8OAAC;oDAAI,WAAU;8DAA2C;;;;;;;;;;;;sDAE5D,8OAAC;4CAAE,WAAU;sDAA6B;;;;;;sDAC1C,8OAAC,kIAAA,CAAA,SAAM;4CAAC,MAAK;4CAAK,WAAU;sDAA4D;;;;;;;;;;;;8CAI1F,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,WAAU;oCACV,YAAY;wCAAE,GAAG,CAAC;oCAAE;;sDAEpB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;;;;;8DACf,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAK,WAAU;kEAAqC;;;;;;;;;;;8DAEvD,8OAAC;oDAAI,WAAU;8DAA4C;;;;;;;;;;;;sDAE7D,8OAAC;4CAAE,WAAU;sDAA6B;;;;;;sDAC1C,8OAAC,kIAAA,CAAA,SAAM;4CAAC,MAAK;4CAAK,WAAU;sDAA0D;;;;;;;;;;;;8CAIxF,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,WAAU;oCACV,YAAY;wCAAE,GAAG,CAAC;oCAAE;;sDAEpB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;;;;;8DACf,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAK,WAAU;kEAAqC;;;;;;;;;;;8DAEvD,8OAAC;oDAAI,WAAU;8DAAgD;;;;;;;;;;;;sDAEjE,8OAAC;4CAAE,WAAU;sDAA6B;;;;;;sDAC1C,8OAAC,kIAAA,CAAA,SAAM;4CAAC,MAAK;4CAAK,WAAU;sDAA0D;;;;;;;;;;;;;;;;;;;;;;;;YAMhG,KAAK;gBACH,qBACE,8OAAC;oBAAI,WAAU;8BAEb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA8B;;;;;;kDAC5C,8OAAC,iIAAA,CAAA,QAAK;wCAAC,WAAU;kDAA8B;;;;;;;;;;;;0CAIjD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAM,WAAU;kDAA+C;;;;;;kDAChE,8OAAC;wCAAI,WAAU;kDAAuD;;;;;;;;;;;;0CAMxE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,WAAU;wCACV,SAAS;4CAAE,SAAS;4CAAG,GAAG,CAAC;wCAAG;wCAC9B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,OAAO;wCAAI;;0DAEzB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAA8B;;;;;;kEAC5C,8OAAC;wDAAK,WAAU;kEAAsD;;;;;;;;;;;;0DAExE,8OAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;kDAGvC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,WAAU;wCACV,SAAS;4CAAE,SAAS;4CAAG,GAAG,CAAC;wCAAG;wCAC9B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,OAAO;wCAAI;;0DAEzB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAA8B;;;;;;kEAC5C,8OAAC;wDAAK,WAAU;kEAAwD;;;;;;;;;;;;0DAE1E,8OAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;kDAGvC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,WAAU;wCACV,SAAS;4CAAE,SAAS;4CAAG,GAAG,CAAC;wCAAG;wCAC9B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,OAAO;wCAAI;;0DAEzB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAA8B;;;;;;kEAC5C,8OAAC;wDAAK,WAAU;kEAA0D;;;;;;;;;;;;0DAE5E,8OAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAOjD,KAAK;gBACH,qBACE,8OAAC;oBAAI,WAAU;8BAEb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA8B;;;;;;kDAC5C,8OAAC,iIAAA,CAAA,QAAK;wCAAC,WAAU;kDAA4B;;;;;;;;;;;;0CAI/C,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAAgB;;;;;;8DAChC,8OAAC;oDAAK,WAAU;8DAAwC;;;;;;;;;;;;;;;;;kDAK5D,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAuC;;;;;;0DACrD,8OAAC;gDAAE,WAAU;0DAA6B;;;;;;0DAG1C,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAU;0EAAsB;;;;;;0EACtC,8OAAC;gEAAK,WAAU;0EAAwB;;;;;;;;;;;;kEAE1C,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;kFAAiC;;;;;;kFAChD,8OAAC;wEAAI,WAAU;kFAAwB;;;;;;;;;;;;0EAEzC,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;kFAAoC;;;;;;kFACnD,8OAAC;wEAAI,WAAU;kFAAwB;;;;;;;;;;;;0EAEzC,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;kFAAkC;;;;;;kFACjD,8OAAC;wEAAI,WAAU;kFAAwB;;;;;;;;;;;;;;;;;;;;;;;;0DAI7C,8OAAC,kIAAA,CAAA,SAAM;gDAAC,WAAU;0DAAiE;;;;;;;;;;;;;;;;;;0CAOvF,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DAAmC;;;;;;0DAClD,8OAAC;gDAAI,WAAU;0DAAwB;;;;;;;;;;;;kDAEzC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DAAkC;;;;;;0DACjD,8OAAC;gDAAI,WAAU;0DAAwB;;;;;;;;;;;;kDAEzC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DAAoC;;;;;;0DACnD,8OAAC;gDAAI,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAOnD,KAAK;gBACH,qBACE,8OAAC;oBAAI,WAAU;8BAEb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA8B;;;;;;kDAC5C,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAK,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;0CAK5C,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,WAAU;wCACV,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,OAAO;wCAAI;;0DAEzB,8OAAC;gDAAI,WAAU;0DAAoC;;;;;;0DACnD,8OAAC;gDAAI,WAAU;0DAAwB;;;;;;0DACvC,8OAAC;gDAAI,WAAU;0DAA8B;;;;;;;;;;;;kDAG/C,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,WAAU;wCACV,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,OAAO;wCAAI;;0DAEzB,8OAAC;gDAAI,WAAU;0DAAmC;;;;;;0DAClD,8OAAC;gDAAI,WAAU;0DAAwB;;;;;;0DACvC,8OAAC;gDAAI,WAAU;0DAA6B;;;;;;;;;;;;kDAG9C,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,WAAU;wCACV,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,OAAO;wCAAI;;0DAEzB,8OAAC;gDAAI,WAAU;0DAAqC;;;;;;0DACpD,8OAAC;gDAAI,WAAU;0DAAwB;;;;;;0DACvC,8OAAC;gDAAI,WAAU;0DAA+B;;;;;;;;;;;;kDAGhD,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,WAAU;wCACV,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,OAAO;wCAAI;;0DAEzB,8OAAC;gDAAI,WAAU;0DAAqC;;;;;;0DACpD,8OAAC;gDAAI,WAAU;0DAAwB;;;;;;0DACvC,8OAAC;gDAAI,WAAU;0DAA+B;;;;;;;;;;;;;;;;;;0CAKlD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DAAoC;;;;;;0DACpD,8OAAC;gDAAK,WAAU;0DAAwB;;;;;;;;;;;;kDAE1C,8OAAC;wCAAI,WAAU;kDAEZ;4CAAC;4CAAI;4CAAI;4CAAI;4CAAI;4CAAI;4CAAI;4CAAI;yCAAG,CAAC,GAAG,CAAC,CAAC,QAAQ,sBAC7C,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDAET,WAAU;gDACV,OAAO;oDAAE,QAAQ,GAAG,OAAO,CAAC,CAAC;oDAAE,OAAO;gDAAK;gDAC3C,SAAS;oDAAE,QAAQ;gDAAE;gDACrB,SAAS;oDAAE,QAAQ,GAAG,OAAO,CAAC,CAAC;gDAAC;gDAChC,YAAY;oDAAE,OAAO,QAAQ;oDAAK,UAAU;gDAAI;+CAL3C;;;;;;;;;;;;;;;;0CAYb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAK,WAAU;8DAAqB;;;;;;;;;;;0DAEvC,8OAAC;gDAAK,WAAU;0DAAoC;;;;;;;;;;;;kDAEtD,8OAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;;;;;;;;;;;;YAQ/C;gBACE,OAAO;QACX;IACF;IAEA,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAEV,8OAAC,iIAAA,CAAA,QAAK;4BAAC,WAAU;sCAA+D;;;;;;sCAGhF,8OAAC;4BAAG,WAAU;;gCAAiD;8CAE7D,8OAAC;oCAAK,WAAU;8CAA2E;;;;;;;;;;;;sCAE7F,8OAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;8BAMzD,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;;;;;sCAGf,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;8CACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;4CAEZ,SAAS,IAAM,iBAAiB;4CAChC,WAAW,CAAC,2EAA2E,EACrF,kBAAkB,QACd,kCACA,mEACJ;4CACF,YAAY;gDAAE,OAAO;4CAAK;4CAC1B,UAAU;gDAAE,OAAO;4CAAK;;8DAExB,8OAAC,QAAQ,IAAI;oDAAC,WAAU;;;;;;8DACxB,8OAAC;oDAAK,WAAU;8DAAe,QAAQ,IAAI;;;;;;;2CAXtC,QAAQ,EAAE;;;;;;;;;;8CAiBrB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,UAAU;oCAAI;oCAC5B,WAAU;;sDAGV,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEACX,QAAQ,CAAC,cAAc,CAAC,KAAK;;;;;;sEAEhC,8OAAC;4DAAE,WAAU;sEACV,QAAQ,CAAC,cAAc,CAAC,WAAW;;;;;;;;;;;;8DAIxC,8OAAC,kIAAA,CAAA,SAAM;oDAAC,WAAU;8DAA6D;;;;;;;;;;;;sDAMjF,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,SAAS;oDAAE,SAAS;oDAAG,OAAO;gDAAI;gDAClC,SAAS;oDAAE,SAAS;oDAAG,OAAO;gDAAE;gDAChC,YAAY;oDAAE,UAAU;oDAAK,OAAO;gDAAI;gDACxC,WAAU;0DAGV,cAAA,8OAAC;oDAAI,WAAU;;sEAEb,8OAAC;4DAAI,WAAU;;;;;;sEAGf,8OAAC;4DAAI,WAAU;sEACZ,aAAa,QAAQ,CAAC,cAAc;;;;;;sEAIvC,8OAAC;4DAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;mCAzChB;;;;;;;;;;;sCAiDT,8OAAC;4BAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;AAKzB;uCAEe", "debugId": null}}, {"offset": {"line": 4263, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Landing%20page/admybrand-ai-suite/src/components/sections/features.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { Badge } from '@/components/ui/badge'\r\nimport { motion } from 'framer-motion'\r\nimport {\r\n    BarChart3,\r\n    Mail,\r\n    MessageSquare,\r\n    Monitor,\r\n    PenTool,\r\n    Zap\r\n} from 'lucide-react'\r\n\r\nconst features = [\r\n  {\r\n    icon: PenTool,\r\n    title: \"Blog writer\",\r\n    description: \"A Blog Writer AI tool helps generate high-quality and SEO-optimized blog content quickly and efficiently.\",\r\n    badge: \"Most Popular\",\r\n    color: \"from-blue-500 to-cyan-500\",\r\n    bgGradient: \"from-blue-900/20 via-blue-800/10 to-cyan-900/20\"\r\n  },\r\n  {\r\n    icon: BarChart3,\r\n    title: \"Real-time analytics\",\r\n    description: \"Get real-time marketing analytics to make smarter, faster decisions on the fly.\",\r\n    badge: \"Analytics\",\r\n    color: \"from-purple-500 to-pink-500\",\r\n    bgGradient: \"from-purple-900/20 via-purple-800/10 to-pink-900/20\"\r\n  },\r\n  {\r\n    icon: Zap,\r\n    title: \"AI-driven automation\",\r\n    description: \"Streamline your marketing processes with AI that cuts down on manual work.\",\r\n    badge: \"Automation\",\r\n    color: \"from-indigo-500 to-purple-500\",\r\n    bgGradient: \"from-indigo-900/20 via-indigo-800/10 to-purple-900/20\"\r\n  },\r\n  {\r\n    icon: MessageSquare,\r\n    title: \"Brand Voice Analyzer\",\r\n    description: \"Maintain consistent messaging across all platforms. Our AI learns your brand's unique voice and ensures every piece of content matches your style.\",\r\n    badge: \"New\",\r\n    color: \"from-teal-500 to-blue-500\",\r\n    bgGradient: \"from-teal-900/20 via-teal-800/10 to-blue-900/20\"\r\n  },\r\n  {\r\n    icon: Mail,\r\n    title: \"Email Marketing AI\",\r\n    description: \"Personalize email campaigns at scale. Generate subject lines, content, and send times that increase open rates and conversions.\",\r\n    badge: \"Essential\",\r\n    color: \"from-green-500 to-teal-500\",\r\n    bgGradient: \"from-green-900/20 via-green-800/10 to-teal-900/20\"\r\n  },\r\n  {\r\n    icon: Monitor,\r\n    title: \"AI Ad Creation\",\r\n    description: \"Generate high-converting ads for all platforms instantly. Create compelling visuals, copy, and targeting strategies that drive results and maximize ROI.\",\r\n    badge: \"Creative\",\r\n    color: \"from-pink-500 to-rose-500\",\r\n    bgGradient: \"from-pink-900/20 via-pink-800/10 to-rose-900/20\"\r\n  }\r\n]\r\n\r\nconst Features = () => {\r\n  return (\r\n    <section className=\"pt-8 pb-24 bg-black\">\r\n      <div className=\"max-w-7xl mx-auto px-2 sm:px-4 lg:px-6\">\r\n        {/* Section Header */}\r\n        <motion.div\r\n          initial={{ opacity: 0, y: 30 }}\r\n          whileInView={{ opacity: 1, y: 0 }}\r\n          transition={{ duration: 0.8 }}\r\n          viewport={{ once: true }}\r\n          className=\"text-center mb-16\"\r\n        >\r\n          <Badge className=\"mb-4 bg-white/10 text-white border-white/20 backdrop-blur-sm\">\r\n            Powerful Features\r\n          </Badge>\r\n          <h2 className=\"text-4xl md:text-5xl font-bold text-white mb-6\">\r\n            Everything you need to\r\n            <span className=\"bg-gradient-to-r from-blue-400 to-cyan-400 bg-clip-text text-transparent\"> dominate marketing</span>\r\n          </h2>\r\n          <p className=\"text-xl text-gray-300 max-w-3xl mx-auto\">\r\n            Our comprehensive AI suite provides all the tools you need to create, optimize, and scale your marketing efforts with unprecedented efficiency.\r\n          </p>\r\n        </motion.div>\r\n\r\n        {/* Features Grid */}\r\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\r\n          {features.map((feature, index) => (\r\n            <motion.div\r\n              key={feature.title}\r\n              initial={{ opacity: 0, y: 30 }}\r\n              whileInView={{ opacity: 1, y: 0 }}\r\n              transition={{ duration: 0.6, delay: index * 0.1 }}\r\n              viewport={{ once: true }}\r\n              whileHover={{ y: -8, scale: 1.02 }}\r\n              className=\"group\"\r\n            >\r\n              <div className={`relative h-full rounded-2xl border border-white/10 shadow-2xl hover:shadow-3xl transition-all duration-500 overflow-hidden bg-gradient-to-br ${feature.bgGradient} backdrop-blur-xl hover:border-white/20`}>\r\n                {/* Animated background gradient */}\r\n                <div className=\"absolute inset-0 bg-gradient-to-br from-white/5 via-transparent to-white/10 pointer-events-none\" />\r\n                <div className={`absolute inset-0 bg-gradient-to-br ${feature.color} opacity-0 group-hover:opacity-10 transition-opacity duration-500`} />\r\n\r\n                {/* Content */}\r\n                <div className=\"relative z-10 p-6 h-full flex flex-col\">\r\n                  {/* Header with Icon and Badge */}\r\n                  <div className=\"flex items-start justify-between mb-4\">\r\n                    <motion.div\r\n                      className={`p-3 rounded-xl bg-gradient-to-br ${feature.color} shadow-lg`}\r\n                      whileHover={{ scale: 1.1, rotate: 5 }}\r\n                      transition={{ duration: 0.2 }}\r\n                    >\r\n                      <feature.icon className=\"w-6 h-6 text-white\" />\r\n                    </motion.div>\r\n                    <Badge className=\"text-xs bg-white/20 text-white border-white/30 backdrop-blur-sm px-2 py-1\">\r\n                      {feature.badge}\r\n                    </Badge>\r\n                  </div>\r\n\r\n                  {/* Title */}\r\n                  <h3 className=\"text-lg font-bold text-white mb-3 group-hover:text-transparent group-hover:bg-gradient-to-r group-hover:from-blue-400 group-hover:to-cyan-400 group-hover:bg-clip-text transition-all duration-300\">\r\n                    {feature.title}\r\n                  </h3>\r\n\r\n                  {/* Description */}\r\n                  <p className=\"text-gray-300 text-sm leading-relaxed flex-grow\">\r\n                    {feature.description}\r\n                  </p>\r\n\r\n                  {/* Hover Effect */}\r\n                  <div className=\"mt-6 opacity-0 group-hover:opacity-100 transition-all duration-300 transform translate-y-2 group-hover:translate-y-0\">\r\n                    <div className=\"text-blue-400 text-sm font-medium flex items-center\">\r\n                      Learn more\r\n                      <motion.div\r\n                        className=\"ml-2\"\r\n                        animate={{ x: [0, 4, 0] }}\r\n                        transition={{ duration: 1.5, repeat: Infinity }}\r\n                      >\r\n                        →\r\n                      </motion.div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                {/* Animated border effect */}\r\n                <div className=\"absolute inset-0 rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-500\">\r\n                  <div className={`absolute inset-0 rounded-3xl bg-gradient-to-r ${feature.color} opacity-20 blur-xl`} />\r\n                </div>\r\n              </div>\r\n            </motion.div>\r\n          ))}\r\n        </div>\r\n\r\n        {/* Bottom CTA */}\r\n        <motion.div\r\n          initial={{ opacity: 0, y: 30 }}\r\n          whileInView={{ opacity: 1, y: 0 }}\r\n          transition={{ duration: 0.8, delay: 0.4 }}\r\n          viewport={{ once: true }}\r\n          className=\"text-center mt-16\"\r\n        >\r\n          <p className=\"text-gray-600 mb-6\">\r\n            Ready to transform your marketing with AI?\r\n          </p>\r\n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\r\n            <motion.button\r\n              whileHover={{ scale: 1.05 }}\r\n              whileTap={{ scale: 0.95 }}\r\n              className=\"px-8 py-3 bg-gradient-to-r from-purple-600 to-blue-600 text-white rounded-lg font-medium shadow-lg hover:shadow-xl transition-all duration-300\"\r\n            >\r\n              Start Free Trial\r\n            </motion.button>\r\n            <motion.button\r\n              whileHover={{ scale: 1.05 }}\r\n              whileTap={{ scale: 0.95 }}\r\n              className=\"px-8 py-3 border border-gray-300 text-gray-700 rounded-lg font-medium hover:bg-gray-50 transition-all duration-300\"\r\n            >\r\n              View All Features\r\n            </motion.button>\r\n          </div>\r\n        </motion.div>\r\n      </div>\r\n    </section>\r\n  )\r\n}\r\n\r\nexport default Features\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAJA;;;;;AAaA,MAAM,WAAW;IACf;QACE,MAAM,4MAAA,CAAA,UAAO;QACb,OAAO;QACP,aAAa;QACb,OAAO;QACP,OAAO;QACP,YAAY;IACd;IACA;QACE,MAAM,kNAAA,CAAA,YAAS;QACf,OAAO;QACP,aAAa;QACb,OAAO;QACP,OAAO;QACP,YAAY;IACd;IACA;QACE,MAAM,gMAAA,CAAA,MAAG;QACT,OAAO;QACP,aAAa;QACb,OAAO;QACP,OAAO;QACP,YAAY;IACd;IACA;QACE,MAAM,wNAAA,CAAA,gBAAa;QACnB,OAAO;QACP,aAAa;QACb,OAAO;QACP,OAAO;QACP,YAAY;IACd;IACA;QACE,MAAM,kMAAA,CAAA,OAAI;QACV,OAAO;QACP,aAAa;QACb,OAAO;QACP,OAAO;QACP,YAAY;IACd;IACA;QACE,MAAM,wMAAA,CAAA,UAAO;QACb,OAAO;QACP,aAAa;QACb,OAAO;QACP,OAAO;QACP,YAAY;IACd;CACD;AAED,MAAM,WAAW;IACf,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAEV,8OAAC,iIAAA,CAAA,QAAK;4BAAC,WAAU;sCAA+D;;;;;;sCAGhF,8OAAC;4BAAG,WAAU;;gCAAiD;8CAE7D,8OAAC;oCAAK,WAAU;8CAA2E;;;;;;;;;;;;sCAE7F,8OAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;8BAMzD,8OAAC;oBAAI,WAAU;8BACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO,QAAQ;4BAAI;4BAChD,UAAU;gCAAE,MAAM;4BAAK;4BACvB,YAAY;gCAAE,GAAG,CAAC;gCAAG,OAAO;4BAAK;4BACjC,WAAU;sCAEV,cAAA,8OAAC;gCAAI,WAAW,CAAC,6IAA6I,EAAE,QAAQ,UAAU,CAAC,uCAAuC,CAAC;;kDAEzN,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAI,WAAW,CAAC,mCAAmC,EAAE,QAAQ,KAAK,CAAC,iEAAiE,CAAC;;;;;;kDAGtI,8OAAC;wCAAI,WAAU;;0DAEb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wDACT,WAAW,CAAC,iCAAiC,EAAE,QAAQ,KAAK,CAAC,UAAU,CAAC;wDACxE,YAAY;4DAAE,OAAO;4DAAK,QAAQ;wDAAE;wDACpC,YAAY;4DAAE,UAAU;wDAAI;kEAE5B,cAAA,8OAAC,QAAQ,IAAI;4DAAC,WAAU;;;;;;;;;;;kEAE1B,8OAAC,iIAAA,CAAA,QAAK;wDAAC,WAAU;kEACd,QAAQ,KAAK;;;;;;;;;;;;0DAKlB,8OAAC;gDAAG,WAAU;0DACX,QAAQ,KAAK;;;;;;0DAIhB,8OAAC;gDAAE,WAAU;0DACV,QAAQ,WAAW;;;;;;0DAItB,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;;wDAAsD;sEAEnE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4DACT,WAAU;4DACV,SAAS;gEAAE,GAAG;oEAAC;oEAAG;oEAAG;iEAAE;4DAAC;4DACxB,YAAY;gEAAE,UAAU;gEAAK,QAAQ;4DAAS;sEAC/C;;;;;;;;;;;;;;;;;;;;;;;kDAQP,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAW,CAAC,8CAA8C,EAAE,QAAQ,KAAK,CAAC,mBAAmB,CAAC;;;;;;;;;;;;;;;;;2BAxDlG,QAAQ,KAAK;;;;;;;;;;8BAgExB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAEV,8OAAC;4BAAE,WAAU;sCAAqB;;;;;;sCAGlC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;oCACZ,YAAY;wCAAE,OAAO;oCAAK;oCAC1B,UAAU;wCAAE,OAAO;oCAAK;oCACxB,WAAU;8CACX;;;;;;8CAGD,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;oCACZ,YAAY;wCAAE,OAAO;oCAAK;oCAC1B,UAAU;wCAAE,OAAO;oCAAK;oCACxB,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb;uCAEe", "debugId": null}}, {"offset": {"line": 4648, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Landing%20page/admybrand-ai-suite/src/components/sections/hero.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { But<PERSON> } from '@/components/ui/button'\r\nimport { motion } from 'framer-motion'\r\nimport { <PERSON>rk<PERSON> } from 'lucide-react'\r\n\r\nconst Hero = () => {\r\n  return (\r\n    <section className=\"relative min-h-screen bg-gradient-to-br from-black via-gray-900 to-black flex flex-col overflow-hidden\">\r\n      {/* Animated background elements */}\r\n      <div className=\"absolute inset-0 overflow-hidden\">\r\n        <div className=\"absolute top-1/4 left-1/4 w-96 h-96 bg-blue-500/10 rounded-full blur-3xl animate-pulse\"></div>\r\n        <div className=\"absolute bottom-1/4 right-1/4 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl animate-pulse delay-1000\"></div>\r\n        <div className=\"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[800px] h-[800px] bg-gradient-conic from-blue-500/5 via-purple-500/5 to-blue-500/5 rounded-full blur-3xl animate-spin-slow\"></div>\r\n      </div>\r\n\r\n      {/* Image Section - Modern Glass Card */}\r\n      <div className=\"flex items-center justify-center px-2 sm:px-4 lg:px-6 pt-24 pb-4 relative z-10\">\r\n        <motion.div\r\n          initial={{ opacity: 0, scale: 0.9, y: 20 }}\r\n          animate={{ opacity: 1, scale: 1, y: 0 }}\r\n          transition={{ duration: 1, ease: \"easeOut\" }}\r\n          className=\"relative w-full max-w-7xl h-[45vh] rounded-2xl overflow-hidden shadow-2xl border border-white/10 bg-white/5 backdrop-blur-xl\"\r\n        >\r\n          <div\r\n            className=\"absolute inset-0 bg-cover bg-center bg-no-repeat\"\r\n            style={{\r\n              backgroundImage: \"url('/office-background.jpg')\",\r\n            }}\r\n          />\r\n          {/* Modern gradient overlay */}\r\n          <div className=\"absolute inset-0 bg-gradient-to-br from-black/30 via-transparent to-purple-900/20\" />\r\n\r\n          {/* Floating badge */}\r\n          <motion.div\r\n            initial={{ opacity: 0, y: -20 }}\r\n            animate={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 0.8, delay: 0.5 }}\r\n            className=\"absolute top-4 left-4 bg-white/10 backdrop-blur-md border border-white/20 rounded-full px-4 py-2 flex items-center gap-2\"\r\n          >\r\n            <Sparkles className=\"w-4 h-4 text-yellow-400\" />\r\n            <span className=\"text-white text-sm font-medium\">AI-Powered</span>\r\n          </motion.div>\r\n        </motion.div>\r\n      </div>\r\n\r\n      {/* Modern floating notification cards */}\r\n      <motion.div\r\n        className=\"absolute top-16 left-[15%] bg-gradient-to-r from-blue-500/20 to-purple-500/20 backdrop-blur-md rounded-xl px-4 py-3 text-sm text-white border border-white/20 z-20 shadow-lg\"\r\n        animate={{\r\n          y: [0, -8, 0],\r\n          opacity: [0.8, 1, 0.8],\r\n          scale: [1, 1.02, 1],\r\n        }}\r\n        transition={{\r\n          duration: 3,\r\n          repeat: Infinity,\r\n          repeatType: \"reverse\",\r\n        }}\r\n      >\r\n        <div className=\"flex items-center gap-2\">\r\n          <div className=\"w-2 h-2 bg-green-400 rounded-full animate-pulse\"></div>\r\n          <span className=\"font-medium\">27 menus analyzed</span>\r\n        </div>\r\n      </motion.div>\r\n\r\n      <motion.div\r\n        className=\"absolute top-24 right-[15%] bg-gradient-to-r from-purple-500/20 to-pink-500/20 backdrop-blur-md rounded-xl px-4 py-3 text-sm text-white border border-white/20 z-20 shadow-lg\"\r\n        animate={{\r\n          y: [0, 8, 0],\r\n          opacity: [0.8, 1, 0.8],\r\n          scale: [1, 1.02, 1],\r\n        }}\r\n        transition={{\r\n          duration: 4,\r\n          repeat: Infinity,\r\n          repeatType: \"reverse\",\r\n          delay: 1,\r\n        }}\r\n      >\r\n        <div className=\"flex items-center gap-2\">\r\n          <div className=\"w-2 h-2 bg-orange-400 rounded-full animate-pulse\"></div>\r\n          <span className=\"font-medium\">4 churn risks detected</span>\r\n        </div>\r\n      </motion.div>\r\n\r\n      <motion.div\r\n        className=\"absolute top-32 left-[20%] bg-gradient-to-r from-green-500/20 to-blue-500/20 backdrop-blur-md rounded-xl px-4 py-3 text-sm text-white border border-white/20 z-20 shadow-lg\"\r\n        animate={{\r\n          y: [0, -6, 0],\r\n          opacity: [0.8, 1, 0.8],\r\n          scale: [1, 1.02, 1],\r\n        }}\r\n        transition={{\r\n          duration: 5,\r\n          repeat: Infinity,\r\n          repeatType: \"reverse\",\r\n          delay: 2,\r\n        }}\r\n      >\r\n        <div className=\"flex items-center gap-2\">\r\n          <div className=\"w-2 h-2 bg-blue-400 rounded-full animate-pulse\"></div>\r\n          <span className=\"font-medium\">13 emails automated</span>\r\n        </div>\r\n      </motion.div>\r\n\r\n      <motion.div\r\n        className=\"absolute top-40 right-[20%] bg-gradient-to-r from-yellow-500/20 to-orange-500/20 backdrop-blur-md rounded-xl px-4 py-3 text-sm text-white border border-white/20 z-20 shadow-lg\"\r\n        animate={{\r\n          y: [0, 10, 0],\r\n          opacity: [0.8, 1, 0.8],\r\n          scale: [1, 1.02, 1],\r\n        }}\r\n        transition={{\r\n          duration: 3.5,\r\n          repeat: Infinity,\r\n          repeatType: \"reverse\",\r\n          delay: 0.5,\r\n        }}\r\n      >\r\n        <div className=\"flex items-center gap-2\">\r\n          <div className=\"w-2 h-2 bg-purple-400 rounded-full animate-pulse\"></div>\r\n          <span className=\"font-medium\">34 orders processed</span>\r\n        </div>\r\n      </motion.div>\r\n\r\n      {/* Text Content Section Below Image */}\r\n      <div className=\"flex-1 flex flex-col justify-center px-2 sm:px-4 lg:px-6 pb-8 relative z-10\">\r\n        <div className=\"max-w-7xl mx-auto text-center\">\r\n          {/* Badge */}\r\n          <motion.div\r\n            initial={{ opacity: 0, y: 20 }}\r\n            animate={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 0.6 }}\r\n            className=\"inline-flex items-center gap-2 bg-white/10 backdrop-blur-md border border-white/20 rounded-full px-4 py-2 mb-6\"\r\n          >\r\n            <Sparkles className=\"w-4 h-4 text-yellow-400\" />\r\n            <span className=\"text-white text-sm font-medium\">Next-Gen AI Marketing</span>\r\n          </motion.div>\r\n\r\n          {/* Main Heading with Gradient Text */}\r\n          <motion.h1\r\n            initial={{ opacity: 0, y: 30 }}\r\n            animate={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 0.8, delay: 0.2 }}\r\n            className=\"text-3xl md:text-5xl lg:text-6xl font-bold mb-6 leading-tight\"\r\n          >\r\n            <span className=\"text-white\">All-in-One AI Marketing Suite</span>\r\n            <br />\r\n            <span className=\"bg-gradient-to-r from-blue-400 via-purple-400 to-pink-400 bg-clip-text text-transparent\">\r\n              to Grow Your Brand\r\n            </span>\r\n          </motion.h1>\r\n\r\n          {/* Subtitle */}\r\n          <motion.p\r\n            initial={{ opacity: 0, y: 30 }}\r\n            animate={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 0.8, delay: 0.4 }}\r\n            className=\"text-lg md:text-xl text-gray-300 mb-8 max-w-2xl mx-auto leading-relaxed\"\r\n          >\r\n            Automate content, campaigns, and customer insights — all in one\r\n            <span className=\"text-blue-400 font-semibold\"> intelligent marketing platform</span>.\r\n          </motion.p>\r\n\r\n          {/* CTA Buttons */}\r\n          <motion.div\r\n            initial={{ opacity: 0, y: 30 }}\r\n            animate={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 0.8, delay: 0.6 }}\r\n            className=\"flex flex-col sm:flex-row gap-4 justify-center items-center\"\r\n          >\r\n            <Button\r\n              size=\"lg\"\r\n              className=\"bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-8 py-4 text-lg font-semibold rounded-xl shadow-2xl transition-all duration-300 transform hover:scale-105 hover:shadow-blue-500/25 border border-white/10\"\r\n            >\r\n              <span className=\"flex items-center gap-2\">\r\n                BOOK A DEMO\r\n                <motion.div\r\n                  animate={{ x: [0, 4, 0] }}\r\n                  transition={{ duration: 1.5, repeat: Infinity }}\r\n                >\r\n                  →\r\n                </motion.div>\r\n              </span>\r\n            </Button>\r\n            <Button\r\n              variant=\"outline\"\r\n              size=\"lg\"\r\n              className=\"border-white/20 text-white hover:bg-white/10 backdrop-blur-md px-8 py-4 text-lg font-semibold rounded-xl transition-all duration-300 hover:border-white/30\"\r\n            >\r\n              Get Started Free\r\n            </Button>\r\n          </motion.div>\r\n\r\n          {/* Trust indicators */}\r\n          <motion.div\r\n            initial={{ opacity: 0 }}\r\n            animate={{ opacity: 1 }}\r\n            transition={{ duration: 0.8, delay: 0.8 }}\r\n            className=\"mt-8 flex flex-wrap justify-center items-center gap-6 text-sm text-gray-400\"\r\n          >\r\n            <div className=\"flex items-center gap-2\">\r\n              <div className=\"w-2 h-2 bg-green-400 rounded-full\"></div>\r\n              <span>No credit card required</span>\r\n            </div>\r\n            <div className=\"flex items-center gap-2\">\r\n              <div className=\"w-2 h-2 bg-blue-400 rounded-full\"></div>\r\n              <span>14-day free trial</span>\r\n            </div>\r\n            <div className=\"flex items-center gap-2\">\r\n              <div className=\"w-2 h-2 bg-purple-400 rounded-full\"></div>\r\n              <span>Setup in 5 minutes</span>\r\n            </div>\r\n          </motion.div>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  )\r\n}\r\n\r\nexport default Hero\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAMA,MAAM,OAAO;IACX,qBACE,8OAAC;QAAQ,WAAU;;0BAEjB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;;;;;;;0BAIjB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,OAAO;wBAAK,GAAG;oBAAG;oBACzC,SAAS;wBAAE,SAAS;wBAAG,OAAO;wBAAG,GAAG;oBAAE;oBACtC,YAAY;wBAAE,UAAU;wBAAG,MAAM;oBAAU;oBAC3C,WAAU;;sCAEV,8OAAC;4BACC,WAAU;4BACV,OAAO;gCACL,iBAAiB;4BACnB;;;;;;sCAGF,8OAAC;4BAAI,WAAU;;;;;;sCAGf,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG,CAAC;4BAAG;4BAC9B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,WAAU;;8CAEV,8OAAC,0MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;8CACpB,8OAAC;oCAAK,WAAU;8CAAiC;;;;;;;;;;;;;;;;;;;;;;;0BAMvD,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBACP,GAAG;wBAAC;wBAAG,CAAC;wBAAG;qBAAE;oBACb,SAAS;wBAAC;wBAAK;wBAAG;qBAAI;oBACtB,OAAO;wBAAC;wBAAG;wBAAM;qBAAE;gBACrB;gBACA,YAAY;oBACV,UAAU;oBACV,QAAQ;oBACR,YAAY;gBACd;0BAEA,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAK,WAAU;sCAAc;;;;;;;;;;;;;;;;;0BAIlC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBACP,GAAG;wBAAC;wBAAG;wBAAG;qBAAE;oBACZ,SAAS;wBAAC;wBAAK;wBAAG;qBAAI;oBACtB,OAAO;wBAAC;wBAAG;wBAAM;qBAAE;gBACrB;gBACA,YAAY;oBACV,UAAU;oBACV,QAAQ;oBACR,YAAY;oBACZ,OAAO;gBACT;0BAEA,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAK,WAAU;sCAAc;;;;;;;;;;;;;;;;;0BAIlC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBACP,GAAG;wBAAC;wBAAG,CAAC;wBAAG;qBAAE;oBACb,SAAS;wBAAC;wBAAK;wBAAG;qBAAI;oBACtB,OAAO;wBAAC;wBAAG;wBAAM;qBAAE;gBACrB;gBACA,YAAY;oBACV,UAAU;oBACV,QAAQ;oBACR,YAAY;oBACZ,OAAO;gBACT;0BAEA,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAK,WAAU;sCAAc;;;;;;;;;;;;;;;;;0BAIlC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBACP,GAAG;wBAAC;wBAAG;wBAAI;qBAAE;oBACb,SAAS;wBAAC;wBAAK;wBAAG;qBAAI;oBACtB,OAAO;wBAAC;wBAAG;wBAAM;qBAAE;gBACrB;gBACA,YAAY;oBACV,UAAU;oBACV,QAAQ;oBACR,YAAY;oBACZ,OAAO;gBACT;0BAEA,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAK,WAAU;sCAAc;;;;;;;;;;;;;;;;;0BAKlC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,WAAU;;8CAEV,8OAAC,0MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;8CACpB,8OAAC;oCAAK,WAAU;8CAAiC;;;;;;;;;;;;sCAInD,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;4BACR,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,WAAU;;8CAEV,8OAAC;oCAAK,WAAU;8CAAa;;;;;;8CAC7B,8OAAC;;;;;8CACD,8OAAC;oCAAK,WAAU;8CAA0F;;;;;;;;;;;;sCAM5G,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;4BACP,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,WAAU;;gCACX;8CAEC,8OAAC;oCAAK,WAAU;8CAA8B;;;;;;gCAAsC;;;;;;;sCAItF,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,WAAU;;8CAEV,8OAAC,kIAAA,CAAA,SAAM;oCACL,MAAK;oCACL,WAAU;8CAEV,cAAA,8OAAC;wCAAK,WAAU;;4CAA0B;0DAExC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,SAAS;oDAAE,GAAG;wDAAC;wDAAG;wDAAG;qDAAE;gDAAC;gDACxB,YAAY;oDAAE,UAAU;oDAAK,QAAQ;gDAAS;0DAC/C;;;;;;;;;;;;;;;;;8CAKL,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;sCAMH,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;4BAAE;4BACtB,SAAS;gCAAE,SAAS;4BAAE;4BACtB,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,WAAU;;8CAEV,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;sDAAK;;;;;;;;;;;;8CAER,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;sDAAK;;;;;;;;;;;;8CAER,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;sDAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOpB;uCAEe", "debugId": null}}, {"offset": {"line": 5283, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Landing%20page/admybrand-ai-suite/src/components/sections/logo-slider.tsx"], "sourcesContent": ["'use client'\n\nimport { motion } from 'framer-motion'\nimport { useState } from 'react'\n\nconst LogoSlider = () => {\n  const [isPaused, setIsPaused] = useState(false)\n\n  // AI Marketing and Tech companies\n  const logos = [\n    { name: 'HubSpot', style: 'normal', weight: 'semibold' },\n    { name: 'Mailchi<PERSON>', style: 'normal', weight: 'medium' },\n    { name: 'Salesforce', style: 'normal', weight: 'bold' },\n    { name: '<PERSON><PERSON>', style: 'normal', weight: 'medium' },\n    { name: 'Hootsuite', style: 'italic', weight: 'light' },\n    { name: 'Buffer', style: 'normal', weight: 'semibold' },\n    { name: '<PERSON>va', style: 'normal', weight: 'bold' },\n    { name: 'Klaviyo', style: 'normal', weight: 'medium' },\n    { name: '<PERSON>', style: 'italic', weight: 'normal' },\n    { name: 'Copy.ai', style: 'normal', weight: 'semibold' },\n    { name: 'Drift', style: 'normal', weight: 'bold' },\n    { name: 'Intercom', style: 'normal', weight: 'medium' },\n  ]\n\n  // Duplicate logos for seamless infinite scroll\n  const duplicatedLogos = [...logos, ...logos]\n\n  return (\n    <section className=\"py-4 bg-black\">\n      <div className=\"max-w-7xl mx-auto px-2 sm:px-4 lg:px-6\">\n\n\n        {/* Logo Slider Container */}\n        <div className=\"relative overflow-hidden py-8\">\n          {/* Enhanced gradient overlays */}\n          <div className=\"absolute left-0 top-0 bottom-0 w-32 bg-gradient-to-r from-black via-black/80 to-transparent z-10\" />\n          <div className=\"absolute right-0 top-0 bottom-0 w-32 bg-gradient-to-l from-black via-black/80 to-transparent z-10\" />\n\n          {/* Subtle background glow */}\n          <div className=\"absolute inset-0 bg-gradient-to-r from-transparent via-blue-500/5 to-transparent\" />\n          \n          {/* Scrolling logos */}\n          <motion.div\n            className=\"flex items-center gap-16 md:gap-20 lg:gap-24\"\n            animate={isPaused ? {} : {\n              x: [0, -50 + '%'],\n            }}\n            transition={{\n              x: {\n                repeat: Infinity,\n                repeatType: \"loop\",\n                duration: 40,\n                ease: \"linear\",\n              },\n            }}\n            style={{\n              width: `${duplicatedLogos.length * 240}px`,\n            }}\n            onHoverStart={() => setIsPaused(true)}\n            onHoverEnd={() => setIsPaused(false)}\n          >\n            {duplicatedLogos.map((logo, index) => (\n              <motion.div\n                key={`${logo.name}-${index}`}\n                className=\"flex-shrink-0 w-40 h-20 md:w-48 md:h-24 flex items-center justify-center\"\n                whileHover={{\n                  scale: 1.05,\n                  y: -3,\n                }}\n                transition={{ duration: 0.4, ease: \"easeOut\" }}\n              >\n                {/* Glassmorphism logo container */}\n                <div className=\"relative w-full h-full flex items-center justify-center group cursor-pointer\">\n                  {/* Glassmorphism background - always visible */}\n                  <div className=\"absolute inset-0 bg-gradient-to-br from-white/10 via-white/5 to-white/10 backdrop-blur-md rounded-2xl border border-white/20 shadow-xl group-hover:shadow-2xl group-hover:shadow-blue-500/20 transition-all duration-500\" />\n\n                  {/* Inner glow effect */}\n                  <div className=\"absolute inset-0 bg-gradient-to-br from-blue-500/10 via-transparent to-purple-500/10 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-500\" />\n\n                  {/* Subtle border highlight */}\n                  <div className=\"absolute inset-0 rounded-2xl border border-gradient-to-br from-blue-400/30 via-transparent to-purple-400/30 opacity-0 group-hover:opacity-100 transition-opacity duration-500\" />\n\n                  {/* Logo text */}\n                  <span\n                    className={`\n                      relative z-10 text-gray-300 text-base md:text-lg\n                      group-hover:text-white transition-all duration-500\n                      whitespace-nowrap tracking-wide px-4 py-2\n                      ${logo.style === 'italic' ? 'italic' : ''}\n                      ${logo.weight === 'light' ? 'font-light' : ''}\n                      ${logo.weight === 'normal' ? 'font-normal' : ''}\n                      ${logo.weight === 'medium' ? 'font-medium' : ''}\n                      ${logo.weight === 'semibold' ? 'font-semibold' : ''}\n                      ${logo.weight === 'bold' ? 'font-bold' : ''}\n                      ${logo.weight === 'black' ? 'font-black' : ''}\n                    `}\n                    style={{\n                      filter: 'drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3))',\n                      transition: 'all 0.5s ease',\n                    }}\n                    onMouseEnter={(e) => {\n                      e.target.style.filter = 'drop-shadow(0 0 12px rgba(59, 130, 246, 0.8)) drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3))';\n                    }}\n                    onMouseLeave={(e) => {\n                      e.target.style.filter = 'drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3))';\n                    }}\n                  >\n                    {logo.name}\n                  </span>\n                </div>\n\n                {/* Uncomment this when you have actual logo images */}\n                {/*\n                <img\n                  src={logo.url}\n                  alt={logo.name}\n                  className=\"w-full h-full object-contain filter grayscale hover:grayscale-0 transition-all duration-300 opacity-60 hover:opacity-100\"\n                />\n                */}\n              </motion.div>\n            ))}\n          </motion.div>\n        </div>\n      </div>\n    </section>\n  )\n}\n\nexport default LogoSlider\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKA,MAAM,aAAa;IACjB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,kCAAkC;IAClC,MAAM,QAAQ;QACZ;YAAE,MAAM;YAAW,OAAO;YAAU,QAAQ;QAAW;QACvD;YAAE,MAAM;YAAa,OAAO;YAAU,QAAQ;QAAS;QACvD;YAAE,MAAM;YAAc,OAAO;YAAU,QAAQ;QAAO;QACtD;YAAE,MAAM;YAAW,OAAO;YAAU,QAAQ;QAAS;QACrD;YAAE,MAAM;YAAa,OAAO;YAAU,QAAQ;QAAQ;QACtD;YAAE,MAAM;YAAU,OAAO;YAAU,QAAQ;QAAW;QACtD;YAAE,MAAM;YAAS,OAAO;YAAU,QAAQ;QAAO;QACjD;YAAE,MAAM;YAAW,OAAO;YAAU,QAAQ;QAAS;QACrD;YAAE,MAAM;YAAU,OAAO;YAAU,QAAQ;QAAS;QACpD;YAAE,MAAM;YAAW,OAAO;YAAU,QAAQ;QAAW;QACvD;YAAE,MAAM;YAAS,OAAO;YAAU,QAAQ;QAAO;QACjD;YAAE,MAAM;YAAY,OAAO;YAAU,QAAQ;QAAS;KACvD;IAED,+CAA+C;IAC/C,MAAM,kBAAkB;WAAI;WAAU;KAAM;IAE5C,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;sBAIb,cAAA,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;kCAGf,8OAAC;wBAAI,WAAU;;;;;;kCAGf,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS,WAAW,CAAC,IAAI;4BACvB,GAAG;gCAAC;gCAAG,CAAC,KAAK;6BAAI;wBACnB;wBACA,YAAY;4BACV,GAAG;gCACD,QAAQ;gCACR,YAAY;gCACZ,UAAU;gCACV,MAAM;4BACR;wBACF;wBACA,OAAO;4BACL,OAAO,GAAG,gBAAgB,MAAM,GAAG,IAAI,EAAE,CAAC;wBAC5C;wBACA,cAAc,IAAM,YAAY;wBAChC,YAAY,IAAM,YAAY;kCAE7B,gBAAgB,GAAG,CAAC,CAAC,MAAM,sBAC1B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,WAAU;gCACV,YAAY;oCACV,OAAO;oCACP,GAAG,CAAC;gCACN;gCACA,YAAY;oCAAE,UAAU;oCAAK,MAAM;gCAAU;0CAG7C,cAAA,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC;4CAAI,WAAU;;;;;;sDAGf,8OAAC;4CAAI,WAAU;;;;;;sDAGf,8OAAC;4CAAI,WAAU;;;;;;sDAGf,8OAAC;4CACC,WAAW,CAAC;;;;sBAIV,EAAE,KAAK,KAAK,KAAK,WAAW,WAAW,GAAG;sBAC1C,EAAE,KAAK,MAAM,KAAK,UAAU,eAAe,GAAG;sBAC9C,EAAE,KAAK,MAAM,KAAK,WAAW,gBAAgB,GAAG;sBAChD,EAAE,KAAK,MAAM,KAAK,WAAW,gBAAgB,GAAG;sBAChD,EAAE,KAAK,MAAM,KAAK,aAAa,kBAAkB,GAAG;sBACpD,EAAE,KAAK,MAAM,KAAK,SAAS,cAAc,GAAG;sBAC5C,EAAE,KAAK,MAAM,KAAK,UAAU,eAAe,GAAG;oBAChD,CAAC;4CACD,OAAO;gDACL,QAAQ;gDACR,YAAY;4CACd;4CACA,cAAc,CAAC;gDACb,EAAE,MAAM,CAAC,KAAK,CAAC,MAAM,GAAG;4CAC1B;4CACA,cAAc,CAAC;gDACb,EAAE,MAAM,CAAC,KAAK,CAAC,MAAM,GAAG;4CAC1B;sDAEC,KAAK,IAAI;;;;;;;;;;;;+BA5CT,GAAG,KAAK,IAAI,CAAC,CAAC,EAAE,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;AA+D5C;uCAEe", "debugId": null}}, {"offset": {"line": 5513, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Landing%20page/admybrand-ai-suite/src/components/ui/avatar.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst avatarVariants = cva(\n  \"relative flex shrink-0 overflow-hidden rounded-full\",\n  {\n    variants: {\n      size: {\n        sm: \"h-8 w-8\",\n        default: \"h-10 w-10\",\n        lg: \"h-12 w-12\",\n        xl: \"h-16 w-16\",\n        \"2xl\": \"h-20 w-20\",\n      },\n    },\n    defaultVariants: {\n      size: \"default\",\n    },\n  }\n)\n\nexport interface AvatarProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof avatarVariants> {\n  src?: string\n  alt?: string\n  fallback?: string\n}\n\nconst Avatar = React.forwardRef<HTMLDivElement, AvatarProps>(\n  ({ className, size, src, alt, fallback, ...props }, ref) => {\n    const [imageError, setImageError] = React.useState(false)\n\n    const handleImageError = () => {\n      setImageError(true)\n    }\n\n    return (\n      <div\n        ref={ref}\n        className={cn(avatarVariants({ size, className }))}\n        {...props}\n      >\n        {src && !imageError ? (\n          <img\n            className=\"aspect-square h-full w-full object-cover\"\n            src={src}\n            alt={alt}\n            onError={handleImageError}\n          />\n        ) : (\n          <div className=\"flex h-full w-full items-center justify-center rounded-full bg-muted\">\n            <span className=\"text-sm font-medium text-muted-foreground\">\n              {fallback || alt?.charAt(0)?.toUpperCase() || \"?\"}\n            </span>\n          </div>\n        )}\n      </div>\n    )\n  }\n)\nAvatar.displayName = \"Avatar\"\n\nexport { Avatar, avatarVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,uDACA;IACE,UAAU;QACR,MAAM;YACJ,IAAI;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,OAAO;QACT;IACF;IACA,iBAAiB;QACf,MAAM;IACR;AACF;AAWF,MAAM,uBAAS,qMAAA,CAAA,aAAgB,CAC7B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IAClD,MAAM,CAAC,YAAY,cAAc,GAAG,qMAAA,CAAA,WAAc,CAAC;IAEnD,MAAM,mBAAmB;QACvB,cAAc;IAChB;IAEA,qBACE,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAM;QAAU;QAC9C,GAAG,KAAK;kBAER,OAAO,CAAC,2BACP,8OAAC;YACC,WAAU;YACV,KAAK;YACL,KAAK;YACL,SAAS;;;;;qEAGX,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAK,WAAU;0BACb,YAAY,KAAK,OAAO,IAAI,iBAAiB;;;;;;;;;;;;;;;;AAM1D;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 5587, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Landing%20page/admybrand-ai-suite/src/components/sections/testimonials.tsx"], "sourcesContent": ["'use client'\n\nimport { Avatar } from '@/components/ui/avatar'\nimport { Badge } from '@/components/ui/badge'\nimport { <PERSON><PERSON> } from '@/components/ui/button'\nimport { Card, CardContent } from '@/components/ui/card'\nimport { AnimatePresence, motion } from 'framer-motion'\nimport { ChevronLeft, ChevronRight, Quote, Star } from 'lucide-react'\nimport { useEffect, useState } from 'react'\n\nconst testimonials = [\n  {\n    id: 1,\n    name: \"<PERSON>\",\n    role: \"Marketing Director\",\n    company: \"TechFlow Inc.\",\n    avatar: \"https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face\",\n    rating: 5,\n    content: \"ADmyBRAND AI Suite transformed our marketing completely. We've seen a 300% increase in engagement and our content creation time has been cut by 80%. The AI understands our brand voice perfectly.\",\n    results: \"300% engagement increase\",\n    logo: \"🚀\"\n  },\n  {\n    id: 2,\n    name: \"<PERSON>\",\n    role: \"CEO\",\n    company: \"GrowthLab\",\n    avatar: \"https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face\",\n    rating: 5,\n    content: \"The ROI we've achieved with ADmyBRAND is incredible. Our ad campaigns are now 10x more effective, and the automated optimization saves us countless hours every week.\",\n    results: \"10x ad effectiveness\",\n    logo: \"📈\"\n  },\n  {\n    id: 3,\n    name: \"Emily Watson\",\n    role: \"Content Manager\",\n    company: \"Creative Studios\",\n    avatar: \"https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face\",\n    rating: 5,\n    content: \"As a small team, we needed something that could scale with us. ADmyBRAND's AI tools let us compete with much larger agencies. The quality of generated content is outstanding.\",\n    results: \"5x content output\",\n    logo: \"🎨\"\n  },\n  {\n    id: 4,\n    name: \"David Kim\",\n    role: \"Digital Marketing Lead\",\n    company: \"E-commerce Plus\",\n    avatar: \"https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face\",\n    rating: 5,\n    content: \"The email marketing automation alone has paid for itself. Our open rates increased by 150% and conversions by 200%. The AI personalization is game-changing.\",\n    results: \"200% conversion increase\",\n    logo: \"💼\"\n  },\n  {\n    id: 5,\n    name: \"Lisa Thompson\",\n    role: \"Brand Manager\",\n    company: \"Fashion Forward\",\n    avatar: \"https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=150&h=150&fit=crop&crop=face\",\n    rating: 5,\n    content: \"Managing multiple brand voices was a nightmare before ADmyBRAND. Now our AI maintains perfect consistency across all our brands while adapting to each unique style.\",\n    results: \"Perfect brand consistency\",\n    logo: \"👗\"\n  },\n  {\n    id: 6,\n    name: \"Alex Johnson\",\n    role: \"Startup Founder\",\n    company: \"InnovateTech\",\n    avatar: \"https://images.unsplash.com/photo-1560250097-0b93528c311a?w=150&h=150&fit=crop&crop=face\",\n    rating: 5,\n    content: \"Starting with limited marketing budget, ADmyBRAND helped us achieve enterprise-level results. We've grown from 0 to 100K followers in just 6 months.\",\n    results: \"0 to 100K followers\",\n    logo: \"💡\"\n  }\n]\n\nconst Testimonials = () => {\n  const [currentIndex, setCurrentIndex] = useState(0)\n  const [isAutoPlaying, setIsAutoPlaying] = useState(true)\n\n  // Auto-play functionality\n  useEffect(() => {\n    if (!isAutoPlaying) return\n\n    const interval = setInterval(() => {\n      setCurrentIndex((prev) => (prev + 1) % testimonials.length)\n    }, 5000)\n\n    return () => clearInterval(interval)\n  }, [isAutoPlaying])\n\n  const nextTestimonial = () => {\n    setCurrentIndex((prev) => (prev + 1) % testimonials.length)\n  }\n\n  const prevTestimonial = () => {\n    setCurrentIndex((prev) => (prev - 1 + testimonials.length) % testimonials.length)\n  }\n\n  const goToTestimonial = (index: number) => {\n    setCurrentIndex(index)\n  }\n\n  return (\n    <section className=\"py-24 bg-black\">\n      <div className=\"max-w-7xl mx-auto px-2 sm:px-4 lg:px-6\">\n        {/* Section Header */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          viewport={{ once: true }}\n          className=\"text-center mb-16\"\n        >\n          <Badge className=\"mb-4 bg-white/10 text-white border-white/20 backdrop-blur-sm\">\n            Customer Success\n          </Badge>\n          <h2 className=\"text-4xl md:text-5xl font-bold text-white mb-6\">\n            Loved by\n            <span className=\"bg-gradient-to-r from-blue-400 to-cyan-400 bg-clip-text text-transparent\"> marketing teams</span>\n            <br />worldwide\n          </h2>\n          <p className=\"text-xl text-gray-300 max-w-3xl mx-auto\">\n            Join thousands of businesses that have transformed their marketing with ADmyBRAND AI Suite.\n          </p>\n        </motion.div>\n\n        {/* Main Testimonial Display */}\n        <div className=\"relative mb-12\">\n          <div \n            className=\"flex items-center justify-center\"\n            onMouseEnter={() => setIsAutoPlaying(false)}\n            onMouseLeave={() => setIsAutoPlaying(true)}\n          >\n            <AnimatePresence mode=\"wait\">\n              <motion.div\n                key={currentIndex}\n                initial={{ opacity: 0, x: 100 }}\n                animate={{ opacity: 1, x: 0 }}\n                exit={{ opacity: 0, x: -100 }}\n                transition={{ duration: 0.5 }}\n                className=\"w-full max-w-4xl\"\n              >\n                <Card className=\"border border-white/10 shadow-2xl bg-white/5 backdrop-blur-md\">\n                  <CardContent className=\"p-8 md:p-12\">\n                    <div className=\"flex flex-col md:flex-row items-center gap-8\">\n                      {/* Quote Icon */}\n                      <div className=\"flex-shrink-0\">\n                        <div className=\"w-16 h-16 bg-white/10 backdrop-blur-sm border border-white/20 rounded-full flex items-center justify-center\">\n                          <Quote className=\"w-8 h-8 text-white\" />\n                        </div>\n                      </div>\n\n                      {/* Content */}\n                      <div className=\"flex-1 text-center md:text-left\">\n                        {/* Stars */}\n                        <div className=\"flex justify-center md:justify-start mb-4\">\n                          {[...Array(testimonials[currentIndex].rating)].map((_, i) => (\n                            <Star key={i} className=\"w-5 h-5 text-yellow-400 fill-current\" />\n                          ))}\n                        </div>\n\n                        {/* Quote */}\n                        <blockquote className=\"text-xl md:text-2xl text-white mb-6 leading-relaxed\">\n                          \"{testimonials[currentIndex].content}\"\n                        </blockquote>\n\n                        {/* Results Badge */}\n                        <Badge className=\"mb-6 bg-blue-500/20 text-blue-400 border-blue-500/30\">\n                          {testimonials[currentIndex].results}\n                        </Badge>\n\n                        {/* Author Info */}\n                        <div className=\"flex items-center justify-center md:justify-start gap-4\">\n                          <Avatar\n                            src={testimonials[currentIndex].avatar}\n                            alt={testimonials[currentIndex].name}\n                            size=\"lg\"\n                          />\n                          <div>\n                            <div className=\"font-semibold text-white\">\n                              {testimonials[currentIndex].name}\n                            </div>\n                            <div className=\"text-gray-300\">\n                              {testimonials[currentIndex].role}\n                            </div>\n                            <div className=\"flex items-center gap-2 text-sm text-gray-400\">\n                              <span>{testimonials[currentIndex].logo}</span>\n                              {testimonials[currentIndex].company}\n                            </div>\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                  </CardContent>\n                </Card>\n              </motion.div>\n            </AnimatePresence>\n          </div>\n\n          {/* Navigation Buttons */}\n          <Button\n            variant=\"ghost\"\n            size=\"icon\"\n            onClick={prevTestimonial}\n            className=\"absolute left-4 top-1/2 transform -translate-y-1/2 bg-white/80 backdrop-blur-sm shadow-lg hover:bg-white\"\n          >\n            <ChevronLeft className=\"w-6 h-6\" />\n          </Button>\n          <Button\n            variant=\"ghost\"\n            size=\"icon\"\n            onClick={nextTestimonial}\n            className=\"absolute right-4 top-1/2 transform -translate-y-1/2 bg-white/80 backdrop-blur-sm shadow-lg hover:bg-white\"\n          >\n            <ChevronRight className=\"w-6 h-6\" />\n          </Button>\n        </div>\n\n        {/* Dots Indicator */}\n        <div className=\"flex justify-center space-x-2 mb-12\">\n          {testimonials.map((_, index) => (\n            <button\n              key={index}\n              onClick={() => goToTestimonial(index)}\n              className={`w-3 h-3 rounded-full transition-all duration-300 ${\n                index === currentIndex\n                  ? 'bg-purple-600 scale-125'\n                  : 'bg-gray-300 hover:bg-gray-400'\n              }`}\n            />\n          ))}\n        </div>\n\n        {/* Stats Section */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          viewport={{ once: true }}\n          className=\"grid grid-cols-2 md:grid-cols-4 gap-8 text-center\"\n        >\n          <div>\n            <div className=\"text-3xl md:text-4xl font-bold text-purple-600 mb-2\">50K+</div>\n            <div className=\"text-gray-600\">Happy Customers</div>\n          </div>\n          <div>\n            <div className=\"text-3xl md:text-4xl font-bold text-blue-600 mb-2\">98%</div>\n            <div className=\"text-gray-600\">Satisfaction Rate</div>\n          </div>\n          <div>\n            <div className=\"text-3xl md:text-4xl font-bold text-green-600 mb-2\">5M+</div>\n            <div className=\"text-gray-600\">Content Pieces</div>\n          </div>\n          <div>\n            <div className=\"text-3xl md:text-4xl font-bold text-orange-600 mb-2\">24/7</div>\n            <div className=\"text-gray-600\">Support</div>\n          </div>\n        </motion.div>\n      </div>\n    </section>\n  )\n}\n\nexport default Testimonials\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AARA;;;;;;;;;AAUA,MAAM,eAAe;IACnB;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,SAAS;QACT,QAAQ;QACR,QAAQ;QACR,SAAS;QACT,SAAS;QACT,MAAM;IACR;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,SAAS;QACT,QAAQ;QACR,QAAQ;QACR,SAAS;QACT,SAAS;QACT,MAAM;IACR;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,SAAS;QACT,QAAQ;QACR,QAAQ;QACR,SAAS;QACT,SAAS;QACT,MAAM;IACR;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,SAAS;QACT,QAAQ;QACR,QAAQ;QACR,SAAS;QACT,SAAS;QACT,MAAM;IACR;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,SAAS;QACT,QAAQ;QACR,QAAQ;QACR,SAAS;QACT,SAAS;QACT,MAAM;IACR;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,SAAS;QACT,QAAQ;QACR,QAAQ;QACR,SAAS;QACT,SAAS;QACT,MAAM;IACR;CACD;AAED,MAAM,eAAe;IACnB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,0BAA0B;IAC1B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,eAAe;QAEpB,MAAM,WAAW,YAAY;YAC3B,gBAAgB,CAAC,OAAS,CAAC,OAAO,CAAC,IAAI,aAAa,MAAM;QAC5D,GAAG;QAEH,OAAO,IAAM,cAAc;IAC7B,GAAG;QAAC;KAAc;IAElB,MAAM,kBAAkB;QACtB,gBAAgB,CAAC,OAAS,CAAC,OAAO,CAAC,IAAI,aAAa,MAAM;IAC5D;IAEA,MAAM,kBAAkB;QACtB,gBAAgB,CAAC,OAAS,CAAC,OAAO,IAAI,aAAa,MAAM,IAAI,aAAa,MAAM;IAClF;IAEA,MAAM,kBAAkB,CAAC;QACvB,gBAAgB;IAClB;IAEA,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAEV,8OAAC,iIAAA,CAAA,QAAK;4BAAC,WAAU;sCAA+D;;;;;;sCAGhF,8OAAC;4BAAG,WAAU;;gCAAiD;8CAE7D,8OAAC;oCAAK,WAAU;8CAA2E;;;;;;8CAC3F,8OAAC;;;;;gCAAK;;;;;;;sCAER,8OAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;8BAMzD,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BACC,WAAU;4BACV,cAAc,IAAM,iBAAiB;4BACrC,cAAc,IAAM,iBAAiB;sCAErC,cAAA,8OAAC,yLAAA,CAAA,kBAAe;gCAAC,MAAK;0CACpB,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAI;oCAC9B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,MAAM;wCAAE,SAAS;wCAAG,GAAG,CAAC;oCAAI;oCAC5B,YAAY;wCAAE,UAAU;oCAAI;oCAC5B,WAAU;8CAEV,cAAA,8OAAC,gIAAA,CAAA,OAAI;wCAAC,WAAU;kDACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;4CAAC,WAAU;sDACrB,cAAA,8OAAC;gDAAI,WAAU;;kEAEb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;;;;;;;;;;;kEAKrB,8OAAC;wDAAI,WAAU;;0EAEb,8OAAC;gEAAI,WAAU;0EACZ;uEAAI,MAAM,YAAY,CAAC,aAAa,CAAC,MAAM;iEAAE,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrD,8OAAC,kMAAA,CAAA,OAAI;wEAAS,WAAU;uEAAb;;;;;;;;;;0EAKf,8OAAC;gEAAW,WAAU;;oEAAsD;oEACxE,YAAY,CAAC,aAAa,CAAC,OAAO;oEAAC;;;;;;;0EAIvC,8OAAC,iIAAA,CAAA,QAAK;gEAAC,WAAU;0EACd,YAAY,CAAC,aAAa,CAAC,OAAO;;;;;;0EAIrC,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,kIAAA,CAAA,SAAM;wEACL,KAAK,YAAY,CAAC,aAAa,CAAC,MAAM;wEACtC,KAAK,YAAY,CAAC,aAAa,CAAC,IAAI;wEACpC,MAAK;;;;;;kFAEP,8OAAC;;0FACC,8OAAC;gFAAI,WAAU;0FACZ,YAAY,CAAC,aAAa,CAAC,IAAI;;;;;;0FAElC,8OAAC;gFAAI,WAAU;0FACZ,YAAY,CAAC,aAAa,CAAC,IAAI;;;;;;0FAElC,8OAAC;gFAAI,WAAU;;kGACb,8OAAC;kGAAM,YAAY,CAAC,aAAa,CAAC,IAAI;;;;;;oFACrC,YAAY,CAAC,aAAa,CAAC,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;mCApD5C;;;;;;;;;;;;;;;sCAiEX,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,SAAS;4BACT,WAAU;sCAEV,cAAA,8OAAC,oNAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;;;;;;sCAEzB,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,SAAS;4BACT,WAAU;sCAEV,cAAA,8OAAC,sNAAA,CAAA,eAAY;gCAAC,WAAU;;;;;;;;;;;;;;;;;8BAK5B,8OAAC;oBAAI,WAAU;8BACZ,aAAa,GAAG,CAAC,CAAC,GAAG,sBACpB,8OAAC;4BAEC,SAAS,IAAM,gBAAgB;4BAC/B,WAAW,CAAC,iDAAiD,EAC3D,UAAU,eACN,4BACA,iCACJ;2BANG;;;;;;;;;;8BAYX,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAEV,8OAAC;;8CACC,8OAAC;oCAAI,WAAU;8CAAsD;;;;;;8CACrE,8OAAC;oCAAI,WAAU;8CAAgB;;;;;;;;;;;;sCAEjC,8OAAC;;8CACC,8OAAC;oCAAI,WAAU;8CAAoD;;;;;;8CACnE,8OAAC;oCAAI,WAAU;8CAAgB;;;;;;;;;;;;sCAEjC,8OAAC;;8CACC,8OAAC;oCAAI,WAAU;8CAAqD;;;;;;8CACpE,8OAAC;oCAAI,WAAU;8CAAgB;;;;;;;;;;;;sCAEjC,8OAAC;;8CACC,8OAAC;oCAAI,WAAU;8CAAsD;;;;;;8CACrE,8OAAC;oCAAI,WAAU;8CAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM3C;uCAEe", "debugId": null}}, {"offset": {"line": 6149, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Landing%20page/admybrand-ai-suite/src/components/ui/floating-action-button.tsx"], "sourcesContent": ["'use client'\n\nimport { motion, AnimatePresence } from 'framer-motion'\nimport { MessageCircle, X, Phone, Mail, Calendar } from 'lucide-react'\nimport { useState } from 'react'\n\nconst FloatingActionButton = () => {\n  const [isOpen, setIsOpen] = useState(false)\n\n  const actions = [\n    {\n      icon: Calendar,\n      label: 'Book Demo',\n      color: 'from-blue-500 to-purple-500',\n      action: () => console.log('Book demo')\n    },\n    {\n      icon: Phone,\n      label: 'Call Us',\n      color: 'from-green-500 to-teal-500',\n      action: () => console.log('Call us')\n    },\n    {\n      icon: Mail,\n      label: 'Email',\n      color: 'from-orange-500 to-red-500',\n      action: () => console.log('Email')\n    }\n  ]\n\n  return (\n    <div className=\"fixed bottom-6 right-6 z-50\">\n      {/* Action buttons */}\n      <AnimatePresence>\n        {isOpen && (\n          <motion.div\n            initial={{ opacity: 0, scale: 0.8 }}\n            animate={{ opacity: 1, scale: 1 }}\n            exit={{ opacity: 0, scale: 0.8 }}\n            transition={{ duration: 0.2 }}\n            className=\"absolute bottom-16 right-0 space-y-3\"\n          >\n            {actions.map((action, index) => (\n              <motion.button\n                key={action.label}\n                initial={{ opacity: 0, x: 20 }}\n                animate={{ opacity: 1, x: 0 }}\n                exit={{ opacity: 0, x: 20 }}\n                transition={{ duration: 0.2, delay: index * 0.05 }}\n                onClick={action.action}\n                className={`flex items-center gap-3 bg-gradient-to-r ${action.color} text-white px-4 py-3 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 group`}\n                whileHover={{ scale: 1.05 }}\n                whileTap={{ scale: 0.95 }}\n              >\n                <action.icon className=\"w-5 h-5\" />\n                <span className=\"text-sm font-medium whitespace-nowrap\">\n                  {action.label}\n                </span>\n              </motion.button>\n            ))}\n          </motion.div>\n        )}\n      </AnimatePresence>\n\n      {/* Main FAB */}\n      <motion.button\n        onClick={() => setIsOpen(!isOpen)}\n        className=\"w-14 h-14 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center\"\n        whileHover={{ scale: 1.1 }}\n        whileTap={{ scale: 0.9 }}\n        animate={{ rotate: isOpen ? 45 : 0 }}\n      >\n        {isOpen ? (\n          <X className=\"w-6 h-6\" />\n        ) : (\n          <MessageCircle className=\"w-6 h-6\" />\n        )}\n      </motion.button>\n    </div>\n  )\n}\n\nexport default FloatingActionButton\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAJA;;;;;AAMA,MAAM,uBAAuB;IAC3B,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,MAAM,UAAU;QACd;YACE,MAAM,0MAAA,CAAA,WAAQ;YACd,OAAO;YACP,OAAO;YACP,QAAQ,IAAM,QAAQ,GAAG,CAAC;QAC5B;QACA;YACE,MAAM,oMAAA,CAAA,QAAK;YACX,OAAO;YACP,OAAO;YACP,QAAQ,IAAM,QAAQ,GAAG,CAAC;QAC5B;QACA;YACE,MAAM,kMAAA,CAAA,OAAI;YACV,OAAO;YACP,OAAO;YACP,QAAQ,IAAM,QAAQ,GAAG,CAAC;QAC5B;KACD;IAED,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC,yLAAA,CAAA,kBAAe;0BACb,wBACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,OAAO;oBAAI;oBAClC,SAAS;wBAAE,SAAS;wBAAG,OAAO;oBAAE;oBAChC,MAAM;wBAAE,SAAS;wBAAG,OAAO;oBAAI;oBAC/B,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,WAAU;8BAET,QAAQ,GAAG,CAAC,CAAC,QAAQ,sBACpB,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;4BAEZ,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,MAAM;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC1B,YAAY;gCAAE,UAAU;gCAAK,OAAO,QAAQ;4BAAK;4BACjD,SAAS,OAAO,MAAM;4BACtB,WAAW,CAAC,yCAAyC,EAAE,OAAO,KAAK,CAAC,8FAA8F,CAAC;4BACnK,YAAY;gCAAE,OAAO;4BAAK;4BAC1B,UAAU;gCAAE,OAAO;4BAAK;;8CAExB,8OAAC,OAAO,IAAI;oCAAC,WAAU;;;;;;8CACvB,8OAAC;oCAAK,WAAU;8CACb,OAAO,KAAK;;;;;;;2BAZV,OAAO,KAAK;;;;;;;;;;;;;;;0BAqB3B,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;gBACZ,SAAS,IAAM,UAAU,CAAC;gBAC1B,WAAU;gBACV,YAAY;oBAAE,OAAO;gBAAI;gBACzB,UAAU;oBAAE,OAAO;gBAAI;gBACvB,SAAS;oBAAE,QAAQ,SAAS,KAAK;gBAAE;0BAElC,uBACC,8OAAC,4LAAA,CAAA,IAAC;oBAAC,WAAU;;;;;6EAEb,8OAAC,wNAAA,CAAA,gBAAa;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAKnC;uCAEe", "debugId": null}}]}