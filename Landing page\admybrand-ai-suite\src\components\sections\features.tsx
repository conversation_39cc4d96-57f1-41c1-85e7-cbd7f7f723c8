'use client'

import { Badge } from '@/components/ui/badge'
import { Card, CardContent } from '@/components/ui/card'
import { motion } from 'framer-motion'
import {
    BarChart3,
    Mail,
    MessageSquare,
    Monitor,
    PenTool,
    Search,
    Target,
    Zap
} from 'lucide-react'

const features = [
  {
    icon: PenTool,
    title: "AI Content Generator",
    description: "Create compelling blog posts, social media content, and ad copy in seconds. Our AI understands your brand voice and generates content that converts.",
    badge: "Most Popular",
    color: "from-purple-500 to-pink-500"
  },
  {
    icon: MessageSquare,
    title: "Brand Voice Analyzer",
    description: "Maintain consistent messaging across all platforms. Our AI learns your brand's unique voice and ensures every piece of content matches your style.",
    badge: "New",
    color: "from-blue-500 to-cyan-500"
  },
  {
    icon: BarChart3,
    title: "Social Media Manager",
    description: "Automate your social media presence with intelligent posting, engagement tracking, and audience analysis. Schedule content across all platforms.",
    badge: "Trending",
    color: "from-green-500 to-emerald-500"
  },
  {
    icon: Target,
    title: "Ad Campaign Optimizer",
    description: "Maximize your ad spend with AI-powered optimization. Automatically adjust targeting, bidding, and creative elements for better performance.",
    badge: "Pro",
    color: "from-orange-500 to-red-500"
  },
  {
    icon: Mail,
    title: "Email Marketing AI",
    description: "Personalize email campaigns at scale. Generate subject lines, content, and send times that increase open rates and conversions.",
    badge: "Essential",
    color: "from-indigo-500 to-purple-500"
  },
  {
    icon: Search,
    title: "SEO Optimizer",
    description: "Boost your search rankings with AI-driven SEO recommendations. Optimize content, keywords, and technical elements automatically.",
    badge: "Growth",
    color: "from-teal-500 to-blue-500"
  },
  {
    icon: Monitor,
    title: "AI Ad Creation",
    description: "Generate high-converting ads for all platforms instantly. Create compelling visuals, copy, and targeting strategies that drive results and maximize ROI.",
    badge: "Creative",
    color: "from-pink-500 to-rose-500"
  },
  {
    icon: Zap,
    title: "Analytics Dashboard",
    description: "Get deep insights into your marketing performance. AI-powered analytics provide actionable recommendations to improve your ROI.",
    badge: "Insights",
    color: "from-yellow-500 to-orange-500"
  }
]

const Features = () => {
  return (
    <section className="pt-8 pb-24 bg-black">
      <div className="max-w-7xl mx-auto px-2 sm:px-4 lg:px-6">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <Badge className="mb-4 bg-white/10 text-white border-white/20 backdrop-blur-sm">
            Powerful Features
          </Badge>
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
            Everything you need to
            <span className="bg-gradient-to-r from-blue-400 to-cyan-400 bg-clip-text text-transparent"> dominate marketing</span>
          </h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            Our comprehensive AI suite provides all the tools you need to create, optimize, and scale your marketing efforts with unprecedented efficiency.
          </p>
        </motion.div>

        {/* Features Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {features.map((feature, index) => (
            <motion.div
              key={feature.title}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
              whileHover={{ y: -5 }}
              className="group"
            >
              <Card className="h-full border border-white/10 shadow-lg hover:shadow-xl transition-all duration-300 bg-white/5 backdrop-blur-md hover:bg-white/10">
                <CardContent className="p-6">
                  {/* Icon and Badge */}
                  <div className="flex items-start justify-between mb-4">
                    <div className="p-3 rounded-lg bg-white/10 backdrop-blur-sm border border-white/20 group-hover:scale-110 transition-transform duration-300">
                      <feature.icon className="w-6 h-6 text-white" />
                    </div>
                    <Badge className="text-xs bg-white/10 text-white border-white/20">
                      {feature.badge}
                    </Badge>
                  </div>

                  {/* Content */}
                  <h3 className="text-lg font-semibold text-white mb-3 group-hover:text-blue-400 transition-colors">
                    {feature.title}
                  </h3>
                  <p className="text-gray-300 text-sm leading-relaxed">
                    {feature.description}
                  </p>

                  {/* Hover Effect */}
                  <div className="mt-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                    <div className="text-blue-400 text-sm font-medium flex items-center">
                      Learn more
                      <motion.div
                        className="ml-1"
                        animate={{ x: [0, 4, 0] }}
                        transition={{ duration: 1.5, repeat: Infinity }}
                      >
                        →
                      </motion.div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        {/* Bottom CTA */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          viewport={{ once: true }}
          className="text-center mt-16"
        >
          <p className="text-gray-600 mb-6">
            Ready to transform your marketing with AI?
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="px-8 py-3 bg-gradient-to-r from-purple-600 to-blue-600 text-white rounded-lg font-medium shadow-lg hover:shadow-xl transition-all duration-300"
            >
              Start Free Trial
            </motion.button>
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="px-8 py-3 border border-gray-300 text-gray-700 rounded-lg font-medium hover:bg-gray-50 transition-all duration-300"
            >
              View All Features
            </motion.button>
          </div>
        </motion.div>
      </div>
    </section>
  )
}

export default Features
