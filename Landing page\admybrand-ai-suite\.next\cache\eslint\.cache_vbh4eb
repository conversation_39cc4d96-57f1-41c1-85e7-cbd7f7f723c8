[{"C:\\Users\\<USER>\\Downloads\\Landing page\\admybrand-ai-suite\\src\\app\\layout.tsx": "1", "C:\\Users\\<USER>\\Downloads\\Landing page\\admybrand-ai-suite\\src\\app\\page.tsx": "2", "C:\\Users\\<USER>\\Downloads\\Landing page\\admybrand-ai-suite\\src\\components\\layout\\footer.tsx": "3", "C:\\Users\\<USER>\\Downloads\\Landing page\\admybrand-ai-suite\\src\\components\\layout\\header.tsx": "4", "C:\\Users\\<USER>\\Downloads\\Landing page\\admybrand-ai-suite\\src\\components\\sections\\blog-resources.tsx": "5", "C:\\Users\\<USER>\\Downloads\\Landing page\\admybrand-ai-suite\\src\\components\\sections\\contact.tsx": "6", "C:\\Users\\<USER>\\Downloads\\Landing page\\admybrand-ai-suite\\src\\components\\sections\\demo-video.tsx": "7", "C:\\Users\\<USER>\\Downloads\\Landing page\\admybrand-ai-suite\\src\\components\\sections\\faq.tsx": "8", "C:\\Users\\<USER>\\Downloads\\Landing page\\admybrand-ai-suite\\src\\components\\sections\\feature-showcase.tsx": "9", "C:\\Users\\<USER>\\Downloads\\Landing page\\admybrand-ai-suite\\src\\components\\sections\\features.tsx": "10", "C:\\Users\\<USER>\\Downloads\\Landing page\\admybrand-ai-suite\\src\\components\\sections\\hero.tsx": "11", "C:\\Users\\<USER>\\Downloads\\Landing page\\admybrand-ai-suite\\src\\components\\sections\\logo-slider.tsx": "12", "C:\\Users\\<USER>\\Downloads\\Landing page\\admybrand-ai-suite\\src\\components\\sections\\pricing-calculator.tsx": "13", "C:\\Users\\<USER>\\Downloads\\Landing page\\admybrand-ai-suite\\src\\components\\sections\\pricing.tsx": "14", "C:\\Users\\<USER>\\Downloads\\Landing page\\admybrand-ai-suite\\src\\components\\sections\\testimonials.tsx": "15", "C:\\Users\\<USER>\\Downloads\\Landing page\\admybrand-ai-suite\\src\\components\\ui\\advanced-animations.tsx": "16", "C:\\Users\\<USER>\\Downloads\\Landing page\\admybrand-ai-suite\\src\\components\\ui\\avatar.tsx": "17", "C:\\Users\\<USER>\\Downloads\\Landing page\\admybrand-ai-suite\\src\\components\\ui\\badge.tsx": "18", "C:\\Users\\<USER>\\Downloads\\Landing page\\admybrand-ai-suite\\src\\components\\ui\\button.tsx": "19", "C:\\Users\\<USER>\\Downloads\\Landing page\\admybrand-ai-suite\\src\\components\\ui\\card.tsx": "20", "C:\\Users\\<USER>\\Downloads\\Landing page\\admybrand-ai-suite\\src\\components\\ui\\dropdown.tsx": "21", "C:\\Users\\<USER>\\Downloads\\Landing page\\admybrand-ai-suite\\src\\components\\ui\\floating-action-button.tsx": "22", "C:\\Users\\<USER>\\Downloads\\Landing page\\admybrand-ai-suite\\src\\components\\ui\\index.ts": "23", "C:\\Users\\<USER>\\Downloads\\Landing page\\admybrand-ai-suite\\src\\components\\ui\\input.tsx": "24", "C:\\Users\\<USER>\\Downloads\\Landing page\\admybrand-ai-suite\\src\\components\\ui\\modal.tsx": "25", "C:\\Users\\<USER>\\Downloads\\Landing page\\admybrand-ai-suite\\src\\components\\ui\\tooltip.tsx": "26", "C:\\Users\\<USER>\\Downloads\\Landing page\\admybrand-ai-suite\\src\\lib\\utils.ts": "27"}, {"size": 1552, "mtime": 1753772150675, "results": "28", "hashOfConfig": "29"}, {"size": 1797, "mtime": 1753898462323, "results": "30", "hashOfConfig": "29"}, {"size": 9448, "mtime": 1753795474472, "results": "31", "hashOfConfig": "29"}, {"size": 6256, "mtime": 1753891679492, "results": "32", "hashOfConfig": "29"}, {"size": 10681, "mtime": 1753891312852, "results": "33", "hashOfConfig": "29"}, {"size": 12287, "mtime": 1753893684625, "results": "34", "hashOfConfig": "29"}, {"size": 10549, "mtime": 1753899070725, "results": "35", "hashOfConfig": "29"}, {"size": 8060, "mtime": 1753889336879, "results": "36", "hashOfConfig": "29"}, {"size": 26189, "mtime": 1753898308295, "results": "37", "hashOfConfig": "29"}, {"size": 8235, "mtime": 1753893173714, "results": "38", "hashOfConfig": "29"}, {"size": 6195, "mtime": 1753896484713, "results": "39", "hashOfConfig": "29"}, {"size": 5752, "mtime": 1753898370152, "results": "40", "hashOfConfig": "29"}, {"size": 9834, "mtime": 1753899133479, "results": "41", "hashOfConfig": "29"}, {"size": 10326, "mtime": 1753899159915, "results": "42", "hashOfConfig": "29"}, {"size": 10709, "mtime": 1753795372029, "results": "43", "hashOfConfig": "29"}, {"size": 7111, "mtime": 1753891667187, "results": "44", "hashOfConfig": "29"}, {"size": 1612, "mtime": 1753771570593, "results": "45", "hashOfConfig": "29"}, {"size": 1752, "mtime": 1753771559812, "results": "46", "hashOfConfig": "29"}, {"size": 2903, "mtime": 1753771520722, "results": "47", "hashOfConfig": "29"}, {"size": 2705, "mtime": 1753771536195, "results": "48", "hashOfConfig": "29"}, {"size": 7294, "mtime": 1753771644932, "results": "49", "hashOfConfig": "29"}, {"size": 2663, "mtime": 1753890268722, "results": "50", "hashOfConfig": "29"}, {"size": 226, "mtime": 1753771651344, "results": "51", "hashOfConfig": "29"}, {"size": 2338, "mtime": 1753893697941, "results": "52", "hashOfConfig": "29"}, {"size": 3824, "mtime": 1753771605558, "results": "53", "hashOfConfig": "29"}, {"size": 1144, "mtime": 1753771615580, "results": "54", "hashOfConfig": "29"}, {"size": 1322, "mtime": 1753771505206, "results": "55", "hashOfConfig": "29"}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1kis207", {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\Downloads\\Landing page\\admybrand-ai-suite\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\Landing page\\admybrand-ai-suite\\src\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\Landing page\\admybrand-ai-suite\\src\\components\\layout\\footer.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\Landing page\\admybrand-ai-suite\\src\\components\\layout\\header.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\Landing page\\admybrand-ai-suite\\src\\components\\sections\\blog-resources.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\Landing page\\admybrand-ai-suite\\src\\components\\sections\\contact.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\Landing page\\admybrand-ai-suite\\src\\components\\sections\\demo-video.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\Landing page\\admybrand-ai-suite\\src\\components\\sections\\faq.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\Landing page\\admybrand-ai-suite\\src\\components\\sections\\feature-showcase.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\Landing page\\admybrand-ai-suite\\src\\components\\sections\\features.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\Landing page\\admybrand-ai-suite\\src\\components\\sections\\hero.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\Landing page\\admybrand-ai-suite\\src\\components\\sections\\logo-slider.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\Landing page\\admybrand-ai-suite\\src\\components\\sections\\pricing-calculator.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\Landing page\\admybrand-ai-suite\\src\\components\\sections\\pricing.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\Landing page\\admybrand-ai-suite\\src\\components\\sections\\testimonials.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\Landing page\\admybrand-ai-suite\\src\\components\\ui\\advanced-animations.tsx", ["137", "138"], [], "C:\\Users\\<USER>\\Downloads\\Landing page\\admybrand-ai-suite\\src\\components\\ui\\avatar.tsx", ["139"], [], "C:\\Users\\<USER>\\Downloads\\Landing page\\admybrand-ai-suite\\src\\components\\ui\\badge.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\Landing page\\admybrand-ai-suite\\src\\components\\ui\\button.tsx", ["140"], [], "C:\\Users\\<USER>\\Downloads\\Landing page\\admybrand-ai-suite\\src\\components\\ui\\card.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\Landing page\\admybrand-ai-suite\\src\\components\\ui\\dropdown.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\Landing page\\admybrand-ai-suite\\src\\components\\ui\\floating-action-button.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\Landing page\\admybrand-ai-suite\\src\\components\\ui\\index.ts", [], [], "C:\\Users\\<USER>\\Downloads\\Landing page\\admybrand-ai-suite\\src\\components\\ui\\input.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\Landing page\\admybrand-ai-suite\\src\\components\\ui\\modal.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\Landing page\\admybrand-ai-suite\\src\\components\\ui\\tooltip.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\Landing page\\admybrand-ai-suite\\src\\lib\\utils.ts", ["141", "142", "143", "144"], [], {"ruleId": "145", "severity": 1, "message": "146", "line": 190, "column": 35, "nodeType": null, "messageId": "147", "endLine": 190, "endColumn": 40}, {"ruleId": "148", "severity": 1, "message": "149", "line": 220, "column": 72, "nodeType": "150", "messageId": "151", "endLine": 220, "endColumn": 75, "suggestions": "152"}, {"ruleId": "153", "severity": 1, "message": "154", "line": 46, "column": 11, "nodeType": "155", "endLine": 51, "endColumn": 13}, {"ruleId": "145", "severity": 1, "message": "156", "line": 45, "column": 32, "nodeType": null, "messageId": "147", "endLine": 45, "endColumn": 39}, {"ruleId": "148", "severity": 1, "message": "149", "line": 31, "column": 46, "nodeType": "150", "messageId": "151", "endLine": 31, "endColumn": 49, "suggestions": "157"}, {"ruleId": "148", "severity": 1, "message": "149", "line": 31, "column": 56, "nodeType": "150", "messageId": "151", "endLine": 31, "endColumn": 59, "suggestions": "158"}, {"ruleId": "148", "severity": 1, "message": "149", "line": 42, "column": 46, "nodeType": "150", "messageId": "151", "endLine": 42, "endColumn": 49, "suggestions": "159"}, {"ruleId": "148", "severity": 1, "message": "149", "line": 42, "column": 56, "nodeType": "150", "messageId": "151", "endLine": 42, "endColumn": 59, "suggestions": "160"}, "@typescript-eslint/no-unused-vars", "'index' is defined but never used.", "unusedVar", "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["161", "162"], "@next/next/no-img-element", "Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element", "JSXOpeningElement", "'as<PERSON><PERSON>d' is assigned a value but never used.", ["163", "164"], ["165", "166"], ["167", "168"], ["169", "170"], {"messageId": "171", "fix": "172", "desc": "173"}, {"messageId": "174", "fix": "175", "desc": "176"}, {"messageId": "171", "fix": "177", "desc": "173"}, {"messageId": "174", "fix": "178", "desc": "176"}, {"messageId": "171", "fix": "179", "desc": "173"}, {"messageId": "174", "fix": "180", "desc": "176"}, {"messageId": "171", "fix": "181", "desc": "173"}, {"messageId": "174", "fix": "182", "desc": "176"}, {"messageId": "171", "fix": "183", "desc": "173"}, {"messageId": "174", "fix": "184", "desc": "176"}, "suggestUnknown", {"range": "185", "text": "186"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "187", "text": "188"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "189", "text": "186"}, {"range": "190", "text": "188"}, {"range": "191", "text": "186"}, {"range": "192", "text": "188"}, {"range": "193", "text": "186"}, {"range": "194", "text": "188"}, {"range": "195", "text": "186"}, {"range": "196", "text": "188"}, [5315, 5318], "unknown", [5315, 5318], "never", [766, 769], [766, 769], [776, 779], [776, 779], [1045, 1048], [1045, 1048], [1055, 1058], [1055, 1058]]