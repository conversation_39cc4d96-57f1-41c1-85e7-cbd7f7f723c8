"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[607],{224:(t,e,r)=>{r.d(e,{A:()=>i});let i=(0,r(9946).A)("quote",[["path",{d:"M16 3a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2 1 1 0 0 1 1 1v1a2 2 0 0 1-2 2 1 1 0 0 0-1 1v2a1 1 0 0 0 1 1 6 6 0 0 0 6-6V5a2 2 0 0 0-2-2z",key:"rib7q0"}],["path",{d:"M5 3a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2 1 1 0 0 1 1 1v1a2 2 0 0 1-2 2 1 1 0 0 0-1 1v2a1 1 0 0 0 1 1 6 6 0 0 0 6-6V5a2 2 0 0 0-2-2z",key:"1ymkrd"}]])},556:(t,e,r)=>{r.d(e,{A:()=>i});let i=(0,r(9946).A)("twitter",[["path",{d:"M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z",key:"pff0z6"}]])},646:(t,e,r)=>{r.d(e,{A:()=>i});let i=(0,r(9946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},760:(t,e,r)=>{r.d(e,{N:()=>y});var i=r(5155),n=r(2115),s=r(869),o=r(2885),a=r(7494),l=r(845),u=r(7351),h=r(1508);class c extends n.Component{getSnapshotBeforeUpdate(t){let e=this.props.childRef.current;if(e&&t.isPresent&&!this.props.isPresent){let t=e.offsetParent,r=(0,u.s)(t)&&t.offsetWidth||0,i=this.props.sizeRef.current;i.height=e.offsetHeight||0,i.width=e.offsetWidth||0,i.top=e.offsetTop,i.left=e.offsetLeft,i.right=r-i.width-i.left}return null}componentDidUpdate(){}render(){return this.props.children}}function d(t){let{children:e,isPresent:r,anchorX:s,root:o}=t,a=(0,n.useId)(),l=(0,n.useRef)(null),u=(0,n.useRef)({width:0,height:0,top:0,left:0,right:0}),{nonce:d}=(0,n.useContext)(h.Q);return(0,n.useInsertionEffect)(()=>{let{width:t,height:e,top:i,left:n,right:h}=u.current;if(r||!l.current||!t||!e)return;l.current.dataset.motionPopId=a;let c=document.createElement("style");d&&(c.nonce=d);let p=null!=o?o:document.head;return p.appendChild(c),c.sheet&&c.sheet.insertRule('\n          [data-motion-pop-id="'.concat(a,'"] {\n            position: absolute !important;\n            width: ').concat(t,"px !important;\n            height: ").concat(e,"px !important;\n            ").concat("left"===s?"left: ".concat(n):"right: ".concat(h),"px !important;\n            top: ").concat(i,"px !important;\n          }\n        ")),()=>{p.contains(c)&&p.removeChild(c)}},[r]),(0,i.jsx)(c,{isPresent:r,childRef:l,sizeRef:u,children:n.cloneElement(e,{ref:l})})}let p=t=>{let{children:e,initial:r,isPresent:s,onExitComplete:a,custom:u,presenceAffectsLayout:h,mode:c,anchorX:p,root:m}=t,g=(0,o.M)(f),v=(0,n.useId)(),y=!0,x=(0,n.useMemo)(()=>(y=!1,{id:v,initial:r,isPresent:s,custom:u,onExitComplete:t=>{for(let e of(g.set(t,!0),g.values()))if(!e)return;a&&a()},register:t=>(g.set(t,!1),()=>g.delete(t))}),[s,g,a]);return h&&y&&(x={...x}),(0,n.useMemo)(()=>{g.forEach((t,e)=>g.set(e,!1))},[s]),n.useEffect(()=>{s||g.size||!a||a()},[s]),"popLayout"===c&&(e=(0,i.jsx)(d,{isPresent:s,anchorX:p,root:m,children:e})),(0,i.jsx)(l.t.Provider,{value:x,children:e})};function f(){return new Map}var m=r(2082);let g=t=>t.key||"";function v(t){let e=[];return n.Children.forEach(t,t=>{(0,n.isValidElement)(t)&&e.push(t)}),e}let y=t=>{let{children:e,custom:r,initial:l=!0,onExitComplete:u,presenceAffectsLayout:h=!0,mode:c="sync",propagate:d=!1,anchorX:f="left",root:y}=t,[x,b]=(0,m.xQ)(d),w=(0,n.useMemo)(()=>v(e),[e]),k=d&&!x?[]:w.map(g),A=(0,n.useRef)(!0),T=(0,n.useRef)(w),M=(0,o.M)(()=>new Map),[P,S]=(0,n.useState)(w),[E,C]=(0,n.useState)(w);(0,a.E)(()=>{A.current=!1,T.current=w;for(let t=0;t<E.length;t++){let e=g(E[t]);k.includes(e)?M.delete(e):!0!==M.get(e)&&M.set(e,!1)}},[E,k.length,k.join("-")]);let V=[];if(w!==P){let t=[...w];for(let e=0;e<E.length;e++){let r=E[e],i=g(r);k.includes(i)||(t.splice(e,0,r),V.push(r))}return"wait"===c&&V.length&&(t=V),C(v(t)),S(w),null}let{forceRender:D}=(0,n.useContext)(s.L);return(0,i.jsx)(i.Fragment,{children:E.map(t=>{let e=g(t),n=(!d||!!x)&&(w===E||k.includes(e));return(0,i.jsx)(p,{isPresent:n,initial:(!A.current||!!l)&&void 0,custom:r,presenceAffectsLayout:h,mode:c,root:y,onExitComplete:n?void 0:()=>{if(!M.has(e))return;M.set(e,!0);let t=!0;M.forEach(e=>{e||(t=!1)}),t&&(null==D||D(),C(T.current),d&&(null==b||b()),u&&u())},anchorX:f,children:t},e)})})}},802:(t,e,r)=>{r.d(e,{os:()=>tb});var i,n,s,o,a,l,u,h=r(934),c={},d=180/Math.PI,p=Math.PI/180,f=Math.atan2,m=/([A-Z])/g,g=/(left|right|width|margin|padding|x)/i,v=/[\s,\(]\S/,y={autoAlpha:"opacity,visibility",scale:"scaleX,scaleY",alpha:"opacity"},x=function(t,e){return e.set(e.t,e.p,Math.round((e.s+e.c*t)*1e4)/1e4+e.u,e)},b=function(t,e){return e.set(e.t,e.p,1===t?e.e:Math.round((e.s+e.c*t)*1e4)/1e4+e.u,e)},w=function(t,e){return e.set(e.t,e.p,t?Math.round((e.s+e.c*t)*1e4)/1e4+e.u:e.b,e)},k=function(t,e){var r=e.s+e.c*t;e.set(e.t,e.p,~~(r+(r<0?-.5:.5))+e.u,e)},A=function(t,e){return e.set(e.t,e.p,t?e.e:e.b,e)},T=function(t,e){return e.set(e.t,e.p,1!==t?e.b:e.e,e)},M=function(t,e,r){return t.style[e]=r},P=function(t,e,r){return t.style.setProperty(e,r)},S=function(t,e,r){return t._gsap[e]=r},E=function(t,e,r){return t._gsap.scaleX=t._gsap.scaleY=r},C=function(t,e,r,i,n){var s=t._gsap;s.scaleX=s.scaleY=r,s.renderTransform(n,s)},V=function(t,e,r,i,n){var s=t._gsap;s[e]=r,s.renderTransform(n,s)},D="transform",O=D+"Origin",R=function t(e,r){var i=this,n=this.target,s=n.style,o=n._gsap;if(e in c&&s){if(this.tfm=this.tfm||{},"transform"===e)return y.transform.split(",").forEach(function(e){return t.call(i,e,r)});if(~(e=y[e]||e).indexOf(",")?e.split(",").forEach(function(t){return i.tfm[t]=Z(n,t)}):this.tfm[e]=o.x?o[e]:Z(n,e),e===O&&(this.tfm.zOrigin=o.zOrigin),this.props.indexOf(D)>=0)return;o.svg&&(this.svgo=n.getAttribute("data-svg-origin"),this.props.push(O,r,"")),e=D}(s||r)&&this.props.push(e,r,s[e])},_=function(t){t.translate&&(t.removeProperty("translate"),t.removeProperty("scale"),t.removeProperty("rotate"))},j=function(){var t,e,r=this.props,i=this.target,n=i.style,s=i._gsap;for(t=0;t<r.length;t+=3)r[t+1]?2===r[t+1]?i[r[t]](r[t+2]):i[r[t]]=r[t+2]:r[t+2]?n[r[t]]=r[t+2]:n.removeProperty("--"===r[t].substr(0,2)?r[t]:r[t].replace(m,"-$1").toLowerCase());if(this.tfm){for(e in this.tfm)s[e]=this.tfm[e];s.svg&&(s.renderTransform(),i.setAttribute("data-svg-origin",this.svgo||"")),(t=l())&&t.isStart||n[D]||(_(n),s.zOrigin&&n[O]&&(n[O]+=" "+s.zOrigin+"px",s.zOrigin=0,s.renderTransform()),s.uncache=1)}},L=function(t,e){var r={target:t,props:[],revert:j,save:R};return t._gsap||h.os.core.getCache(t),e&&t.style&&t.nodeType&&e.split(",").forEach(function(t){return r.save(t)}),r},z=function(t,e){var r=i.createElementNS?i.createElementNS((e||"http://www.w3.org/1999/xhtml").replace(/^https/,"http"),t):i.createElement(t);return r&&r.style?r:i.createElement(t)},F=function t(e,r,i){var n=getComputedStyle(e);return n[r]||n.getPropertyValue(r.replace(m,"-$1").toLowerCase())||n.getPropertyValue(r)||!i&&t(e,I(r)||r,1)||""},B="O,Moz,ms,Ms,Webkit".split(","),I=function(t,e,r){var i=(e||o).style,n=5;if(t in i&&!r)return t;for(t=t.charAt(0).toUpperCase()+t.substr(1);n--&&!(B[n]+t in i););return n<0?null:(3===n?"ms":n>=0?B[n]:"")+t},N=function(){"undefined"!=typeof window&&window.document&&(n=(i=window.document).documentElement,o=z("div")||{style:{}},z("div"),O=(D=I(D))+"Origin",o.style.cssText="border-width:0;line-height:0;position:absolute;padding:0",u=!!I("perspective"),l=h.os.core.reverting,s=1)},Y=function(t){var e,r=t.ownerSVGElement,i=z("svg",r&&r.getAttribute("xmlns")||"http://www.w3.org/2000/svg"),s=t.cloneNode(!0);s.style.display="block",i.appendChild(s),n.appendChild(i);try{e=s.getBBox()}catch(t){}return i.removeChild(s),n.removeChild(i),e},U=function(t,e){for(var r=e.length;r--;)if(t.hasAttribute(e[r]))return t.getAttribute(e[r])},W=function(t){var e,r;try{e=t.getBBox()}catch(i){e=Y(t),r=1}return e&&(e.width||e.height)||r||(e=Y(t)),!e||e.width||e.x||e.y?e:{x:+U(t,["x","cx","x1"])||0,y:+U(t,["y","cy","y1"])||0,width:0,height:0}},X=function(t){return!!(t.getCTM&&(!t.parentNode||t.ownerSVGElement)&&W(t))},$=function(t,e){if(e){var r,i=t.style;e in c&&e!==O&&(e=D),i.removeProperty?(("ms"===(r=e.substr(0,2))||"webkit"===e.substr(0,6))&&(e="-"+e),i.removeProperty("--"===r?e:e.replace(m,"-$1").toLowerCase())):i.removeAttribute(e)}},H=function(t,e,r,i,n,s){var o=new h.J7(t._pt,e,r,0,1,s?T:A);return t._pt=o,o.b=i,o.e=n,t._props.push(r),o},q={deg:1,rad:1,turn:1},G={grid:1,flex:1},K=function t(e,r,n,s){var a,l,u,d,p=parseFloat(n)||0,f=(n+"").trim().substr((p+"").length)||"px",m=o.style,v=g.test(r),y="svg"===e.tagName.toLowerCase(),x=(y?"client":"offset")+(v?"Width":"Height"),b="px"===s,w="%"===s;if(s===f||!p||q[s]||q[f])return p;if("px"===f||b||(p=t(e,r,n,"px")),d=e.getCTM&&X(e),(w||"%"===f)&&(c[r]||~r.indexOf("adius")))return a=d?e.getBBox()[v?"width":"height"]:e[x],(0,h.E_)(w?p/a*100:p/100*a);if(m[v?"width":"height"]=100+(b?f:s),l="rem"!==s&&~r.indexOf("adius")||"em"===s&&e.appendChild&&!y?e:e.parentNode,d&&(l=(e.ownerSVGElement||{}).parentNode),l&&l!==i&&l.appendChild||(l=i.body),(u=l._gsap)&&w&&u.width&&v&&u.time===h.au.time&&!u.uncache)return(0,h.E_)(p/u.width*100);if(w&&("height"===r||"width"===r)){var k=e.style[r];e.style[r]=100+s,a=e[x],k?e.style[r]=k:$(e,r)}else(w||"%"===f)&&!G[F(l,"display")]&&(m.position=F(e,"position")),l===e&&(m.position="static"),l.appendChild(o),a=o[x],l.removeChild(o),m.position="absolute";return v&&w&&((u=(0,h.a0)(l)).time=h.au.time,u.width=l[x]),(0,h.E_)(b?a*p/100:a&&p?100/a*p:0)},Z=function(t,e,r,i){var n;return s||N(),e in y&&"transform"!==e&&~(e=y[e]).indexOf(",")&&(e=e.split(",")[0]),c[e]&&"transform"!==e?(n=tu(t,i),n="transformOrigin"!==e?n[e]:n.svg?n.origin:th(F(t,O))+" "+n.zOrigin+"px"):(!(n=t.style[e])||"auto"===n||i||~(n+"").indexOf("calc("))&&(n=tr[e]&&tr[e](t,e,r)||F(t,e)||(0,h.n)(t,e)||+("opacity"===e)),r&&!~(n+"").trim().indexOf(" ")?K(t,e,n,r)+r:n},Q=function(t,e,r,i){if(!r||"none"===r){var n=I(e,t,1),s=n&&F(t,n,1);s&&s!==r?(e=n,r=s):"borderColor"===e&&(r=F(t,"borderTopColor"))}var o,a,l,u,c,d,p,f,m,g,v,y=new h.J7(this._pt,t.style,e,0,1,h.l1),x=0,b=0;if(y.b=r,y.e=i,r+="","var(--"===(i+="").substring(0,6)&&(i=F(t,i.substring(4,i.indexOf(")")))),"auto"===i&&(d=t.style[e],t.style[e]=i,i=F(t,e)||i,d?t.style[e]=d:$(t,e)),o=[r,i],(0,h.Uc)(o),r=o[0],i=o[1],l=r.match(h.vM)||[],(i.match(h.vM)||[]).length){for(;a=h.vM.exec(i);)p=a[0],m=i.substring(x,a.index),c?c=(c+1)%5:("rgba("===m.substr(-5)||"hsla("===m.substr(-5))&&(c=1),p!==(d=l[b++]||"")&&(u=parseFloat(d)||0,v=d.substr((u+"").length),"="===p.charAt(1)&&(p=(0,h.B0)(u,p)+v),f=parseFloat(p),g=p.substr((f+"").length),x=h.vM.lastIndex-g.length,g||(g=g||h.Yz.units[e]||v,x===i.length&&(i+=g,y.e+=g)),v!==g&&(u=K(t,e,d,g)||0),y._pt={_next:y._pt,p:m||1===b?m:",",s:u,c:f-u,m:c&&c<4||"zIndex"===e?Math.round:0});y.c=x<i.length?i.substring(x,i.length):""}else y.r="display"===e&&"none"===i?T:A;return h.Ks.test(i)&&(y.e=0),this._pt=y,y},J={top:"0%",bottom:"100%",left:"0%",right:"100%",center:"50%"},tt=function(t){var e=t.split(" "),r=e[0],i=e[1]||"50%";return("top"===r||"bottom"===r||"left"===i||"right"===i)&&(t=r,r=i,i=t),e[0]=J[r]||r,e[1]=J[i]||i,e.join(" ")},te=function(t,e){if(e.tween&&e.tween._time===e.tween._dur){var r,i,n,s=e.t,o=s.style,a=e.u,l=s._gsap;if("all"===a||!0===a)o.cssText="",i=1;else for(n=(a=a.split(",")).length;--n>-1;)c[r=a[n]]&&(i=1,r="transformOrigin"===r?O:D),$(s,r);i&&($(s,D),l&&(l.svg&&s.removeAttribute("transform"),o.scale=o.rotate=o.translate="none",tu(s,1),l.uncache=1,_(o)))}},tr={clearProps:function(t,e,r,i,n){if("isFromStart"!==n.data){var s=t._pt=new h.J7(t._pt,e,r,0,0,te);return s.u=i,s.pr=-10,s.tween=n,t._props.push(r),1}}},ti=[1,0,0,1,0,0],tn={},ts=function(t){return"matrix(1, 0, 0, 1, 0, 0)"===t||"none"===t||!t},to=function(t){var e=F(t,D);return ts(e)?ti:e.substr(7).match(h.vX).map(h.E_)},ta=function(t,e){var r,i,s,o,a=t._gsap||(0,h.a0)(t),l=t.style,u=to(t);return a.svg&&t.getAttribute("transform")?"1,0,0,1,0,0"===(u=[(s=t.transform.baseVal.consolidate().matrix).a,s.b,s.c,s.d,s.e,s.f]).join(",")?ti:u:(u!==ti||t.offsetParent||t===n||a.svg||(s=l.display,l.display="block",(r=t.parentNode)&&(t.offsetParent||t.getBoundingClientRect().width)||(o=1,i=t.nextElementSibling,n.appendChild(t)),u=to(t),s?l.display=s:$(t,"display"),o&&(i?r.insertBefore(t,i):r?r.appendChild(t):n.removeChild(t))),e&&u.length>6?[u[0],u[1],u[4],u[5],u[12],u[13]]:u)},tl=function(t,e,r,i,n,s){var o,a,l,u,h=t._gsap,c=n||ta(t,!0),d=h.xOrigin||0,p=h.yOrigin||0,f=h.xOffset||0,m=h.yOffset||0,g=c[0],v=c[1],y=c[2],x=c[3],b=c[4],w=c[5],k=e.split(" "),A=parseFloat(k[0])||0,T=parseFloat(k[1])||0;r?c!==ti&&(a=g*x-v*y)&&(l=x/a*A+-y/a*T+(y*w-x*b)/a,u=-v/a*A+g/a*T-(g*w-v*b)/a,A=l,T=u):(A=(o=W(t)).x+(~k[0].indexOf("%")?A/100*o.width:A),T=o.y+(~(k[1]||k[0]).indexOf("%")?T/100*o.height:T)),i||!1!==i&&h.smooth?(h.xOffset=f+((b=A-d)*g+(w=T-p)*y)-b,h.yOffset=m+(b*v+w*x)-w):h.xOffset=h.yOffset=0,h.xOrigin=A,h.yOrigin=T,h.smooth=!!i,h.origin=e,h.originIsAbsolute=!!r,t.style[O]="0px 0px",s&&(H(s,h,"xOrigin",d,A),H(s,h,"yOrigin",p,T),H(s,h,"xOffset",f,h.xOffset),H(s,h,"yOffset",m,h.yOffset)),t.setAttribute("data-svg-origin",A+" "+T)},tu=function(t,e){var r=t._gsap||new h.n6(t);if("x"in r&&!e&&!r.uncache)return r;var i,n,s,o,a,l,c,m,g,v,y,x,b,w,k,A,T,M,P,S,E,C,V,R,_,j,L,z,B,I,N,Y,U=t.style,W=r.scaleX<0,$=getComputedStyle(t),H=F(t,O)||"0";return i=n=s=l=c=m=g=v=y=0,o=a=1,r.svg=!!(t.getCTM&&X(t)),$.translate&&(("none"!==$.translate||"none"!==$.scale||"none"!==$.rotate)&&(U[D]=("none"!==$.translate?"translate3d("+($.translate+" 0 0").split(" ").slice(0,3).join(", ")+") ":"")+("none"!==$.rotate?"rotate("+$.rotate+") ":"")+("none"!==$.scale?"scale("+$.scale.split(" ").join(",")+") ":"")+("none"!==$[D]?$[D]:"")),U.scale=U.rotate=U.translate="none"),w=ta(t,r.svg),r.svg&&(r.uncache?(_=t.getBBox(),H=r.xOrigin-_.x+"px "+(r.yOrigin-_.y)+"px",R=""):R=!e&&t.getAttribute("data-svg-origin"),tl(t,R||H,!!R||r.originIsAbsolute,!1!==r.smooth,w)),x=r.xOrigin||0,b=r.yOrigin||0,w!==ti&&(M=w[0],P=w[1],S=w[2],E=w[3],i=C=w[4],n=V=w[5],6===w.length?(o=Math.sqrt(M*M+P*P),a=Math.sqrt(E*E+S*S),l=M||P?f(P,M)*d:0,(g=S||E?f(S,E)*d+l:0)&&(a*=Math.abs(Math.cos(g*p))),r.svg&&(i-=x-(x*M+b*S),n-=b-(x*P+b*E))):(Y=w[6],I=w[7],L=w[8],z=w[9],B=w[10],N=w[11],i=w[12],n=w[13],s=w[14],c=(k=f(Y,B))*d,k&&(R=C*(A=Math.cos(-k))+L*(T=Math.sin(-k)),_=V*A+z*T,j=Y*A+B*T,L=-(C*T)+L*A,z=-(V*T)+z*A,B=-(Y*T)+B*A,N=-(I*T)+N*A,C=R,V=_,Y=j),m=(k=f(-S,B))*d,k&&(R=M*(A=Math.cos(-k))-L*(T=Math.sin(-k)),_=P*A-z*T,j=S*A-B*T,N=E*T+N*A,M=R,P=_,S=j),l=(k=f(P,M))*d,k&&(R=M*(A=Math.cos(k))+P*(T=Math.sin(k)),_=C*A+V*T,P=P*A-M*T,V=V*A-C*T,M=R,C=_),c&&Math.abs(c)+Math.abs(l)>359.9&&(c=l=0,m=180-m),o=(0,h.E_)(Math.sqrt(M*M+P*P+S*S)),a=(0,h.E_)(Math.sqrt(V*V+Y*Y)),g=Math.abs(k=f(C,V))>2e-4?k*d:0,y=N?1/(N<0?-N:N):0),r.svg&&(R=t.getAttribute("transform"),r.forceCSS=t.setAttribute("transform","")||!ts(F(t,D)),R&&t.setAttribute("transform",R))),Math.abs(g)>90&&270>Math.abs(g)&&(W?(o*=-1,g+=l<=0?180:-180,l+=l<=0?180:-180):(a*=-1,g+=g<=0?180:-180)),e=e||r.uncache,r.x=i-((r.xPercent=i&&(!e&&r.xPercent||(Math.round(t.offsetWidth/2)===Math.round(-i)?-50:0)))?t.offsetWidth*r.xPercent/100:0)+"px",r.y=n-((r.yPercent=n&&(!e&&r.yPercent||(Math.round(t.offsetHeight/2)===Math.round(-n)?-50:0)))?t.offsetHeight*r.yPercent/100:0)+"px",r.z=s+"px",r.scaleX=(0,h.E_)(o),r.scaleY=(0,h.E_)(a),r.rotation=(0,h.E_)(l)+"deg",r.rotationX=(0,h.E_)(c)+"deg",r.rotationY=(0,h.E_)(m)+"deg",r.skewX=g+"deg",r.skewY=v+"deg",r.transformPerspective=y+"px",(r.zOrigin=parseFloat(H.split(" ")[2])||!e&&r.zOrigin||0)&&(U[O]=th(H)),r.xOffset=r.yOffset=0,r.force3D=h.Yz.force3D,r.renderTransform=r.svg?tm:u?tf:td,r.uncache=0,r},th=function(t){return(t=t.split(" "))[0]+" "+t[1]},tc=function(t,e,r){var i=(0,h.l_)(e);return(0,h.E_)(parseFloat(e)+parseFloat(K(t,"x",r+"px",i)))+i},td=function(t,e){e.z="0px",e.rotationY=e.rotationX="0deg",e.force3D=0,tf(t,e)},tp="0deg",tf=function(t,e){var r=e||this,i=r.xPercent,n=r.yPercent,s=r.x,o=r.y,a=r.z,l=r.rotation,u=r.rotationY,h=r.rotationX,c=r.skewX,d=r.skewY,f=r.scaleX,m=r.scaleY,g=r.transformPerspective,v=r.force3D,y=r.target,x=r.zOrigin,b="",w="auto"===v&&t&&1!==t||!0===v;if(x&&(h!==tp||u!==tp)){var k,A=parseFloat(u)*p,T=Math.sin(A),M=Math.cos(A);s=tc(y,s,-(T*(k=Math.cos(A=parseFloat(h)*p))*x)),o=tc(y,o,-(-Math.sin(A)*x)),a=tc(y,a,-(M*k*x)+x)}"0px"!==g&&(b+="perspective("+g+") "),(i||n)&&(b+="translate("+i+"%, "+n+"%) "),(w||"0px"!==s||"0px"!==o||"0px"!==a)&&(b+="0px"!==a||w?"translate3d("+s+", "+o+", "+a+") ":"translate("+s+", "+o+") "),l!==tp&&(b+="rotate("+l+") "),u!==tp&&(b+="rotateY("+u+") "),h!==tp&&(b+="rotateX("+h+") "),(c!==tp||d!==tp)&&(b+="skew("+c+", "+d+") "),(1!==f||1!==m)&&(b+="scale("+f+", "+m+") "),y.style[D]=b||"translate(0, 0)"},tm=function(t,e){var r,i,n,s,o,a=e||this,l=a.xPercent,u=a.yPercent,c=a.x,d=a.y,f=a.rotation,m=a.skewX,g=a.skewY,v=a.scaleX,y=a.scaleY,x=a.target,b=a.xOrigin,w=a.yOrigin,k=a.xOffset,A=a.yOffset,T=a.forceCSS,M=parseFloat(c),P=parseFloat(d);f=parseFloat(f),m=parseFloat(m),(g=parseFloat(g))&&(m+=g=parseFloat(g),f+=g),f||m?(f*=p,m*=p,r=Math.cos(f)*v,i=Math.sin(f)*v,n=-(Math.sin(f-m)*y),s=Math.cos(f-m)*y,m&&(g*=p,n*=o=Math.sqrt(1+(o=Math.tan(m-g))*o),s*=o,g&&(r*=o=Math.sqrt(1+(o=Math.tan(g))*o),i*=o)),r=(0,h.E_)(r),i=(0,h.E_)(i),n=(0,h.E_)(n),s=(0,h.E_)(s)):(r=v,s=y,i=n=0),(M&&!~(c+"").indexOf("px")||P&&!~(d+"").indexOf("px"))&&(M=K(x,"x",c,"px"),P=K(x,"y",d,"px")),(b||w||k||A)&&(M=(0,h.E_)(M+b-(b*r+w*n)+k),P=(0,h.E_)(P+w-(b*i+w*s)+A)),(l||u)&&(o=x.getBBox(),M=(0,h.E_)(M+l/100*o.width),P=(0,h.E_)(P+u/100*o.height)),o="matrix("+r+","+i+","+n+","+s+","+M+","+P+")",x.setAttribute("transform",o),T&&(x.style[D]=o)},tg=function(t,e,r,i,n){var s,o,a=(0,h.vQ)(n),l=parseFloat(n)*(a&&~n.indexOf("rad")?d:1)-i,u=i+l+"deg";return a&&("short"===(s=n.split("_")[1])&&(l%=360)!=l%180&&(l+=l<0?360:-360),"cw"===s&&l<0?l=(l+36e9)%360-360*~~(l/360):"ccw"===s&&l>0&&(l=(l-36e9)%360-360*~~(l/360))),t._pt=o=new h.J7(t._pt,e,r,i,l,b),o.e=u,o.u="deg",t._props.push(r),o},tv=function(t,e){for(var r in e)t[r]=e[r];return t},ty=function(t,e,r){var i,n,s,o,a,l,u,d=tv({},r._gsap),p=r.style;for(n in d.svg?(s=r.getAttribute("transform"),r.setAttribute("transform",""),p[D]=e,i=tu(r,1),$(r,D),r.setAttribute("transform",s)):(s=getComputedStyle(r)[D],p[D]=e,i=tu(r,1),p[D]=s),c)(s=d[n])!==(o=i[n])&&0>"perspective,force3D,transformOrigin,svgOrigin".indexOf(n)&&(a=(0,h.l_)(s)!==(u=(0,h.l_)(o))?K(r,n,s,u):parseFloat(s),l=parseFloat(o),t._pt=new h.J7(t._pt,i,n,a,l-a,x),t._pt.u=u||0,t._props.push(n));tv(i,d)};(0,h.fA)("padding,margin,Width,Radius",function(t,e){var r="Right",i="Bottom",n="Left",s=(e<3?["Top",r,i,n]:["Top"+n,"Top"+r,i+r,i+n]).map(function(r){return e<2?t+r:"border"+r+t});tr[e>1?"border"+t:t]=function(t,e,r,i,n){var o,a;if(arguments.length<4)return 5===(a=(o=s.map(function(e){return Z(t,e,r)})).join(" ")).split(o[0]).length?o[0]:a;o=(i+"").split(" "),a={},s.forEach(function(t,e){return a[t]=o[e]=o[e]||o[(e-1)/2|0]}),t.init(e,a,n)}});var tx={name:"css",register:N,targetTest:function(t){return t.style&&t.nodeType},init:function(t,e,r,i,n){var o,a,l,u,d,p,f,m,g,b,A,T,M,P,S,E,C=this._props,V=t.style,R=r.vars.startAt;for(f in s||N(),this.styles=this.styles||L(t),E=this.styles.props,this.tween=r,e)if("autoRound"!==f&&(a=e[f],!(h.wU[f]&&(0,h.Zm)(f,e,r,i,t,n)))){if(d=typeof a,p=tr[f],"function"===d&&(d=typeof(a=a.call(r,i,t,n))),"string"===d&&~a.indexOf("random(")&&(a=(0,h.Vy)(a)),p)p(this,t,f,a,r)&&(S=1);else if("--"===f.substr(0,2))o=(getComputedStyle(t).getPropertyValue(f)+"").trim(),a+="",h.qA.lastIndex=0,h.qA.test(o)||(m=(0,h.l_)(o),g=(0,h.l_)(a)),g?m!==g&&(o=K(t,f,o,g)+g):m&&(a+=m),this.add(V,"setProperty",o,a,i,n,0,0,f),C.push(f),E.push(f,0,V[f]);else if("undefined"!==d){if(R&&f in R?(o="function"==typeof R[f]?R[f].call(r,i,t,n):R[f],(0,h.vQ)(o)&&~o.indexOf("random(")&&(o=(0,h.Vy)(o)),(0,h.l_)(o+"")||"auto"===o||(o+=h.Yz.units[f]||(0,h.l_)(Z(t,f))||""),"="===(o+"").charAt(1)&&(o=Z(t,f))):o=Z(t,f),u=parseFloat(o),(b="string"===d&&"="===a.charAt(1)&&a.substr(0,2))&&(a=a.substr(2)),l=parseFloat(a),f in y&&("autoAlpha"===f&&(1===u&&"hidden"===Z(t,"visibility")&&l&&(u=0),E.push("visibility",0,V.visibility),H(this,V,"visibility",u?"inherit":"hidden",l?"inherit":"hidden",!l)),"scale"!==f&&"transform"!==f&&~(f=y[f]).indexOf(",")&&(f=f.split(",")[0])),A=f in c){if(this.styles.save(f),"string"===d&&"var(--"===a.substring(0,6)&&(l=parseFloat(a=F(t,a.substring(4,a.indexOf(")"))))),T||((M=t._gsap).renderTransform&&!e.parseTransform||tu(t,e.parseTransform),P=!1!==e.smoothOrigin&&M.smooth,(T=this._pt=new h.J7(this._pt,V,D,0,1,M.renderTransform,M,0,-1)).dep=1),"scale"===f)this._pt=new h.J7(this._pt,M,"scaleY",M.scaleY,(b?(0,h.B0)(M.scaleY,b+l):l)-M.scaleY||0,x),this._pt.u=0,C.push("scaleY",f),f+="X";else if("transformOrigin"===f){E.push(O,0,V[O]),a=tt(a),M.svg?tl(t,a,0,P,0,this):((g=parseFloat(a.split(" ")[2])||0)!==M.zOrigin&&H(this,M,"zOrigin",M.zOrigin,g),H(this,V,f,th(o),th(a)));continue}else if("svgOrigin"===f){tl(t,a,1,P,0,this);continue}else if(f in tn){tg(this,M,f,u,b?(0,h.B0)(u,b+a):a);continue}else if("smoothOrigin"===f){H(this,M,"smooth",M.smooth,a);continue}else if("force3D"===f){M[f]=a;continue}else if("transform"===f){ty(this,a,t);continue}}else f in V||(f=I(f)||f);if(A||(l||0===l)&&(u||0===u)&&!v.test(a)&&f in V)m=(o+"").substr((u+"").length),l||(l=0),g=(0,h.l_)(a)||(f in h.Yz.units?h.Yz.units[f]:m),m!==g&&(u=K(t,f,o,g)),this._pt=new h.J7(this._pt,A?M:V,f,u,(b?(0,h.B0)(u,b+l):l)-u,!A&&("px"===g||"zIndex"===f)&&!1!==e.autoRound?k:x),this._pt.u=g||0,m!==g&&"%"!==g&&(this._pt.b=o,this._pt.r=w);else if(f in V)Q.call(this,t,f,o,b?b+a:a);else if(f in t)this.add(t,f,o||t[f],b?b+a:a,i,n);else if("parseTransform"!==f){(0,h.dg)(f,a);continue}A||(f in V?E.push(f,0,V[f]):"function"==typeof t[f]?E.push(f,2,t[f]()):E.push(f,1,o||t[f])),C.push(f)}}S&&(0,h.St)(this)},render:function(t,e){if(e.tween._time||!l())for(var r=e._pt;r;)r.r(t,r.d),r=r._next;else e.styles.revert()},get:Z,aliases:y,getSetter:function(t,e,r){var i=y[e];return i&&0>i.indexOf(",")&&(e=i),e in c&&e!==O&&(t._gsap.x||Z(t,"x"))?r&&a===r?"scale"===e?E:S:(a=r||{},"scale"===e?C:V):t.style&&!(0,h.OF)(t.style[e])?M:~e.indexOf("-")?P:(0,h.Dx)(t,e)},core:{_removeProperty:$,_getMatrix:ta}};h.os.utils.checkPrefix=I,h.os.core.getStyleSaver=L,function(t,e,r,i){var n=(0,h.fA)(t+","+e+","+r,function(t){c[t]=1});(0,h.fA)(e,function(t){h.Yz.units[t]="deg",tn[t]=1}),y[n[13]]=t+","+e,(0,h.fA)(i,function(t){var e=t.split(":");y[e[1]]=n[e[0]]})}("x,y,z,scale,scaleX,scaleY,xPercent,yPercent","rotation,rotationX,rotationY,skewX,skewY","transform,transformOrigin,svgOrigin,force3D,smoothOrigin,transformPerspective","0:translateX,1:translateY,2:translateZ,8:rotate,8:rotationZ,8:rotateZ,9:rotateX,10:rotateY"),(0,h.fA)("x,y,z,top,right,bottom,left,width,height,fontSize,padding,margin,perspective",function(t){h.Yz.units[t]="px"}),h.os.registerPlugin(tx);var tb=h.os.registerPlugin(tx)||h.os;tb.core.Tween},845:(t,e,r)=>{r.d(e,{t:()=>i});let i=(0,r(2115).createContext)(null)},869:(t,e,r)=>{r.d(e,{L:()=>i});let i=(0,r(2115).createContext)({})},1366:(t,e,r)=>{r.d(e,{A:()=>i});let i=(0,r(9946).A)("message-circle",[["path",{d:"M7.9 20A9 9 0 1 0 4 16.1L2 22Z",key:"vv11sd"}]])},1497:(t,e,r)=>{r.d(e,{A:()=>i});let i=(0,r(9946).A)("message-square",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]])},1508:(t,e,r)=>{r.d(e,{Q:()=>i});let i=(0,r(2115).createContext)({transformPagePoint:t=>t,isStatic:!1,reducedMotion:"never"})},1539:(t,e,r)=>{r.d(e,{A:()=>i});let i=(0,r(9946).A)("zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]])},1788:(t,e,r)=>{r.d(e,{A:()=>i});let i=(0,r(9946).A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]])},2082:(t,e,r)=>{r.d(e,{xQ:()=>s});var i=r(2115),n=r(845);function s(t=!0){let e=(0,i.useContext)(n.t);if(null===e)return[!0,null];let{isPresent:r,onExitComplete:o,register:a}=e,l=(0,i.useId)();(0,i.useEffect)(()=>{if(t)return a(l)},[t]);let u=(0,i.useCallback)(()=>t&&o&&o(l),[l,o,t]);return!r&&o?[!1,u]:[!0]}},2085:(t,e,r)=>{r.d(e,{F:()=>o});var i=r(2596);let n=t=>"boolean"==typeof t?`${t}`:0===t?"0":t,s=i.$,o=(t,e)=>r=>{var i;if((null==e?void 0:e.variants)==null)return s(t,null==r?void 0:r.class,null==r?void 0:r.className);let{variants:o,defaultVariants:a}=e,l=Object.keys(o).map(t=>{let e=null==r?void 0:r[t],i=null==a?void 0:a[t];if(null===e)return null;let s=n(e)||n(i);return o[t][s]}),u=r&&Object.entries(r).reduce((t,e)=>{let[r,i]=e;return void 0===i||(t[r]=i),t},{});return s(t,l,null==e||null==(i=e.compoundVariants)?void 0:i.reduce((t,e)=>{let{class:r,className:i,...n}=e;return Object.entries(n).every(t=>{let[e,r]=t;return Array.isArray(r)?r.includes({...a,...u}[e]):({...a,...u})[e]===r})?[...t,r,i]:t},[]),null==r?void 0:r.class,null==r?void 0:r.className)}},2138:(t,e,r)=>{r.d(e,{A:()=>i});let i=(0,r(9946).A)("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},2355:(t,e,r)=>{r.d(e,{A:()=>i});let i=(0,r(9946).A)("chevron-left",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]])},2486:(t,e,r)=>{r.d(e,{A:()=>i});let i=(0,r(9946).A)("send",[["path",{d:"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z",key:"1ffxy3"}],["path",{d:"m21.854 2.147-10.94 10.939",key:"12cjpa"}]])},2596:(t,e,r)=>{r.d(e,{$:()=>i});function i(){for(var t,e,r=0,i="",n=arguments.length;r<n;r++)(t=arguments[r])&&(e=function t(e){var r,i,n="";if("string"==typeof e||"number"==typeof e)n+=e;else if("object"==typeof e)if(Array.isArray(e)){var s=e.length;for(r=0;r<s;r++)e[r]&&(i=t(e[r]))&&(n&&(n+=" "),n+=i)}else for(i in e)e[i]&&(n&&(n+=" "),n+=i);return n}(t))&&(i&&(i+=" "),i+=e);return i}},2713:(t,e,r)=>{r.d(e,{A:()=>i});let i=(0,r(9946).A)("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},2885:(t,e,r)=>{r.d(e,{M:()=>n});var i=r(2115);function n(t){let e=(0,i.useRef)(null);return null===e.current&&(e.current=t()),e.current}},2894:(t,e,r)=>{r.d(e,{A:()=>i});let i=(0,r(9946).A)("linkedin",[["path",{d:"M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z",key:"c2jq9f"}],["rect",{width:"4",height:"12",x:"2",y:"9",key:"mk3on5"}],["circle",{cx:"4",cy:"4",r:"2",key:"bt5ra8"}]])},2925:(t,e,r)=>{r.d(e,{A:()=>i});let i=(0,r(9946).A)("youtube",[["path",{d:"M2.5 17a24.12 24.12 0 0 1 0-10 2 2 0 0 1 1.4-1.4 49.56 49.56 0 0 1 16.2 0A2 2 0 0 1 21.5 7a24.12 24.12 0 0 1 0 10 2 2 0 0 1-1.4 1.4 49.55 49.55 0 0 1-16.2 0A2 2 0 0 1 2.5 17",key:"1q2vi4"}],["path",{d:"m10 15 5-3-5-3z",key:"1jp15x"}]])},3052:(t,e,r)=>{r.d(e,{A:()=>i});let i=(0,r(9946).A)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},3109:(t,e,r)=>{r.d(e,{A:()=>i});let i=(0,r(9946).A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},3311:(t,e,r)=>{r.d(e,{A:()=>i});let i=(0,r(9946).A)("sparkles",[["path",{d:"M11.017 2.814a1 1 0 0 1 1.966 0l1.051 5.558a2 2 0 0 0 1.594 1.594l5.558 1.051a1 1 0 0 1 0 1.966l-5.558 1.051a2 2 0 0 0-1.594 1.594l-1.051 5.558a1 1 0 0 1-1.966 0l-1.051-5.558a2 2 0 0 0-1.594-1.594l-5.558-1.051a1 1 0 0 1 0-1.966l5.558-1.051a2 2 0 0 0 1.594-1.594z",key:"1s2grr"}],["path",{d:"M20 2v4",key:"1rf3ol"}],["path",{d:"M22 4h-4",key:"gwowj6"}],["circle",{cx:"4",cy:"20",r:"2",key:"6kqj1y"}]])},4186:(t,e,r)=>{r.d(e,{A:()=>i});let i=(0,r(9946).A)("clock",[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},4416:(t,e,r)=>{r.d(e,{A:()=>i});let i=(0,r(9946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},4516:(t,e,r)=>{r.d(e,{A:()=>i});let i=(0,r(9946).A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},4738:(t,e,r)=>{r.d(e,{A:()=>i});let i=(0,r(9946).A)("monitor",[["rect",{width:"20",height:"14",x:"2",y:"3",rx:"2",key:"48i651"}],["line",{x1:"8",x2:"16",y1:"21",y2:"21",key:"1svkeh"}],["line",{x1:"12",x2:"12",y1:"17",y2:"21",key:"vw1qmm"}]])},4783:(t,e,r)=>{r.d(e,{A:()=>i});let i=(0,r(9946).A)("menu",[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]])},5040:(t,e,r)=>{r.d(e,{A:()=>i});let i=(0,r(9946).A)("book-open",[["path",{d:"M12 7v14",key:"1akyts"}],["path",{d:"M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z",key:"ruj8y"}]])},5196:(t,e,r)=>{r.d(e,{A:()=>i});let i=(0,r(9946).A)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},5273:(t,e,r)=>{r.d(e,{A:()=>i});let i=(0,r(9946).A)("volume-2",[["path",{d:"M11 4.702a.705.705 0 0 0-1.203-.498L6.413 7.587A1.4 1.4 0 0 1 5.416 8H3a1 1 0 0 0-1 1v6a1 1 0 0 0 1 1h2.416a1.4 1.4 0 0 1 .997.413l3.383 3.384A.705.705 0 0 0 11 19.298z",key:"uqj9uw"}],["path",{d:"M16 9a5 5 0 0 1 0 6",key:"1q6k2b"}],["path",{d:"M19.364 18.364a9 9 0 0 0 0-12.728",key:"ijwkga"}]])},5690:(t,e,r)=>{r.d(e,{A:()=>i});let i=(0,r(9946).A)("play",[["path",{d:"M5 5a2 2 0 0 1 3.008-1.728l11.997 6.998a2 2 0 0 1 .003 3.458l-12 7A2 2 0 0 1 5 19z",key:"10ikf1"}]])},6474:(t,e,r)=>{r.d(e,{A:()=>i});let i=(0,r(9946).A)("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},6740:(t,e,r)=>{r.d(e,{A:()=>i});let i=(0,r(9946).A)("calculator",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",key:"1nb95v"}],["line",{x1:"8",x2:"16",y1:"6",y2:"6",key:"x4nwl0"}],["line",{x1:"16",x2:"16",y1:"14",y2:"18",key:"wjye3r"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M8 14h.01",key:"6423bh"}],["path",{d:"M12 18h.01",key:"mhygvu"}],["path",{d:"M8 18h.01",key:"lrp35t"}]])},6983:(t,e,r)=>{r.d(e,{G:()=>i});function i(t){return"object"==typeof t&&null!==t}},7100:(t,e,r)=>{r.d(e,{A:()=>i});let i=(0,r(9946).A)("pen-tool",[["path",{d:"M15.707 21.293a1 1 0 0 1-1.414 0l-1.586-1.586a1 1 0 0 1 0-1.414l5.586-5.586a1 1 0 0 1 1.414 0l1.586 1.586a1 1 0 0 1 0 1.414z",key:"nt11vn"}],["path",{d:"m18 13-1.375-6.874a1 1 0 0 0-.746-.776L3.235 2.028a1 1 0 0 0-1.207 1.207L5.35 15.879a1 1 0 0 0 .776.746L13 18",key:"15qc1e"}],["path",{d:"m2.3 2.3 7.286 7.286",key:"1wuzzi"}],["circle",{cx:"11",cy:"11",r:"2",key:"xmgehs"}]])},7351:(t,e,r)=>{r.d(e,{s:()=>n});var i=r(6983);function n(t){return(0,i.G)(t)&&"offsetHeight"in t}},7395:(t,e,r)=>{r.d(e,{A:()=>i});let i=(0,r(9946).A)("headphones",[["path",{d:"M3 14h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-7a9 9 0 0 1 18 0v7a2 2 0 0 1-2 2h-1a2 2 0 0 1-2-2v-3a2 2 0 0 1 2-2h3",key:"1xhozi"}]])},7434:(t,e,r)=>{r.d(e,{A:()=>i});let i=(0,r(9946).A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},7494:(t,e,r)=>{r.d(e,{E:()=>n});var i=r(2115);let n=r(8972).B?i.useLayoutEffect:i.useEffect},7580:(t,e,r)=>{r.d(e,{A:()=>i});let i=(0,r(9946).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},7918:(t,e,r)=>{r.d(e,{A:()=>i});let i=(0,r(9946).A)("maximize",[["path",{d:"M8 3H5a2 2 0 0 0-2 2v3",key:"1dcmit"}],["path",{d:"M21 8V5a2 2 0 0 0-2-2h-3",key:"1e4gt3"}],["path",{d:"M3 16v3a2 2 0 0 0 2 2h3",key:"wsl5sc"}],["path",{d:"M16 21h3a2 2 0 0 0 2-2v-3",key:"18trek"}]])},7951:(t,e,r)=>{r.d(e,{A:()=>i});let i=(0,r(9946).A)("crown",[["path",{d:"M11.562 3.266a.5.5 0 0 1 .876 0L15.39 8.87a1 1 0 0 0 1.516.294L21.183 5.5a.5.5 0 0 1 .798.519l-2.834 10.246a1 1 0 0 1-.956.734H5.81a1 1 0 0 1-.957-.734L2.02 6.02a.5.5 0 0 1 .798-.519l4.276 3.664a1 1 0 0 0 1.516-.294z",key:"1vdc57"}],["path",{d:"M5 21h14",key:"11awu3"}]])},8274:(t,e,r)=>{let i;r.d(e,{P:()=>sf});var n=r(2115);let s=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],o=new Set(s),a=t=>180*t/Math.PI,l=t=>h(a(Math.atan2(t[1],t[0]))),u={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:t=>(Math.abs(t[0])+Math.abs(t[3]))/2,rotate:l,rotateZ:l,skewX:t=>a(Math.atan(t[1])),skewY:t=>a(Math.atan(t[2])),skew:t=>(Math.abs(t[1])+Math.abs(t[2]))/2},h=t=>((t%=360)<0&&(t+=360),t),c=t=>Math.sqrt(t[0]*t[0]+t[1]*t[1]),d=t=>Math.sqrt(t[4]*t[4]+t[5]*t[5]),p={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:c,scaleY:d,scale:t=>(c(t)+d(t))/2,rotateX:t=>h(a(Math.atan2(t[6],t[5]))),rotateY:t=>h(a(Math.atan2(-t[2],t[0]))),rotateZ:l,rotate:l,skewX:t=>a(Math.atan(t[4])),skewY:t=>a(Math.atan(t[1])),skew:t=>(Math.abs(t[1])+Math.abs(t[4]))/2};function f(t){return+!!t.includes("scale")}function m(t,e){let r,i;if(!t||"none"===t)return f(e);let n=t.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);if(n)r=p,i=n;else{let e=t.match(/^matrix\(([-\d.e\s,]+)\)$/u);r=u,i=e}if(!i)return f(e);let s=r[e],o=i[1].split(",").map(g);return"function"==typeof s?s(o):o[s]}function g(t){return parseFloat(t.trim())}let v=t=>e=>"string"==typeof e&&e.startsWith(t),y=v("--"),x=v("var(--"),b=t=>!!x(t)&&w.test(t.split("/*")[0].trim()),w=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu;function k({top:t,left:e,right:r,bottom:i}){return{x:{min:e,max:r},y:{min:t,max:i}}}let A=(t,e,r)=>t+(e-t)*r;function T(t){return void 0===t||1===t}function M({scale:t,scaleX:e,scaleY:r}){return!T(t)||!T(e)||!T(r)}function P(t){return M(t)||S(t)||t.z||t.rotate||t.rotateX||t.rotateY||t.skewX||t.skewY}function S(t){var e,r;return(e=t.x)&&"0%"!==e||(r=t.y)&&"0%"!==r}function E(t,e,r,i,n){return void 0!==n&&(t=i+n*(t-i)),i+r*(t-i)+e}function C(t,e=0,r=1,i,n){t.min=E(t.min,e,r,i,n),t.max=E(t.max,e,r,i,n)}function V(t,{x:e,y:r}){C(t.x,e.translate,e.scale,e.originPoint),C(t.y,r.translate,r.scale,r.originPoint)}function D(t,e){t.min=t.min+e,t.max=t.max+e}function O(t,e,r,i,n=.5){let s=A(t.min,t.max,n);C(t,e,r,s,i)}function R(t,e){O(t.x,e.x,e.scaleX,e.scale,e.originX),O(t.y,e.y,e.scaleY,e.scale,e.originY)}function _(t,e){return k(function(t,e){if(!e)return t;let r=e({x:t.left,y:t.top}),i=e({x:t.right,y:t.bottom});return{top:r.y,left:r.x,bottom:i.y,right:i.x}}(t.getBoundingClientRect(),e))}let j=new Set(["width","height","top","left","right","bottom",...s]),L=(t,e,r)=>r>e?e:r<t?t:r,z={test:t=>"number"==typeof t,parse:parseFloat,transform:t=>t},F={...z,transform:t=>L(0,1,t)},B={...z,default:1},I=t=>({test:e=>"string"==typeof e&&e.endsWith(t)&&1===e.split(" ").length,parse:parseFloat,transform:e=>`${e}${t}`}),N=I("deg"),Y=I("%"),U=I("px"),W=I("vh"),X=I("vw"),$={...Y,parse:t=>Y.parse(t)/100,transform:t=>Y.transform(100*t)},H=t=>e=>e.test(t),q=[z,U,Y,N,X,W,{test:t=>"auto"===t,parse:t=>t}],G=t=>q.find(H(t)),K=()=>{},Z=()=>{},Q=t=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(t),J=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u,tt=t=>t===z||t===U,te=new Set(["x","y","z"]),tr=s.filter(t=>!te.has(t)),ti={width:({x:t},{paddingLeft:e="0",paddingRight:r="0"})=>t.max-t.min-parseFloat(e)-parseFloat(r),height:({y:t},{paddingTop:e="0",paddingBottom:r="0"})=>t.max-t.min-parseFloat(e)-parseFloat(r),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:(t,{transform:e})=>m(e,"x"),y:(t,{transform:e})=>m(e,"y")};ti.translateX=ti.x,ti.translateY=ti.y;let tn=t=>t,ts={},to=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"],ta={value:null,addProjectionMetrics:null};function tl(t,e){let r=!1,i=!0,n={delta:0,timestamp:0,isProcessing:!1},s=()=>r=!0,o=to.reduce((t,r)=>(t[r]=function(t,e){let r=new Set,i=new Set,n=!1,s=!1,o=new WeakSet,a={delta:0,timestamp:0,isProcessing:!1},l=0;function u(e){o.has(e)&&(h.schedule(e),t()),l++,e(a)}let h={schedule:(t,e=!1,s=!1)=>{let a=s&&n?r:i;return e&&o.add(t),a.has(t)||a.add(t),t},cancel:t=>{i.delete(t),o.delete(t)},process:t=>{if(a=t,n){s=!0;return}n=!0,[r,i]=[i,r],r.forEach(u),e&&ta.value&&ta.value.frameloop[e].push(l),l=0,r.clear(),n=!1,s&&(s=!1,h.process(t))}};return h}(s,e?r:void 0),t),{}),{setup:a,read:l,resolveKeyframes:u,preUpdate:h,update:c,preRender:d,render:p,postRender:f}=o,m=()=>{let s=ts.useManualTiming?n.timestamp:performance.now();r=!1,ts.useManualTiming||(n.delta=i?1e3/60:Math.max(Math.min(s-n.timestamp,40),1)),n.timestamp=s,n.isProcessing=!0,a.process(n),l.process(n),u.process(n),h.process(n),c.process(n),d.process(n),p.process(n),f.process(n),n.isProcessing=!1,r&&e&&(i=!1,t(m))};return{schedule:to.reduce((e,s)=>{let a=o[s];return e[s]=(e,s=!1,o=!1)=>(!r&&(r=!0,i=!0,n.isProcessing||t(m)),a.schedule(e,s,o)),e},{}),cancel:t=>{for(let e=0;e<to.length;e++)o[to[e]].cancel(t)},state:n,steps:o}}let{schedule:tu,cancel:th,state:tc,steps:td}=tl("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:tn,!0),tp=new Set,tf=!1,tm=!1,tg=!1;function tv(){if(tm){let t=Array.from(tp).filter(t=>t.needsMeasurement),e=new Set(t.map(t=>t.element)),r=new Map;e.forEach(t=>{let e=function(t){let e=[];return tr.forEach(r=>{let i=t.getValue(r);void 0!==i&&(e.push([r,i.get()]),i.set(+!!r.startsWith("scale")))}),e}(t);e.length&&(r.set(t,e),t.render())}),t.forEach(t=>t.measureInitialState()),e.forEach(t=>{t.render();let e=r.get(t);e&&e.forEach(([e,r])=>{t.getValue(e)?.set(r)})}),t.forEach(t=>t.measureEndState()),t.forEach(t=>{void 0!==t.suspendedScrollY&&window.scrollTo(0,t.suspendedScrollY)})}tm=!1,tf=!1,tp.forEach(t=>t.complete(tg)),tp.clear()}function ty(){tp.forEach(t=>{t.readKeyframes(),t.needsMeasurement&&(tm=!0)})}class tx{constructor(t,e,r,i,n,s=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...t],this.onComplete=e,this.name=r,this.motionValue=i,this.element=n,this.isAsync=s}scheduleResolve(){this.state="scheduled",this.isAsync?(tp.add(this),tf||(tf=!0,tu.read(ty),tu.resolveKeyframes(tv))):(this.readKeyframes(),this.complete())}readKeyframes(){let{unresolvedKeyframes:t,name:e,element:r,motionValue:i}=this;if(null===t[0]){let n=i?.get(),s=t[t.length-1];if(void 0!==n)t[0]=n;else if(r&&e){let i=r.readValue(e,s);null!=i&&(t[0]=i)}void 0===t[0]&&(t[0]=s),i&&void 0===n&&i.set(t[0])}for(let e=1;e<t.length;e++)t[e]??(t[e]=t[e-1])}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(t=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,t),tp.delete(this)}cancel(){"scheduled"===this.state&&(tp.delete(this),this.state="pending")}resume(){"pending"===this.state&&this.scheduleResolve()}}let tb=t=>/^0[^.\s]+$/u.test(t),tw=t=>Math.round(1e5*t)/1e5,tk=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu,tA=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,tT=(t,e)=>r=>!!("string"==typeof r&&tA.test(r)&&r.startsWith(t)||e&&null!=r&&Object.prototype.hasOwnProperty.call(r,e)),tM=(t,e,r)=>i=>{if("string"!=typeof i)return i;let[n,s,o,a]=i.match(tk);return{[t]:parseFloat(n),[e]:parseFloat(s),[r]:parseFloat(o),alpha:void 0!==a?parseFloat(a):1}},tP={...z,transform:t=>Math.round(L(0,255,t))},tS={test:tT("rgb","red"),parse:tM("red","green","blue"),transform:({red:t,green:e,blue:r,alpha:i=1})=>"rgba("+tP.transform(t)+", "+tP.transform(e)+", "+tP.transform(r)+", "+tw(F.transform(i))+")"},tE={test:tT("#"),parse:function(t){let e="",r="",i="",n="";return t.length>5?(e=t.substring(1,3),r=t.substring(3,5),i=t.substring(5,7),n=t.substring(7,9)):(e=t.substring(1,2),r=t.substring(2,3),i=t.substring(3,4),n=t.substring(4,5),e+=e,r+=r,i+=i,n+=n),{red:parseInt(e,16),green:parseInt(r,16),blue:parseInt(i,16),alpha:n?parseInt(n,16)/255:1}},transform:tS.transform},tC={test:tT("hsl","hue"),parse:tM("hue","saturation","lightness"),transform:({hue:t,saturation:e,lightness:r,alpha:i=1})=>"hsla("+Math.round(t)+", "+Y.transform(tw(e))+", "+Y.transform(tw(r))+", "+tw(F.transform(i))+")"},tV={test:t=>tS.test(t)||tE.test(t)||tC.test(t),parse:t=>tS.test(t)?tS.parse(t):tC.test(t)?tC.parse(t):tE.parse(t),transform:t=>"string"==typeof t?t:t.hasOwnProperty("red")?tS.transform(t):tC.transform(t),getAnimatableNone:t=>{let e=tV.parse(t);return e.alpha=0,tV.transform(e)}},tD=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu,tO="number",tR="color",t_=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function tj(t){let e=t.toString(),r=[],i={color:[],number:[],var:[]},n=[],s=0,o=e.replace(t_,t=>(tV.test(t)?(i.color.push(s),n.push(tR),r.push(tV.parse(t))):t.startsWith("var(")?(i.var.push(s),n.push("var"),r.push(t)):(i.number.push(s),n.push(tO),r.push(parseFloat(t))),++s,"${}")).split("${}");return{values:r,split:o,indexes:i,types:n}}function tL(t){return tj(t).values}function tz(t){let{split:e,types:r}=tj(t),i=e.length;return t=>{let n="";for(let s=0;s<i;s++)if(n+=e[s],void 0!==t[s]){let e=r[s];e===tO?n+=tw(t[s]):e===tR?n+=tV.transform(t[s]):n+=t[s]}return n}}let tF=t=>"number"==typeof t?0:tV.test(t)?tV.getAnimatableNone(t):t,tB={test:function(t){return isNaN(t)&&"string"==typeof t&&(t.match(tk)?.length||0)+(t.match(tD)?.length||0)>0},parse:tL,createTransformer:tz,getAnimatableNone:function(t){let e=tL(t);return tz(t)(e.map(tF))}},tI=new Set(["brightness","contrast","saturate","opacity"]);function tN(t){let[e,r]=t.slice(0,-1).split("(");if("drop-shadow"===e)return t;let[i]=r.match(tk)||[];if(!i)return t;let n=r.replace(i,""),s=+!!tI.has(e);return i!==r&&(s*=100),e+"("+s+n+")"}let tY=/\b([a-z-]*)\(.*?\)/gu,tU={...tB,getAnimatableNone:t=>{let e=t.match(tY);return e?e.map(tN).join(" "):t}},tW={...z,transform:Math.round},tX={borderWidth:U,borderTopWidth:U,borderRightWidth:U,borderBottomWidth:U,borderLeftWidth:U,borderRadius:U,radius:U,borderTopLeftRadius:U,borderTopRightRadius:U,borderBottomRightRadius:U,borderBottomLeftRadius:U,width:U,maxWidth:U,height:U,maxHeight:U,top:U,right:U,bottom:U,left:U,padding:U,paddingTop:U,paddingRight:U,paddingBottom:U,paddingLeft:U,margin:U,marginTop:U,marginRight:U,marginBottom:U,marginLeft:U,backgroundPositionX:U,backgroundPositionY:U,rotate:N,rotateX:N,rotateY:N,rotateZ:N,scale:B,scaleX:B,scaleY:B,scaleZ:B,skew:N,skewX:N,skewY:N,distance:U,translateX:U,translateY:U,translateZ:U,x:U,y:U,z:U,perspective:U,transformPerspective:U,opacity:F,originX:$,originY:$,originZ:U,zIndex:tW,fillOpacity:F,strokeOpacity:F,numOctaves:tW},t$={...tX,color:tV,backgroundColor:tV,outlineColor:tV,fill:tV,stroke:tV,borderColor:tV,borderTopColor:tV,borderRightColor:tV,borderBottomColor:tV,borderLeftColor:tV,filter:tU,WebkitFilter:tU},tH=t=>t$[t];function tq(t,e){let r=tH(t);return r!==tU&&(r=tB),r.getAnimatableNone?r.getAnimatableNone(e):void 0}let tG=new Set(["auto","none","0"]);class tK extends tx{constructor(t,e,r,i,n){super(t,e,r,i,n,!0)}readKeyframes(){let{unresolvedKeyframes:t,element:e,name:r}=this;if(!e||!e.current)return;super.readKeyframes();for(let r=0;r<t.length;r++){let i=t[r];if("string"==typeof i&&b(i=i.trim())){let n=function t(e,r,i=1){Z(i<=4,`Max CSS variable fallback depth detected in property "${e}". This may indicate a circular fallback dependency.`,"max-css-var-depth");let[n,s]=function(t){let e=J.exec(t);if(!e)return[,];let[,r,i,n]=e;return[`--${r??i}`,n]}(e);if(!n)return;let o=window.getComputedStyle(r).getPropertyValue(n);if(o){let t=o.trim();return Q(t)?parseFloat(t):t}return b(s)?t(s,r,i+1):s}(i,e.current);void 0!==n&&(t[r]=n),r===t.length-1&&(this.finalKeyframe=i)}}if(this.resolveNoneKeyframes(),!j.has(r)||2!==t.length)return;let[i,n]=t,s=G(i),o=G(n);if(s!==o)if(tt(s)&&tt(o))for(let e=0;e<t.length;e++){let r=t[e];"string"==typeof r&&(t[e]=parseFloat(r))}else ti[r]&&(this.needsMeasurement=!0)}resolveNoneKeyframes(){let{unresolvedKeyframes:t,name:e}=this,r=[];for(let e=0;e<t.length;e++){var i;(null===t[e]||("number"==typeof(i=t[e])?0===i:null===i||"none"===i||"0"===i||tb(i)))&&r.push(e)}r.length&&function(t,e,r){let i,n=0;for(;n<t.length&&!i;){let e=t[n];"string"==typeof e&&!tG.has(e)&&tj(e).values.length&&(i=t[n]),n++}if(i&&r)for(let n of e)t[n]=tq(r,i)}(t,r,e)}measureInitialState(){let{element:t,unresolvedKeyframes:e,name:r}=this;if(!t||!t.current)return;"height"===r&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=ti[r](t.measureViewportBox(),window.getComputedStyle(t.current)),e[0]=this.measuredOrigin;let i=e[e.length-1];void 0!==i&&t.getValue(r,i).jump(i,!1)}measureEndState(){let{element:t,name:e,unresolvedKeyframes:r}=this;if(!t||!t.current)return;let i=t.getValue(e);i&&i.jump(this.measuredOrigin,!1);let n=r.length-1,s=r[n];r[n]=ti[e](t.measureViewportBox(),window.getComputedStyle(t.current)),null!==s&&void 0===this.finalKeyframe&&(this.finalKeyframe=s),this.removedTransforms?.length&&this.removedTransforms.forEach(([e,r])=>{t.getValue(e).set(r)}),this.resolveNoneKeyframes()}}let tZ=t=>!!(t&&t.getVelocity);function tQ(){i=void 0}let tJ={now:()=>(void 0===i&&tJ.set(tc.isProcessing||ts.useManualTiming?tc.timestamp:performance.now()),i),set:t=>{i=t,queueMicrotask(tQ)}};function t0(t,e){-1===t.indexOf(e)&&t.push(e)}function t1(t,e){let r=t.indexOf(e);r>-1&&t.splice(r,1)}class t2{constructor(){this.subscriptions=[]}add(t){return t0(this.subscriptions,t),()=>t1(this.subscriptions,t)}notify(t,e,r){let i=this.subscriptions.length;if(i)if(1===i)this.subscriptions[0](t,e,r);else for(let n=0;n<i;n++){let i=this.subscriptions[n];i&&i(t,e,r)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}let t5={current:void 0};class t3{constructor(t,e={}){this.canTrackVelocity=null,this.events={},this.updateAndNotify=t=>{let e=tJ.now();if(this.updatedAt!==e&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(t),this.current!==this.prev&&(this.events.change?.notify(this.current),this.dependents))for(let t of this.dependents)t.dirty()},this.hasAnimated=!1,this.setCurrent(t),this.owner=e.owner}setCurrent(t){this.current=t,this.updatedAt=tJ.now(),null===this.canTrackVelocity&&void 0!==t&&(this.canTrackVelocity=!isNaN(parseFloat(this.current)))}setPrevFrameValue(t=this.current){this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt}onChange(t){return this.on("change",t)}on(t,e){this.events[t]||(this.events[t]=new t2);let r=this.events[t].add(e);return"change"===t?()=>{r(),tu.read(()=>{this.events.change.getSize()||this.stop()})}:r}clearListeners(){for(let t in this.events)this.events[t].clear()}attach(t,e){this.passiveEffect=t,this.stopPassiveEffect=e}set(t){this.passiveEffect?this.passiveEffect(t,this.updateAndNotify):this.updateAndNotify(t)}setWithVelocity(t,e,r){this.set(e),this.prev=void 0,this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt-r}jump(t,e=!0){this.updateAndNotify(t),this.prev=t,this.prevUpdatedAt=this.prevFrameValue=void 0,e&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){this.events.change?.notify(this.current)}addDependent(t){this.dependents||(this.dependents=new Set),this.dependents.add(t)}removeDependent(t){this.dependents&&this.dependents.delete(t)}get(){return t5.current&&t5.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){var t;let e=tJ.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||e-this.updatedAt>30)return 0;let r=Math.min(this.updatedAt-this.prevUpdatedAt,30);return t=parseFloat(this.current)-parseFloat(this.prevFrameValue),r?1e3/r*t:0}start(t){return this.stop(),new Promise(e=>{this.hasAnimated=!0,this.animation=t(e),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.dependents?.clear(),this.events.destroy?.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function t4(t,e){return new t3(t,e)}let t6=[...q,tV,tB],{schedule:t9}=tl(queueMicrotask,!1),t8={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},t7={};for(let t in t8)t7[t]={isEnabled:e=>t8[t].some(t=>!!e[t])};let et=()=>({translate:0,scale:1,origin:0,originPoint:0}),ee=()=>({x:et(),y:et()}),er=()=>({min:0,max:0}),ei=()=>({x:er(),y:er()});var en=r(8972);let es={current:null},eo={current:!1},ea=new WeakMap;function el(t){return null!==t&&"object"==typeof t&&"function"==typeof t.start}function eu(t){return"string"==typeof t||Array.isArray(t)}let eh=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],ec=["initial",...eh];function ed(t){return el(t.animate)||ec.some(e=>eu(t[e]))}function ep(t){return!!(ed(t)||t.variants)}function ef(t){let e=[{},{}];return t?.values.forEach((t,r)=>{e[0][r]=t.get(),e[1][r]=t.getVelocity()}),e}function em(t,e,r,i){if("function"==typeof e){let[n,s]=ef(i);e=e(void 0!==r?r:t.custom,n,s)}if("string"==typeof e&&(e=t.variants&&t.variants[e]),"function"==typeof e){let[n,s]=ef(i);e=e(void 0!==r?r:t.custom,n,s)}return e}let eg=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class ev{scrapeMotionValuesFromProps(t,e,r){return{}}constructor({parent:t,props:e,presenceContext:r,reducedMotionConfig:i,blockInitialAnimation:n,visualState:s},o={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=tx,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{let t=tJ.now();this.renderScheduledAt<t&&(this.renderScheduledAt=t,tu.render(this.render,!1,!0))};let{latestValues:a,renderState:l}=s;this.latestValues=a,this.baseTarget={...a},this.initialValues=e.initial?{...a}:{},this.renderState=l,this.parent=t,this.props=e,this.presenceContext=r,this.depth=t?t.depth+1:0,this.reducedMotionConfig=i,this.options=o,this.blockInitialAnimation=!!n,this.isControllingVariants=ed(e),this.isVariantNode=ep(e),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(t&&t.current);let{willChange:u,...h}=this.scrapeMotionValuesFromProps(e,{},this);for(let t in h){let e=h[t];void 0!==a[t]&&tZ(e)&&e.set(a[t])}}mount(t){this.current=t,ea.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((t,e)=>this.bindToMotionValue(e,t)),eo.current||function(){if(eo.current=!0,en.B)if(window.matchMedia){let t=window.matchMedia("(prefers-reduced-motion)"),e=()=>es.current=t.matches;t.addEventListener("change",e),e()}else es.current=!1}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||es.current),this.parent?.addChild(this),this.update(this.props,this.presenceContext)}unmount(){for(let t in this.projection&&this.projection.unmount(),th(this.notifyUpdate),th(this.render),this.valueSubscriptions.forEach(t=>t()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent?.removeChild(this),this.events)this.events[t].clear();for(let t in this.features){let e=this.features[t];e&&(e.unmount(),e.isMounted=!1)}this.current=null}addChild(t){this.children.add(t),this.enteringChildren??(this.enteringChildren=new Set),this.enteringChildren.add(t)}removeChild(t){this.children.delete(t),this.enteringChildren&&this.enteringChildren.delete(t)}bindToMotionValue(t,e){let r;this.valueSubscriptions.has(t)&&this.valueSubscriptions.get(t)();let i=o.has(t);i&&this.onBindTransform&&this.onBindTransform();let n=e.on("change",e=>{this.latestValues[t]=e,this.props.onUpdate&&tu.preRender(this.notifyUpdate),i&&this.projection&&(this.projection.isTransformDirty=!0),this.scheduleRender()});window.MotionCheckAppearSync&&(r=window.MotionCheckAppearSync(this,t,e)),this.valueSubscriptions.set(t,()=>{n(),r&&r(),e.owner&&e.stop()})}sortNodePosition(t){return this.current&&this.sortInstanceNodePosition&&this.type===t.type?this.sortInstanceNodePosition(this.current,t.current):0}updateFeatures(){let t="animation";for(t in t7){let e=t7[t];if(!e)continue;let{isEnabled:r,Feature:i}=e;if(!this.features[t]&&i&&r(this.props)&&(this.features[t]=new i(this)),this.features[t]){let e=this.features[t];e.isMounted?e.update():(e.mount(),e.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):ei()}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,e){this.latestValues[t]=e}update(t,e){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=e;for(let e=0;e<eg.length;e++){let r=eg[e];this.propEventSubscriptions[r]&&(this.propEventSubscriptions[r](),delete this.propEventSubscriptions[r]);let i=t["on"+r];i&&(this.propEventSubscriptions[r]=this.on(r,i))}this.prevMotionValues=function(t,e,r){for(let i in e){let n=e[i],s=r[i];if(tZ(n))t.addValue(i,n);else if(tZ(s))t.addValue(i,t4(n,{owner:t}));else if(s!==n)if(t.hasValue(i)){let e=t.getValue(i);!0===e.liveStyle?e.jump(n):e.hasAnimated||e.set(n)}else{let e=t.getStaticValue(i);t.addValue(i,t4(void 0!==e?e:n,{owner:t}))}}for(let i in r)void 0===e[i]&&t.removeValue(i);return e}(this,this.scrapeMotionValuesFromProps(t,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(t){let e=this.getClosestVariantNode();if(e)return e.variantChildren&&e.variantChildren.add(t),()=>e.variantChildren.delete(t)}addValue(t,e){let r=this.values.get(t);e!==r&&(r&&this.removeValue(t),this.bindToMotionValue(t,e),this.values.set(t,e),this.latestValues[t]=e.get())}removeValue(t){this.values.delete(t);let e=this.valueSubscriptions.get(t);e&&(e(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,e){if(this.props.values&&this.props.values[t])return this.props.values[t];let r=this.values.get(t);return void 0===r&&void 0!==e&&(r=t4(null===e?void 0:e,{owner:this}),this.addValue(t,r)),r}readValue(t,e){let r=void 0===this.latestValues[t]&&this.current?this.getBaseTargetFromProps(this.props,t)??this.readValueFromInstance(this.current,t,this.options):this.latestValues[t];if(null!=r){if("string"==typeof r&&(Q(r)||tb(r)))r=parseFloat(r);else{let i;i=r,!t6.find(H(i))&&tB.test(e)&&(r=tq(t,e))}this.setBaseTarget(t,tZ(r)?r.get():r)}return tZ(r)?r.get():r}setBaseTarget(t,e){this.baseTarget[t]=e}getBaseTarget(t){let e,{initial:r}=this.props;if("string"==typeof r||"object"==typeof r){let i=em(this.props,r,this.presenceContext?.custom);i&&(e=i[t])}if(r&&void 0!==e)return e;let i=this.getBaseTargetFromProps(this.props,t);return void 0===i||tZ(i)?void 0!==this.initialValues[t]&&void 0===e?void 0:this.baseTarget[t]:i}on(t,e){return this.events[t]||(this.events[t]=new t2),this.events[t].add(e)}notify(t,...e){this.events[t]&&this.events[t].notify(...e)}scheduleRenderMicrotask(){t9.render(this.render)}}class ey extends ev{constructor(){super(...arguments),this.KeyframeResolver=tK}sortInstanceNodePosition(t,e){return 2&t.compareDocumentPosition(e)?1:-1}getBaseTargetFromProps(t,e){return t.style?t.style[e]:void 0}removeValueFromRenderState(t,{vars:e,style:r}){delete e[t],delete r[t]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:t}=this.props;tZ(t)&&(this.childSubscription=t.on("change",t=>{this.current&&(this.current.textContent=`${t}`)}))}}let ex=(t,e)=>e&&"number"==typeof t?e.transform(t):t,eb={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},ew=s.length;function ek(t,e,r){let{style:i,vars:n,transformOrigin:a}=t,l=!1,u=!1;for(let t in e){let r=e[t];if(o.has(t)){l=!0;continue}if(y(t)){n[t]=r;continue}{let e=ex(r,tX[t]);t.startsWith("origin")?(u=!0,a[t]=e):i[t]=e}}if(!e.transform&&(l||r?i.transform=function(t,e,r){let i="",n=!0;for(let o=0;o<ew;o++){let a=s[o],l=t[a];if(void 0===l)continue;let u=!0;if(!(u="number"==typeof l?l===+!!a.startsWith("scale"):0===parseFloat(l))||r){let t=ex(l,tX[a]);if(!u){n=!1;let e=eb[a]||a;i+=`${e}(${t}) `}r&&(e[a]=t)}}return i=i.trim(),r?i=r(e,n?"":i):n&&(i="none"),i}(e,t.transform,r):i.transform&&(i.transform="none")),u){let{originX:t="50%",originY:e="50%",originZ:r=0}=a;i.transformOrigin=`${t} ${e} ${r}`}}function eA(t,{style:e,vars:r},i,n){let s,o=t.style;for(s in e)o[s]=e[s];for(s in n?.applyProjectionStyles(o,i),r)o.setProperty(s,r[s])}let eT={};function eM(t,{layout:e,layoutId:r}){return o.has(t)||t.startsWith("origin")||(e||void 0!==r)&&(!!eT[t]||"opacity"===t)}function eP(t,e,r){let{style:i}=t,n={};for(let s in i)(tZ(i[s])||e.style&&tZ(e.style[s])||eM(s,t)||r?.getValue(s)?.liveStyle!==void 0)&&(n[s]=i[s]);return n}class eS extends ey{constructor(){super(...arguments),this.type="html",this.renderInstance=eA}readValueFromInstance(t,e){if(o.has(e))return this.projection?.isProjecting?f(e):((t,e)=>{let{transform:r="none"}=getComputedStyle(t);return m(r,e)})(t,e);{let r=window.getComputedStyle(t),i=(y(e)?r.getPropertyValue(e):r[e])||0;return"string"==typeof i?i.trim():i}}measureInstanceViewportBox(t,{transformPagePoint:e}){return _(t,e)}build(t,e,r){ek(t,e,r.transformTemplate)}scrapeMotionValuesFromProps(t,e,r){return eP(t,e,r)}}let eE=t=>t.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),eC={offset:"stroke-dashoffset",array:"stroke-dasharray"},eV={offset:"strokeDashoffset",array:"strokeDasharray"};function eD(t,{attrX:e,attrY:r,attrScale:i,pathLength:n,pathSpacing:s=1,pathOffset:o=0,...a},l,u,h){if(ek(t,a,u),l){t.style.viewBox&&(t.attrs.viewBox=t.style.viewBox);return}t.attrs=t.style,t.style={};let{attrs:c,style:d}=t;c.transform&&(d.transform=c.transform,delete c.transform),(d.transform||c.transformOrigin)&&(d.transformOrigin=c.transformOrigin??"50% 50%",delete c.transformOrigin),d.transform&&(d.transformBox=h?.transformBox??"fill-box",delete c.transformBox),void 0!==e&&(c.x=e),void 0!==r&&(c.y=r),void 0!==i&&(c.scale=i),void 0!==n&&function(t,e,r=1,i=0,n=!0){t.pathLength=1;let s=n?eC:eV;t[s.offset]=U.transform(-i);let o=U.transform(e),a=U.transform(r);t[s.array]=`${o} ${a}`}(c,n,s,o,!1)}let eO=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]),eR=t=>"string"==typeof t&&"svg"===t.toLowerCase();function e_(t,e,r){let i=eP(t,e,r);for(let r in t)(tZ(t[r])||tZ(e[r]))&&(i[-1!==s.indexOf(r)?"attr"+r.charAt(0).toUpperCase()+r.substring(1):r]=t[r]);return i}class ej extends ey{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=ei}getBaseTargetFromProps(t,e){return t[e]}readValueFromInstance(t,e){if(o.has(e)){let t=tH(e);return t&&t.default||0}return e=eO.has(e)?e:eE(e),t.getAttribute(e)}scrapeMotionValuesFromProps(t,e,r){return e_(t,e,r)}build(t,e,r){eD(t,e,this.isSVGTag,r.transformTemplate,r.style)}renderInstance(t,e,r,i){for(let r in eA(t,e,void 0,i),e.attrs)t.setAttribute(eO.has(r)?r:eE(r),e.attrs[r])}mount(t){this.isSVGTag=eR(t.tagName),super.mount(t)}}let eL=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function ez(t){if("string"!=typeof t||t.includes("-"));else if(eL.indexOf(t)>-1||/[A-Z]/u.test(t))return!0;return!1}var eF=r(5155),eB=r(869);let eI=(0,n.createContext)({strict:!1});var eN=r(1508);let eY=(0,n.createContext)({});function eU(t){return Array.isArray(t)?t.join(" "):t}let eW=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function eX(t,e,r){for(let i in e)tZ(e[i])||eM(i,r)||(t[i]=e[i])}let e$=()=>({...eW(),attrs:{}}),eH=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function eq(t){return t.startsWith("while")||t.startsWith("drag")&&"draggable"!==t||t.startsWith("layout")||t.startsWith("onTap")||t.startsWith("onPan")||t.startsWith("onLayout")||eH.has(t)}let eG=t=>!eq(t);try{!function(t){"function"==typeof t&&(eG=e=>e.startsWith("on")?!eq(e):t(e))}(require("@emotion/is-prop-valid").default)}catch{}var eK=r(845),eZ=r(2885);function eQ(t){return tZ(t)?t.get():t}let eJ=t=>(e,r)=>{let i=(0,n.useContext)(eY),s=(0,n.useContext)(eK.t),o=()=>(function({scrapeMotionValuesFromProps:t,createRenderState:e},r,i,n){return{latestValues:function(t,e,r,i){let n={},s=i(t,{});for(let t in s)n[t]=eQ(s[t]);let{initial:o,animate:a}=t,l=ed(t),u=ep(t);e&&u&&!l&&!1!==t.inherit&&(void 0===o&&(o=e.initial),void 0===a&&(a=e.animate));let h=!!r&&!1===r.initial,c=(h=h||!1===o)?a:o;if(c&&"boolean"!=typeof c&&!el(c)){let e=Array.isArray(c)?c:[c];for(let r=0;r<e.length;r++){let i=em(t,e[r]);if(i){let{transitionEnd:t,transition:e,...r}=i;for(let t in r){let e=r[t];if(Array.isArray(e)){let t=h?e.length-1:0;e=e[t]}null!==e&&(n[t]=e)}for(let e in t)n[e]=t[e]}}}return n}(r,i,n,t),renderState:e()}})(t,e,i,s);return r?o():(0,eZ.M)(o)},e0=eJ({scrapeMotionValuesFromProps:eP,createRenderState:eW}),e1=eJ({scrapeMotionValuesFromProps:e_,createRenderState:e$}),e2=Symbol.for("motionComponentSymbol");function e5(t){return t&&"object"==typeof t&&Object.prototype.hasOwnProperty.call(t,"current")}let e3="data-"+eE("framerAppearId"),e4=(0,n.createContext)({});var e6=r(7494);function e9(t){var e,r;let{forwardMotionProps:i=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},s=arguments.length>2?arguments[2]:void 0,o=arguments.length>3?arguments[3]:void 0;s&&function(t){for(let e in t)t7[e]={...t7[e],...t[e]}}(s);let a=ez(t)?e1:e0;function l(e,r){var s;let l,u={...(0,n.useContext)(eN.Q),...e,layoutId:function(t){let{layoutId:e}=t,r=(0,n.useContext)(eB.L).id;return r&&void 0!==e?r+"-"+e:e}(e)},{isStatic:h}=u,c=function(t){let{initial:e,animate:r}=function(t,e){if(ed(t)){let{initial:e,animate:r}=t;return{initial:!1===e||eu(e)?e:void 0,animate:eu(r)?r:void 0}}return!1!==t.inherit?e:{}}(t,(0,n.useContext)(eY));return(0,n.useMemo)(()=>({initial:e,animate:r}),[eU(e),eU(r)])}(e),d=a(e,h);if(!h&&en.B){(0,n.useContext)(eI).strict;let e=function(t){let{drag:e,layout:r}=t7;if(!e&&!r)return{};let i={...e,...r};return{MeasureLayout:(null==e?void 0:e.isEnabled(t))||(null==r?void 0:r.isEnabled(t))?i.MeasureLayout:void 0,ProjectionNode:i.ProjectionNode}}(u);l=e.MeasureLayout,c.visualElement=function(t,e,r,i,s){let{visualElement:o}=(0,n.useContext)(eY),a=(0,n.useContext)(eI),l=(0,n.useContext)(eK.t),u=(0,n.useContext)(eN.Q).reducedMotion,h=(0,n.useRef)(null);i=i||a.renderer,!h.current&&i&&(h.current=i(t,{visualState:e,parent:o,props:r,presenceContext:l,blockInitialAnimation:!!l&&!1===l.initial,reducedMotionConfig:u}));let c=h.current,d=(0,n.useContext)(e4);c&&!c.projection&&s&&("html"===c.type||"svg"===c.type)&&function(t,e,r,i){let{layoutId:n,layout:s,drag:o,dragConstraints:a,layoutScroll:l,layoutRoot:u,layoutCrossfade:h}=e;t.projection=new r(t.latestValues,e["data-framer-portal-id"]?void 0:function t(e){if(e)return!1!==e.options.allowProjection?e.projection:t(e.parent)}(t.parent)),t.projection.setOptions({layoutId:n,layout:s,alwaysMeasureLayout:!!o||a&&e5(a),visualElement:t,animationType:"string"==typeof s?s:"both",initialPromotionConfig:i,crossfade:h,layoutScroll:l,layoutRoot:u})}(h.current,r,s,d);let p=(0,n.useRef)(!1);(0,n.useInsertionEffect)(()=>{c&&p.current&&c.update(r,l)});let f=r[e3],m=(0,n.useRef)(!!f&&!window.MotionHandoffIsComplete?.(f)&&window.MotionHasOptimisedAnimation?.(f));return(0,e6.E)(()=>{c&&(p.current=!0,window.MotionIsMounted=!0,c.updateFeatures(),c.scheduleRenderMicrotask(),m.current&&c.animationState&&c.animationState.animateChanges())}),(0,n.useEffect)(()=>{c&&(!m.current&&c.animationState&&c.animationState.animateChanges(),m.current&&(queueMicrotask(()=>{window.MotionHandoffMarkAsComplete?.(f)}),m.current=!1),c.enteringChildren=void 0)}),c}(t,d,u,o,e.ProjectionNode)}return(0,eF.jsxs)(eY.Provider,{value:c,children:[l&&c.visualElement?(0,eF.jsx)(l,{visualElement:c.visualElement,...u}):null,function(t,e,r,{latestValues:i},s,o=!1){let a=(ez(t)?function(t,e,r,i){let s=(0,n.useMemo)(()=>{let r=e$();return eD(r,e,eR(i),t.transformTemplate,t.style),{...r.attrs,style:{...r.style}}},[e]);if(t.style){let e={};eX(e,t.style,t),s.style={...e,...s.style}}return s}:function(t,e){let r={},i=function(t,e){let r=t.style||{},i={};return eX(i,r,t),Object.assign(i,function({transformTemplate:t},e){return(0,n.useMemo)(()=>{let r=eW();return ek(r,e,t),Object.assign({},r.vars,r.style)},[e])}(t,e)),i}(t,e);return t.drag&&!1!==t.dragListener&&(r.draggable=!1,i.userSelect=i.WebkitUserSelect=i.WebkitTouchCallout="none",i.touchAction=!0===t.drag?"none":`pan-${"x"===t.drag?"y":"x"}`),void 0===t.tabIndex&&(t.onTap||t.onTapStart||t.whileTap)&&(r.tabIndex=0),r.style=i,r})(e,i,s,t),l=function(t,e,r){let i={};for(let n in t)("values"!==n||"object"!=typeof t.values)&&(eG(n)||!0===r&&eq(n)||!e&&!eq(n)||t.draggable&&n.startsWith("onDrag"))&&(i[n]=t[n]);return i}(e,"string"==typeof t,o),u=t!==n.Fragment?{...l,...a,ref:r}:{},{children:h}=e,c=(0,n.useMemo)(()=>tZ(h)?h.get():h,[h]);return(0,n.createElement)(t,{...u,children:c})}(t,e,(s=c.visualElement,(0,n.useCallback)(t=>{t&&d.onMount&&d.onMount(t),s&&(t?s.mount(t):s.unmount()),r&&("function"==typeof r?r(t):e5(r)&&(r.current=t))},[s])),d,h,i)]})}l.displayName="motion.".concat("string"==typeof t?t:"create(".concat(null!=(r=null!=(e=t.displayName)?e:t.name)?r:"",")"));let u=(0,n.forwardRef)(l);return u[e2]=t,u}function e8(t,e,r){let i=t.getProps();return em(i,e,void 0!==r?r:i.custom,t)}function e7(t,e){return t?.[e]??t?.default??t}let rt=t=>Array.isArray(t);function re(t,e){let r=t.getValue("willChange");if(tZ(r)&&r.add)return r.add(e);if(!r&&ts.WillChange){let r=new ts.WillChange("auto");t.addValue("willChange",r),r.add(e)}}function rr(t){t.duration=0,t.type}let ri=(t,e)=>r=>e(t(r)),rn=(...t)=>t.reduce(ri),rs=t=>1e3*t,ro={layout:0,mainThread:0,waapi:0};function ra(t,e,r){return(r<0&&(r+=1),r>1&&(r-=1),r<1/6)?t+(e-t)*6*r:r<.5?e:r<2/3?t+(e-t)*(2/3-r)*6:t}function rl(t,e){return r=>r>0?e:t}let ru=(t,e,r)=>{let i=t*t,n=r*(e*e-i)+i;return n<0?0:Math.sqrt(n)},rh=[tE,tS,tC];function rc(t){let e=rh.find(e=>e.test(t));if(K(!!e,`'${t}' is not an animatable color. Use the equivalent color code instead.`,"color-not-animatable"),!e)return!1;let r=e.parse(t);return e===tC&&(r=function({hue:t,saturation:e,lightness:r,alpha:i}){t/=360,r/=100;let n=0,s=0,o=0;if(e/=100){let i=r<.5?r*(1+e):r+e-r*e,a=2*r-i;n=ra(a,i,t+1/3),s=ra(a,i,t),o=ra(a,i,t-1/3)}else n=s=o=r;return{red:Math.round(255*n),green:Math.round(255*s),blue:Math.round(255*o),alpha:i}}(r)),r}let rd=(t,e)=>{let r=rc(t),i=rc(e);if(!r||!i)return rl(t,e);let n={...r};return t=>(n.red=ru(r.red,i.red,t),n.green=ru(r.green,i.green,t),n.blue=ru(r.blue,i.blue,t),n.alpha=A(r.alpha,i.alpha,t),tS.transform(n))},rp=new Set(["none","hidden"]);function rf(t,e){return r=>A(t,e,r)}function rm(t){return"number"==typeof t?rf:"string"==typeof t?b(t)?rl:tV.test(t)?rd:ry:Array.isArray(t)?rg:"object"==typeof t?tV.test(t)?rd:rv:rl}function rg(t,e){let r=[...t],i=r.length,n=t.map((t,r)=>rm(t)(t,e[r]));return t=>{for(let e=0;e<i;e++)r[e]=n[e](t);return r}}function rv(t,e){let r={...t,...e},i={};for(let n in r)void 0!==t[n]&&void 0!==e[n]&&(i[n]=rm(t[n])(t[n],e[n]));return t=>{for(let e in i)r[e]=i[e](t);return r}}let ry=(t,e)=>{let r=tB.createTransformer(e),i=tj(t),n=tj(e);return i.indexes.var.length===n.indexes.var.length&&i.indexes.color.length===n.indexes.color.length&&i.indexes.number.length>=n.indexes.number.length?rp.has(t)&&!n.values.length||rp.has(e)&&!i.values.length?function(t,e){return rp.has(t)?r=>r<=0?t:e:r=>r>=1?e:t}(t,e):rn(rg(function(t,e){let r=[],i={color:0,var:0,number:0};for(let n=0;n<e.values.length;n++){let s=e.types[n],o=t.indexes[s][i[s]],a=t.values[o]??0;r[n]=a,i[s]++}return r}(i,n),n.values),r):(K(!0,`Complex values '${t}' and '${e}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`,"complex-values-different"),rl(t,e))};function rx(t,e,r){return"number"==typeof t&&"number"==typeof e&&"number"==typeof r?A(t,e,r):rm(t)(t,e)}let rb=t=>{let e=({timestamp:e})=>t(e);return{start:(t=!0)=>tu.update(e,t),stop:()=>th(e),now:()=>tc.isProcessing?tc.timestamp:tJ.now()}},rw=(t,e,r=10)=>{let i="",n=Math.max(Math.round(e/r),2);for(let e=0;e<n;e++)i+=Math.round(1e4*t(e/(n-1)))/1e4+", ";return`linear(${i.substring(0,i.length-2)})`};function rk(t){let e=0,r=t.next(e);for(;!r.done&&e<2e4;)e+=50,r=t.next(e);return e>=2e4?1/0:e}function rA(t,e,r){var i,n;let s=Math.max(e-5,0);return i=r-t(s),(n=e-s)?1e3/n*i:0}let rT={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1};function rM(t,e){return t*Math.sqrt(1-e*e)}let rP=["duration","bounce"],rS=["stiffness","damping","mass"];function rE(t,e){return e.some(e=>void 0!==t[e])}function rC(t=rT.visualDuration,e=rT.bounce){let r,i="object"!=typeof t?{visualDuration:t,keyframes:[0,1],bounce:e}:t,{restSpeed:n,restDelta:s}=i,o=i.keyframes[0],a=i.keyframes[i.keyframes.length-1],l={done:!1,value:o},{stiffness:u,damping:h,mass:c,duration:d,velocity:p,isResolvedFromDuration:f}=function(t){let e={velocity:rT.velocity,stiffness:rT.stiffness,damping:rT.damping,mass:rT.mass,isResolvedFromDuration:!1,...t};if(!rE(t,rS)&&rE(t,rP))if(t.visualDuration){let r=2*Math.PI/(1.2*t.visualDuration),i=r*r,n=2*L(.05,1,1-(t.bounce||0))*Math.sqrt(i);e={...e,mass:rT.mass,stiffness:i,damping:n}}else{let r=function({duration:t=rT.duration,bounce:e=rT.bounce,velocity:r=rT.velocity,mass:i=rT.mass}){let n,s;K(t<=rs(rT.maxDuration),"Spring duration must be 10 seconds or less","spring-duration-limit");let o=1-e;o=L(rT.minDamping,rT.maxDamping,o),t=L(rT.minDuration,rT.maxDuration,t/1e3),o<1?(n=e=>{let i=e*o,n=i*t;return .001-(i-r)/rM(e,o)*Math.exp(-n)},s=e=>{let i=e*o*t,s=Math.pow(o,2)*Math.pow(e,2)*t,a=Math.exp(-i),l=rM(Math.pow(e,2),o);return(i*r+r-s)*a*(-n(e)+.001>0?-1:1)/l}):(n=e=>-.001+Math.exp(-e*t)*((e-r)*t+1),s=e=>t*t*(r-e)*Math.exp(-e*t));let a=function(t,e,r){let i=r;for(let r=1;r<12;r++)i-=t(i)/e(i);return i}(n,s,5/t);if(t=rs(t),isNaN(a))return{stiffness:rT.stiffness,damping:rT.damping,duration:t};{let e=Math.pow(a,2)*i;return{stiffness:e,damping:2*o*Math.sqrt(i*e),duration:t}}}(t);(e={...e,...r,mass:rT.mass}).isResolvedFromDuration=!0}return e}({...i,velocity:-((i.velocity||0)/1e3)}),m=p||0,g=h/(2*Math.sqrt(u*c)),v=a-o,y=Math.sqrt(u/c)/1e3,x=5>Math.abs(v);if(n||(n=x?rT.restSpeed.granular:rT.restSpeed.default),s||(s=x?rT.restDelta.granular:rT.restDelta.default),g<1){let t=rM(y,g);r=e=>a-Math.exp(-g*y*e)*((m+g*y*v)/t*Math.sin(t*e)+v*Math.cos(t*e))}else if(1===g)r=t=>a-Math.exp(-y*t)*(v+(m+y*v)*t);else{let t=y*Math.sqrt(g*g-1);r=e=>{let r=Math.exp(-g*y*e),i=Math.min(t*e,300);return a-r*((m+g*y*v)*Math.sinh(i)+t*v*Math.cosh(i))/t}}let b={calculatedDuration:f&&d||null,next:t=>{let e=r(t);if(f)l.done=t>=d;else{let i=0===t?m:0;g<1&&(i=0===t?rs(m):rA(r,t,e));let o=Math.abs(a-e)<=s;l.done=Math.abs(i)<=n&&o}return l.value=l.done?a:e,l},toString:()=>{let t=Math.min(rk(b),2e4),e=rw(e=>b.next(t*e).value,t,30);return t+"ms "+e},toTransition:()=>{}};return b}function rV({keyframes:t,velocity:e=0,power:r=.8,timeConstant:i=325,bounceDamping:n=10,bounceStiffness:s=500,modifyTarget:o,min:a,max:l,restDelta:u=.5,restSpeed:h}){let c,d,p=t[0],f={done:!1,value:p},m=r*e,g=p+m,v=void 0===o?g:o(g);v!==g&&(m=v-p);let y=t=>-m*Math.exp(-t/i),x=t=>v+y(t),b=t=>{let e=y(t),r=x(t);f.done=Math.abs(e)<=u,f.value=f.done?v:r},w=t=>{let e;if(e=f.value,void 0!==a&&e<a||void 0!==l&&e>l){var r;c=t,d=rC({keyframes:[f.value,(r=f.value,void 0===a?l:void 0===l||Math.abs(a-r)<Math.abs(l-r)?a:l)],velocity:rA(x,t,f.value),damping:n,stiffness:s,restDelta:u,restSpeed:h})}};return w(0),{calculatedDuration:null,next:t=>{let e=!1;return(d||void 0!==c||(e=!0,b(t),w(t)),void 0!==c&&t>=c)?d.next(t-c):(e||b(t),f)}}}rC.applyToOptions=t=>{let e=function(t,e=100,r){let i=r({...t,keyframes:[0,e]}),n=Math.min(rk(i),2e4);return{type:"keyframes",ease:t=>i.next(n*t).value/e,duration:n/1e3}}(t,100,rC);return t.ease=e.ease,t.duration=rs(e.duration),t.type="keyframes",t};let rD=(t,e,r)=>(((1-3*r+3*e)*t+(3*r-6*e))*t+3*e)*t;function rO(t,e,r,i){return t===e&&r===i?tn:n=>0===n||1===n?n:rD(function(t,e,r,i,n){let s,o,a=0;do(s=rD(o=e+(r-e)/2,i,n)-t)>0?r=o:e=o;while(Math.abs(s)>1e-7&&++a<12);return o}(n,0,1,t,r),e,i)}let rR=rO(.42,0,1,1),r_=rO(0,0,.58,1),rj=rO(.42,0,.58,1),rL=t=>e=>e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2,rz=t=>e=>1-t(1-e),rF=rO(.33,1.53,.69,.99),rB=rz(rF),rI=rL(rB),rN=t=>(t*=2)<1?.5*rB(t):.5*(2-Math.pow(2,-10*(t-1))),rY=t=>1-Math.sin(Math.acos(t)),rU=rz(rY),rW=rL(rY),rX=t=>Array.isArray(t)&&"number"==typeof t[0],r$={linear:tn,easeIn:rR,easeInOut:rj,easeOut:r_,circIn:rY,circInOut:rW,circOut:rU,backIn:rB,backInOut:rI,backOut:rF,anticipate:rN},rH=t=>{if(rX(t)){Z(4===t.length,"Cubic bezier arrays must contain four numerical values.","cubic-bezier-length");let[e,r,i,n]=t;return rO(e,r,i,n)}return"string"==typeof t?(Z(void 0!==r$[t],`Invalid easing type '${t}'`,"invalid-easing-type"),r$[t]):t},rq=(t,e,r)=>{let i=e-t;return 0===i?1:(r-t)/i};function rG({duration:t=300,keyframes:e,times:r,ease:i="easeInOut"}){var n;let s=Array.isArray(i)&&"number"!=typeof i[0]?i.map(rH):rH(i),o={done:!1,value:e[0]},a=function(t,e,{clamp:r=!0,ease:i,mixer:n}={}){let s=t.length;if(Z(s===e.length,"Both input and output ranges must be the same length","range-length"),1===s)return()=>e[0];if(2===s&&e[0]===e[1])return()=>e[1];let o=t[0]===t[1];t[0]>t[s-1]&&(t=[...t].reverse(),e=[...e].reverse());let a=function(t,e,r){let i=[],n=r||ts.mix||rx,s=t.length-1;for(let r=0;r<s;r++){let s=n(t[r],t[r+1]);e&&(s=rn(Array.isArray(e)?e[r]||tn:e,s)),i.push(s)}return i}(e,i,n),l=a.length,u=r=>{if(o&&r<t[0])return e[0];let i=0;if(l>1)for(;i<t.length-2&&!(r<t[i+1]);i++);let n=rq(t[i],t[i+1],r);return a[i](n)};return r?e=>u(L(t[0],t[s-1],e)):u}((n=r&&r.length===e.length?r:function(t){let e=[0];return!function(t,e){let r=t[t.length-1];for(let i=1;i<=e;i++){let n=rq(0,e,i);t.push(A(r,1,n))}}(e,t.length-1),e}(e),n.map(e=>e*t)),e,{ease:Array.isArray(s)?s:e.map(()=>s||rj).splice(0,e.length-1)});return{calculatedDuration:t,next:e=>(o.value=a(e),o.done=e>=t,o)}}let rK=t=>null!==t;function rZ(t,{repeat:e,repeatType:r="loop"},i,n=1){let s=t.filter(rK),o=n<0||e&&"loop"!==r&&e%2==1?0:s.length-1;return o&&void 0!==i?i:s[o]}let rQ={decay:rV,inertia:rV,tween:rG,keyframes:rG,spring:rC};function rJ(t){"string"==typeof t.type&&(t.type=rQ[t.type])}class r0{constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise(t=>{this.resolve=t})}notifyFinished(){this.resolve()}then(t,e){return this.finished.then(t,e)}}let r1=t=>t/100;class r2 extends r0{constructor(t){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=()=>{let{motionValue:t}=this.options;t&&t.updatedAt!==tJ.now()&&this.tick(tJ.now()),this.isStopped=!0,"idle"!==this.state&&(this.teardown(),this.options.onStop?.())},ro.mainThread++,this.options=t,this.initAnimation(),this.play(),!1===t.autoplay&&this.pause()}initAnimation(){let{options:t}=this;rJ(t);let{type:e=rG,repeat:r=0,repeatDelay:i=0,repeatType:n,velocity:s=0}=t,{keyframes:o}=t,a=e||rG;a!==rG&&"number"!=typeof o[0]&&(this.mixKeyframes=rn(r1,rx(o[0],o[1])),o=[0,100]);let l=a({...t,keyframes:o});"mirror"===n&&(this.mirroredGenerator=a({...t,keyframes:[...o].reverse(),velocity:-s})),null===l.calculatedDuration&&(l.calculatedDuration=rk(l));let{calculatedDuration:u}=l;this.calculatedDuration=u,this.resolvedDuration=u+i,this.totalDuration=this.resolvedDuration*(r+1)-i,this.generator=l}updateTime(t){let e=Math.round(t-this.startTime)*this.playbackSpeed;null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=e}tick(t,e=!1){let{generator:r,totalDuration:i,mixKeyframes:n,mirroredGenerator:s,resolvedDuration:o,calculatedDuration:a}=this;if(null===this.startTime)return r.next(0);let{delay:l=0,keyframes:u,repeat:h,repeatType:c,repeatDelay:d,type:p,onUpdate:f,finalKeyframe:m}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,t):this.speed<0&&(this.startTime=Math.min(t-i/this.speed,this.startTime)),e?this.currentTime=t:this.updateTime(t);let g=this.currentTime-l*(this.playbackSpeed>=0?1:-1),v=this.playbackSpeed>=0?g<0:g>i;this.currentTime=Math.max(g,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=i);let y=this.currentTime,x=r;if(h){let t=Math.min(this.currentTime,i)/o,e=Math.floor(t),r=t%1;!r&&t>=1&&(r=1),1===r&&e--,(e=Math.min(e,h+1))%2&&("reverse"===c?(r=1-r,d&&(r-=d/o)):"mirror"===c&&(x=s)),y=L(0,1,r)*o}let b=v?{done:!1,value:u[0]}:x.next(y);n&&(b.value=n(b.value));let{done:w}=b;v||null===a||(w=this.playbackSpeed>=0?this.currentTime>=i:this.currentTime<=0);let k=null===this.holdTime&&("finished"===this.state||"running"===this.state&&w);return k&&p!==rV&&(b.value=rZ(u,this.options,m,this.speed)),f&&f(b.value),k&&this.finish(),b}then(t,e){return this.finished.then(t,e)}get duration(){return this.calculatedDuration/1e3}get time(){return this.currentTime/1e3}set time(t){t=rs(t),this.currentTime=t,null===this.startTime||null!==this.holdTime||0===this.playbackSpeed?this.holdTime=t:this.driver&&(this.startTime=this.driver.now()-t/this.playbackSpeed),this.driver?.start(!1)}get speed(){return this.playbackSpeed}set speed(t){this.updateTime(tJ.now());let e=this.playbackSpeed!==t;this.playbackSpeed=t,e&&(this.time=this.currentTime/1e3)}play(){if(this.isStopped)return;let{driver:t=rb,startTime:e}=this.options;this.driver||(this.driver=t(t=>this.tick(t))),this.options.onPlay?.();let r=this.driver.now();"finished"===this.state?(this.updateFinished(),this.startTime=r):null!==this.holdTime?this.startTime=r-this.holdTime:this.startTime||(this.startTime=e??r),"finished"===this.state&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(tJ.now()),this.holdTime=this.currentTime}complete(){"running"!==this.state&&this.play(),this.state="finished",this.holdTime=null}finish(){this.notifyFinished(),this.teardown(),this.state="finished",this.options.onComplete?.()}cancel(){this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),this.options.onCancel?.()}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null,ro.mainThread--}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(t){return this.startTime=0,this.tick(t,!0)}attachTimeline(t){return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),this.driver?.stop(),t.observe(this)}}function r5(t){let e;return()=>(void 0===e&&(e=t()),e)}let r3=r5(()=>void 0!==window.ScrollTimeline),r4={},r6=function(t,e){let r=r5(t);return()=>r4[e]??r()}(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(t){return!1}return!0},"linearEasing"),r9=([t,e,r,i])=>`cubic-bezier(${t}, ${e}, ${r}, ${i})`,r8={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:r9([0,.65,.55,1]),circOut:r9([.55,0,1,.45]),backIn:r9([.31,.01,.66,-.59]),backOut:r9([.33,1.53,.69,.99])};function r7(t){return"function"==typeof t&&"applyToOptions"in t}class it extends r0{constructor(t){if(super(),this.finishedTime=null,this.isStopped=!1,!t)return;let{element:e,name:r,keyframes:i,pseudoElement:n,allowFlatten:s=!1,finalKeyframe:o,onComplete:a}=t;this.isPseudoElement=!!n,this.allowFlatten=s,this.options=t,Z("string"!=typeof t.type,'Mini animate() doesn\'t support "type" as a string.',"mini-spring");let l=function({type:t,...e}){return r7(t)&&r6()?t.applyToOptions(e):(e.duration??(e.duration=300),e.ease??(e.ease="easeOut"),e)}(t);this.animation=function(t,e,r,{delay:i=0,duration:n=300,repeat:s=0,repeatType:o="loop",ease:a="easeOut",times:l}={},u){let h={[e]:r};l&&(h.offset=l);let c=function t(e,r){if(e)return"function"==typeof e?r6()?rw(e,r):"ease-out":rX(e)?r9(e):Array.isArray(e)?e.map(e=>t(e,r)||r8.easeOut):r8[e]}(a,n);Array.isArray(c)&&(h.easing=c),ta.value&&ro.waapi++;let d={delay:i,duration:n,easing:Array.isArray(c)?"linear":c,fill:"both",iterations:s+1,direction:"reverse"===o?"alternate":"normal"};u&&(d.pseudoElement=u);let p=t.animate(h,d);return ta.value&&p.finished.finally(()=>{ro.waapi--}),p}(e,r,i,l,n),!1===l.autoplay&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!n){let t=rZ(i,this.options,o,this.speed);this.updateMotionValue?this.updateMotionValue(t):function(t,e,r){e.startsWith("--")?t.style.setProperty(e,r):t.style[e]=r}(e,r,t),this.animation.cancel()}a?.(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),"finished"===this.state&&this.updateFinished())}pause(){this.animation.pause()}complete(){this.animation.finish?.()}cancel(){try{this.animation.cancel()}catch(t){}}stop(){if(this.isStopped)return;this.isStopped=!0;let{state:t}=this;"idle"!==t&&"finished"!==t&&(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){this.isPseudoElement||this.animation.commitStyles?.()}get duration(){return Number(this.animation.effect?.getComputedTiming?.().duration||0)/1e3}get time(){return(Number(this.animation.currentTime)||0)/1e3}set time(t){this.finishedTime=null,this.animation.currentTime=rs(t)}get speed(){return this.animation.playbackRate}set speed(t){t<0&&(this.finishedTime=null),this.animation.playbackRate=t}get state(){return null!==this.finishedTime?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(t){this.animation.startTime=t}attachTimeline({timeline:t,observe:e}){return(this.allowFlatten&&this.animation.effect?.updateTiming({easing:"linear"}),this.animation.onfinish=null,t&&r3())?(this.animation.timeline=t,tn):e(this)}}let ie={anticipate:rN,backInOut:rI,circInOut:rW};class ir extends it{constructor(t){!function(t){"string"==typeof t.ease&&t.ease in ie&&(t.ease=ie[t.ease])}(t),rJ(t),super(t),t.startTime&&(this.startTime=t.startTime),this.options=t}updateMotionValue(t){let{motionValue:e,onUpdate:r,onComplete:i,element:n,...s}=this.options;if(!e)return;if(void 0!==t)return void e.set(t);let o=new r2({...s,autoplay:!1}),a=rs(this.finishedTime??this.time);e.setWithVelocity(o.sample(a-10).value,o.sample(a).value,10),o.stop()}}let ii=(t,e)=>"zIndex"!==e&&!!("number"==typeof t||Array.isArray(t)||"string"==typeof t&&(tB.test(t)||"0"===t)&&!t.startsWith("url(")),is=new Set(["opacity","clipPath","filter","transform"]),io=r5(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));class ia extends r0{constructor({autoplay:t=!0,delay:e=0,type:r="keyframes",repeat:i=0,repeatDelay:n=0,repeatType:s="loop",keyframes:o,name:a,motionValue:l,element:u,...h}){super(),this.stop=()=>{this._animation&&(this._animation.stop(),this.stopTimeline?.()),this.keyframeResolver?.cancel()},this.createdAt=tJ.now();let c={autoplay:t,delay:e,type:r,repeat:i,repeatDelay:n,repeatType:s,name:a,motionValue:l,element:u,...h},d=u?.KeyframeResolver||tx;this.keyframeResolver=new d(o,(t,e,r)=>this.onKeyframesResolved(t,e,c,!r),a,l,u),this.keyframeResolver?.scheduleResolve()}onKeyframesResolved(t,e,r,i){this.keyframeResolver=void 0;let{name:n,type:s,velocity:o,delay:a,isHandoff:l,onUpdate:u}=r;this.resolvedAt=tJ.now(),!function(t,e,r,i){let n=t[0];if(null===n)return!1;if("display"===e||"visibility"===e)return!0;let s=t[t.length-1],o=ii(n,e),a=ii(s,e);return K(o===a,`You are trying to animate ${e} from "${n}" to "${s}". "${o?s:n}" is not an animatable value.`,"value-not-animatable"),!!o&&!!a&&(function(t){let e=t[0];if(1===t.length)return!0;for(let r=0;r<t.length;r++)if(t[r]!==e)return!0}(t)||("spring"===r||r7(r))&&i)}(t,n,s,o)&&((ts.instantAnimations||!a)&&u?.(rZ(t,r,e)),t[0]=t[t.length-1],rr(r),r.repeat=0);let h={startTime:i?this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt:void 0,finalKeyframe:e,...r,keyframes:t},c=!l&&function(t){let{motionValue:e,name:r,repeatDelay:i,repeatType:n,damping:s,type:o}=t;if(!(e?.owner?.current instanceof HTMLElement))return!1;let{onUpdate:a,transformTemplate:l}=e.owner.getProps();return io()&&r&&is.has(r)&&("transform"!==r||!l)&&!a&&!i&&"mirror"!==n&&0!==s&&"inertia"!==o}(h)?new ir({...h,element:h.motionValue.owner.current}):new r2(h);c.finished.then(()=>this.notifyFinished()).catch(tn),this.pendingTimeline&&(this.stopTimeline=c.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=c}get finished(){return this._animation?this.animation.finished:this._finished}then(t,e){return this.finished.finally(t).then(()=>{})}get animation(){return this._animation||(this.keyframeResolver?.resume(),tg=!0,ty(),tv(),tg=!1),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(t){this.animation.time=t}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(t){this.animation.speed=t}get startTime(){return this.animation.startTime}attachTimeline(t){return this._animation?this.stopTimeline=this.animation.attachTimeline(t):this.pendingTimeline=t,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){this._animation&&this.animation.cancel(),this.keyframeResolver?.cancel()}}let il=t=>null!==t,iu={type:"spring",stiffness:500,damping:25,restSpeed:10},ih={type:"keyframes",duration:.8},ic={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},id=(t,e,r,i={},n,s)=>a=>{let l=e7(i,t)||{},u=l.delay||i.delay||0,{elapsed:h=0}=i;h-=rs(u);let c={keyframes:Array.isArray(r)?r:[null,r],ease:"easeOut",velocity:e.getVelocity(),...l,delay:-h,onUpdate:t=>{e.set(t),l.onUpdate&&l.onUpdate(t)},onComplete:()=>{a(),l.onComplete&&l.onComplete()},name:t,motionValue:e,element:s?void 0:n};!function({when:t,delay:e,delayChildren:r,staggerChildren:i,staggerDirection:n,repeat:s,repeatType:o,repeatDelay:a,from:l,elapsed:u,...h}){return!!Object.keys(h).length}(l)&&Object.assign(c,((t,{keyframes:e})=>e.length>2?ih:o.has(t)?t.startsWith("scale")?{type:"spring",stiffness:550,damping:0===e[1]?2*Math.sqrt(550):30,restSpeed:10}:iu:ic)(t,c)),c.duration&&(c.duration=rs(c.duration)),c.repeatDelay&&(c.repeatDelay=rs(c.repeatDelay)),void 0!==c.from&&(c.keyframes[0]=c.from);let d=!1;if(!1!==c.type&&(0!==c.duration||c.repeatDelay)||(rr(c),0===c.delay&&(d=!0)),(ts.instantAnimations||ts.skipAnimations)&&(d=!0,rr(c),c.delay=0),c.allowFlatten=!l.type&&!l.ease,d&&!s&&void 0!==e.get()){let t=function(t,{repeat:e,repeatType:r="loop"},i){let n=t.filter(il),s=e&&"loop"!==r&&e%2==1?0:n.length-1;return n[s]}(c.keyframes,l);if(void 0!==t)return void tu.update(()=>{c.onUpdate(t),c.onComplete()})}return l.isSync?new r2(c):new ia(c)};function ip(t,e,{delay:r=0,transitionOverride:i,type:n}={}){let{transition:s=t.getDefaultTransition(),transitionEnd:o,...a}=e;i&&(s=i);let l=[],u=n&&t.animationState&&t.animationState.getState()[n];for(let e in a){let i=t.getValue(e,t.latestValues[e]??null),n=a[e];if(void 0===n||u&&function({protectedKeys:t,needsAnimating:e},r){let i=t.hasOwnProperty(r)&&!0!==e[r];return e[r]=!1,i}(u,e))continue;let o={delay:r,...e7(s||{},e)},h=i.get();if(void 0!==h&&!i.isAnimating&&!Array.isArray(n)&&n===h&&!o.velocity)continue;let c=!1;if(window.MotionHandoffAnimation){let r=t.props[e3];if(r){let t=window.MotionHandoffAnimation(r,e,tu);null!==t&&(o.startTime=t,c=!0)}}re(t,e),i.start(id(e,i,n,t.shouldReduceMotion&&j.has(e)?{type:!1}:o,t,c));let d=i.animation;d&&l.push(d)}return o&&Promise.all(l).then(()=>{tu.update(()=>{o&&function(t,e){let{transitionEnd:r={},transition:i={},...n}=e8(t,e)||{};for(let e in n={...n,...r}){var s;let r=rt(s=n[e])?s[s.length-1]||0:s;t.hasValue(e)?t.getValue(e).set(r):t.addValue(e,t4(r))}}(t,o)})}),l}function im(t,e,r,i=0,n=1){let s=Array.from(t).sort((t,e)=>t.sortNodePosition(e)).indexOf(e),o=t.size,a=(o-1)*i;return"function"==typeof r?r(s,o):1===n?s*i:a-s*i}function ig(t,e,r={}){let i=e8(t,e,"exit"===r.type?t.presenceContext?.custom:void 0),{transition:n=t.getDefaultTransition()||{}}=i||{};r.transitionOverride&&(n=r.transitionOverride);let s=i?()=>Promise.all(ip(t,i,r)):()=>Promise.resolve(),o=t.variantChildren&&t.variantChildren.size?(i=0)=>{let{delayChildren:s=0,staggerChildren:o,staggerDirection:a}=n;return function(t,e,r=0,i=0,n=0,s=1,o){let a=[];for(let l of t.variantChildren)l.notify("AnimationStart",e),a.push(ig(l,e,{...o,delay:r+("function"==typeof i?0:i)+im(t.variantChildren,l,i,n,s)}).then(()=>l.notify("AnimationComplete",e)));return Promise.all(a)}(t,e,i,s,o,a,r)}:()=>Promise.resolve(),{when:a}=n;if(!a)return Promise.all([s(),o(r.delay)]);{let[t,e]="beforeChildren"===a?[s,o]:[o,s];return t().then(()=>e())}}function iv(t,e){if(!Array.isArray(e))return!1;let r=e.length;if(r!==t.length)return!1;for(let i=0;i<r;i++)if(e[i]!==t[i])return!1;return!0}let iy=ec.length,ix=[...eh].reverse(),ib=eh.length;function iw(t=!1){return{isActive:t,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function ik(){return{animate:iw(!0),whileInView:iw(),whileHover:iw(),whileTap:iw(),whileDrag:iw(),whileFocus:iw(),exit:iw()}}class iA{constructor(t){this.isMounted=!1,this.node=t}update(){}}class iT extends iA{constructor(t){super(t),t.animationState||(t.animationState=function(t){let e=e=>Promise.all(e.map(({animation:e,options:r})=>(function(t,e,r={}){let i;if(t.notify("AnimationStart",e),Array.isArray(e))i=Promise.all(e.map(e=>ig(t,e,r)));else if("string"==typeof e)i=ig(t,e,r);else{let n="function"==typeof e?e8(t,e,r.custom):e;i=Promise.all(ip(t,n,r))}return i.then(()=>{t.notify("AnimationComplete",e)})})(t,e,r))),r=ik(),i=!0,n=e=>(r,i)=>{let n=e8(t,i,"exit"===e?t.presenceContext?.custom:void 0);if(n){let{transition:t,transitionEnd:e,...i}=n;r={...r,...i,...e}}return r};function s(s){let{props:o}=t,a=function t(e){if(!e)return;if(!e.isControllingVariants){let r=e.parent&&t(e.parent)||{};return void 0!==e.props.initial&&(r.initial=e.props.initial),r}let r={};for(let t=0;t<iy;t++){let i=ec[t],n=e.props[i];(eu(n)||!1===n)&&(r[i]=n)}return r}(t.parent)||{},l=[],u=new Set,h={},c=1/0;for(let e=0;e<ib;e++){var d,p;let f=ix[e],m=r[f],g=void 0!==o[f]?o[f]:a[f],v=eu(g),y=f===s?m.isActive:null;!1===y&&(c=e);let x=g===a[f]&&g!==o[f]&&v;if(x&&i&&t.manuallyAnimateOnMount&&(x=!1),m.protectedKeys={...h},!m.isActive&&null===y||!g&&!m.prevProp||el(g)||"boolean"==typeof g)continue;let b=(d=m.prevProp,"string"==typeof(p=g)?p!==d:!!Array.isArray(p)&&!iv(p,d)),w=b||f===s&&m.isActive&&!x&&v||e>c&&v,k=!1,A=Array.isArray(g)?g:[g],T=A.reduce(n(f),{});!1===y&&(T={});let{prevResolvedValues:M={}}=m,P={...M,...T},S=e=>{w=!0,u.has(e)&&(k=!0,u.delete(e)),m.needsAnimating[e]=!0;let r=t.getValue(e);r&&(r.liveStyle=!1)};for(let t in P){let e=T[t],r=M[t];if(!h.hasOwnProperty(t))(rt(e)&&rt(r)?iv(e,r):e===r)?void 0!==e&&u.has(t)?S(t):m.protectedKeys[t]=!0:null!=e?S(t):u.add(t)}m.prevProp=g,m.prevResolvedValues=T,m.isActive&&(h={...h,...T}),i&&t.blockInitialAnimation&&(w=!1);let E=x&&b,C=!E||k;w&&C&&l.push(...A.map(e=>{let r={type:f};if("string"==typeof e&&i&&!E&&t.manuallyAnimateOnMount&&t.parent){let{parent:i}=t,n=e8(i,e);if(i.enteringChildren&&n){let{delayChildren:e}=n.transition||{};r.delay=im(i.enteringChildren,t,e)}}return{animation:e,options:r}}))}if(u.size){let e={};if("boolean"!=typeof o.initial){let r=e8(t,Array.isArray(o.initial)?o.initial[0]:o.initial);r&&r.transition&&(e.transition=r.transition)}u.forEach(r=>{let i=t.getBaseTarget(r),n=t.getValue(r);n&&(n.liveStyle=!0),e[r]=i??null}),l.push({animation:e})}let f=!!l.length;return i&&(!1===o.initial||o.initial===o.animate)&&!t.manuallyAnimateOnMount&&(f=!1),i=!1,f?e(l):Promise.resolve()}return{animateChanges:s,setActive:function(e,i){if(r[e].isActive===i)return Promise.resolve();t.variantChildren?.forEach(t=>t.animationState?.setActive(e,i)),r[e].isActive=i;let n=s(e);for(let t in r)r[t].protectedKeys={};return n},setAnimateFunction:function(r){e=r(t)},getState:()=>r,reset:()=>{r=ik(),i=!0}}}(t))}updateAnimationControlsSubscription(){let{animate:t}=this.node.getProps();el(t)&&(this.unmountControls=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:t}=this.node.getProps(),{animate:e}=this.node.prevProps||{};t!==e&&this.updateAnimationControlsSubscription()}unmount(){this.node.animationState.reset(),this.unmountControls?.()}}let iM=0;class iP extends iA{constructor(){super(...arguments),this.id=iM++}update(){if(!this.node.presenceContext)return;let{isPresent:t,onExitComplete:e}=this.node.presenceContext,{isPresent:r}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===r)return;let i=this.node.animationState.setActive("exit",!t);e&&!t&&i.then(()=>{e(this.id)})}mount(){let{register:t,onExitComplete:e}=this.node.presenceContext||{};e&&e(this.id),t&&(this.unmount=t(this.id))}unmount(){}}let iS={x:!1,y:!1};function iE(t,e,r,i={passive:!0}){return t.addEventListener(e,r,i),()=>t.removeEventListener(e,r)}let iC=t=>"mouse"===t.pointerType?"number"!=typeof t.button||t.button<=0:!1!==t.isPrimary;function iV(t){return{point:{x:t.pageX,y:t.pageY}}}function iD(t,e,r,i){return iE(t,e,t=>iC(t)&&r(t,iV(t)),i)}function iO(t){return t.max-t.min}function iR(t,e,r,i=.5){t.origin=i,t.originPoint=A(e.min,e.max,t.origin),t.scale=iO(r)/iO(e),t.translate=A(r.min,r.max,t.origin)-t.originPoint,(t.scale>=.9999&&t.scale<=1.0001||isNaN(t.scale))&&(t.scale=1),(t.translate>=-.01&&t.translate<=.01||isNaN(t.translate))&&(t.translate=0)}function i_(t,e,r,i){iR(t.x,e.x,r.x,i?i.originX:void 0),iR(t.y,e.y,r.y,i?i.originY:void 0)}function ij(t,e,r){t.min=r.min+e.min,t.max=t.min+iO(e)}function iL(t,e,r){t.min=e.min-r.min,t.max=t.min+iO(e)}function iz(t,e,r){iL(t.x,e.x,r.x),iL(t.y,e.y,r.y)}function iF(t){return[t("x"),t("y")]}let iB=({current:t})=>t?t.ownerDocument.defaultView:null,iI=(t,e)=>Math.abs(t-e);class iN{constructor(t,e,{transformPagePoint:r,contextWindow:i=window,dragSnapToOrigin:n=!1,distanceThreshold:s=3}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let t=iW(this.lastMoveEventInfo,this.history),e=null!==this.startEvent,r=function(t,e){return Math.sqrt(iI(t.x,e.x)**2+iI(t.y,e.y)**2)}(t.offset,{x:0,y:0})>=this.distanceThreshold;if(!e&&!r)return;let{point:i}=t,{timestamp:n}=tc;this.history.push({...i,timestamp:n});let{onStart:s,onMove:o}=this.handlers;e||(s&&s(this.lastMoveEvent,t),this.startEvent=this.lastMoveEvent),o&&o(this.lastMoveEvent,t)},this.handlePointerMove=(t,e)=>{this.lastMoveEvent=t,this.lastMoveEventInfo=iY(e,this.transformPagePoint),tu.update(this.updatePoint,!0)},this.handlePointerUp=(t,e)=>{this.end();let{onEnd:r,onSessionEnd:i,resumeAnimation:n}=this.handlers;if(this.dragSnapToOrigin&&n&&n(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let s=iW("pointercancel"===t.type?this.lastMoveEventInfo:iY(e,this.transformPagePoint),this.history);this.startEvent&&r&&r(t,s),i&&i(t,s)},!iC(t))return;this.dragSnapToOrigin=n,this.handlers=e,this.transformPagePoint=r,this.distanceThreshold=s,this.contextWindow=i||window;let o=iY(iV(t),this.transformPagePoint),{point:a}=o,{timestamp:l}=tc;this.history=[{...a,timestamp:l}];let{onSessionStart:u}=e;u&&u(t,iW(o,this.history)),this.removeListeners=rn(iD(this.contextWindow,"pointermove",this.handlePointerMove),iD(this.contextWindow,"pointerup",this.handlePointerUp),iD(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),th(this.updatePoint)}}function iY(t,e){return e?{point:e(t.point)}:t}function iU(t,e){return{x:t.x-e.x,y:t.y-e.y}}function iW({point:t},e){return{point:t,delta:iU(t,iX(e)),offset:iU(t,e[0]),velocity:function(t,e){if(t.length<2)return{x:0,y:0};let r=t.length-1,i=null,n=iX(t);for(;r>=0&&(i=t[r],!(n.timestamp-i.timestamp>rs(.1)));)r--;if(!i)return{x:0,y:0};let s=(n.timestamp-i.timestamp)/1e3;if(0===s)return{x:0,y:0};let o={x:(n.x-i.x)/s,y:(n.y-i.y)/s};return o.x===1/0&&(o.x=0),o.y===1/0&&(o.y=0),o}(e,.1)}}function iX(t){return t[t.length-1]}function i$(t,e,r){return{min:void 0!==e?t.min+e:void 0,max:void 0!==r?t.max+r-(t.max-t.min):void 0}}function iH(t,e){let r=e.min-t.min,i=e.max-t.max;return e.max-e.min<t.max-t.min&&([r,i]=[i,r]),{min:r,max:i}}function iq(t,e,r){return{min:iG(t,e),max:iG(t,r)}}function iG(t,e){return"number"==typeof t?t:t[e]||0}let iK=new WeakMap;class iZ{constructor(t){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=ei(),this.latestPointerEvent=null,this.latestPanInfo=null,this.visualElement=t}start(t,{snapToCursor:e=!1,distanceThreshold:r}={}){let{presenceContext:i}=this.visualElement;if(i&&!1===i.isPresent)return;let n=t=>{let{dragSnapToOrigin:r}=this.getProps();r?this.pauseAnimation():this.stopAnimation(),e&&this.snapToCursor(iV(t).point)},s=(t,e)=>{let{drag:r,dragPropagation:i,onDragStart:n}=this.getProps();if(r&&!i&&(this.openDragLock&&this.openDragLock(),this.openDragLock=function(t){if("x"===t||"y"===t)if(iS[t])return null;else return iS[t]=!0,()=>{iS[t]=!1};return iS.x||iS.y?null:(iS.x=iS.y=!0,()=>{iS.x=iS.y=!1})}(r),!this.openDragLock))return;this.latestPointerEvent=t,this.latestPanInfo=e,this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),iF(t=>{let e=this.getAxisMotionValue(t).get()||0;if(Y.test(e)){let{projection:r}=this.visualElement;if(r&&r.layout){let i=r.layout.layoutBox[t];i&&(e=iO(i)*(parseFloat(e)/100))}}this.originPoint[t]=e}),n&&tu.postRender(()=>n(t,e)),re(this.visualElement,"transform");let{animationState:s}=this.visualElement;s&&s.setActive("whileDrag",!0)},o=(t,e)=>{this.latestPointerEvent=t,this.latestPanInfo=e;let{dragPropagation:r,dragDirectionLock:i,onDirectionLock:n,onDrag:s}=this.getProps();if(!r&&!this.openDragLock)return;let{offset:o}=e;if(i&&null===this.currentDirection){this.currentDirection=function(t,e=10){let r=null;return Math.abs(t.y)>e?r="y":Math.abs(t.x)>e&&(r="x"),r}(o),null!==this.currentDirection&&n&&n(this.currentDirection);return}this.updateAxis("x",e.point,o),this.updateAxis("y",e.point,o),this.visualElement.render(),s&&s(t,e)},a=(t,e)=>{this.latestPointerEvent=t,this.latestPanInfo=e,this.stop(t,e),this.latestPointerEvent=null,this.latestPanInfo=null},l=()=>iF(t=>"paused"===this.getAnimationState(t)&&this.getAxisMotionValue(t).animation?.play()),{dragSnapToOrigin:u}=this.getProps();this.panSession=new iN(t,{onSessionStart:n,onStart:s,onMove:o,onSessionEnd:a,resumeAnimation:l},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:u,distanceThreshold:r,contextWindow:iB(this.visualElement)})}stop(t,e){let r=t||this.latestPointerEvent,i=e||this.latestPanInfo,n=this.isDragging;if(this.cancel(),!n||!i||!r)return;let{velocity:s}=i;this.startAnimation(s);let{onDragEnd:o}=this.getProps();o&&tu.postRender(()=>o(r,i))}cancel(){this.isDragging=!1;let{projection:t,animationState:e}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:r}=this.getProps();!r&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),e&&e.setActive("whileDrag",!1)}updateAxis(t,e,r){let{drag:i}=this.getProps();if(!r||!iQ(t,i,this.currentDirection))return;let n=this.getAxisMotionValue(t),s=this.originPoint[t]+r[t];this.constraints&&this.constraints[t]&&(s=function(t,{min:e,max:r},i){return void 0!==e&&t<e?t=i?A(e,t,i.min):Math.max(t,e):void 0!==r&&t>r&&(t=i?A(r,t,i.max):Math.min(t,r)),t}(s,this.constraints[t],this.elastic[t])),n.set(s)}resolveConstraints(){let{dragConstraints:t,dragElastic:e}=this.getProps(),r=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):this.visualElement.projection?.layout,i=this.constraints;t&&e5(t)?this.constraints||(this.constraints=this.resolveRefConstraints()):t&&r?this.constraints=function(t,{top:e,left:r,bottom:i,right:n}){return{x:i$(t.x,r,n),y:i$(t.y,e,i)}}(r.layoutBox,t):this.constraints=!1,this.elastic=function(t=.35){return!1===t?t=0:!0===t&&(t=.35),{x:iq(t,"left","right"),y:iq(t,"top","bottom")}}(e),i!==this.constraints&&r&&this.constraints&&!this.hasMutatedConstraints&&iF(t=>{!1!==this.constraints&&this.getAxisMotionValue(t)&&(this.constraints[t]=function(t,e){let r={};return void 0!==e.min&&(r.min=e.min-t.min),void 0!==e.max&&(r.max=e.max-t.min),r}(r.layoutBox[t],this.constraints[t]))})}resolveRefConstraints(){var t;let{dragConstraints:e,onMeasureDragConstraints:r}=this.getProps();if(!e||!e5(e))return!1;let i=e.current;Z(null!==i,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.","drag-constraints-ref");let{projection:n}=this.visualElement;if(!n||!n.layout)return!1;let s=function(t,e,r){let i=_(t,r),{scroll:n}=e;return n&&(D(i.x,n.offset.x),D(i.y,n.offset.y)),i}(i,n.root,this.visualElement.getTransformPagePoint()),o=(t=n.layout.layoutBox,{x:iH(t.x,s.x),y:iH(t.y,s.y)});if(r){let t=r(function({x:t,y:e}){return{top:e.min,right:t.max,bottom:e.max,left:t.min}}(o));this.hasMutatedConstraints=!!t,t&&(o=k(t))}return o}startAnimation(t){let{drag:e,dragMomentum:r,dragElastic:i,dragTransition:n,dragSnapToOrigin:s,onDragTransitionEnd:o}=this.getProps(),a=this.constraints||{};return Promise.all(iF(o=>{if(!iQ(o,e,this.currentDirection))return;let l=a&&a[o]||{};s&&(l={min:0,max:0});let u={type:"inertia",velocity:r?t[o]:0,bounceStiffness:i?200:1e6,bounceDamping:i?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...n,...l};return this.startAxisValueAnimation(o,u)})).then(o)}startAxisValueAnimation(t,e){let r=this.getAxisMotionValue(t);return re(this.visualElement,t),r.start(id(t,r,0,e,this.visualElement,!1))}stopAnimation(){iF(t=>this.getAxisMotionValue(t).stop())}pauseAnimation(){iF(t=>this.getAxisMotionValue(t).animation?.pause())}getAnimationState(t){return this.getAxisMotionValue(t).animation?.state}getAxisMotionValue(t){let e=`_drag${t.toUpperCase()}`,r=this.visualElement.getProps();return r[e]||this.visualElement.getValue(t,(r.initial?r.initial[t]:void 0)||0)}snapToCursor(t){iF(e=>{let{drag:r}=this.getProps();if(!iQ(e,r,this.currentDirection))return;let{projection:i}=this.visualElement,n=this.getAxisMotionValue(e);if(i&&i.layout){let{min:r,max:s}=i.layout.layoutBox[e];n.set(t[e]-A(r,s,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:t,dragConstraints:e}=this.getProps(),{projection:r}=this.visualElement;if(!e5(e)||!r||!this.constraints)return;this.stopAnimation();let i={x:0,y:0};iF(t=>{let e=this.getAxisMotionValue(t);if(e&&!1!==this.constraints){let r=e.get();i[t]=function(t,e){let r=.5,i=iO(t),n=iO(e);return n>i?r=rq(e.min,e.max-i,t.min):i>n&&(r=rq(t.min,t.max-n,e.min)),L(0,1,r)}({min:r,max:r},this.constraints[t])}});let{transformTemplate:n}=this.visualElement.getProps();this.visualElement.current.style.transform=n?n({},""):"none",r.root&&r.root.updateScroll(),r.updateLayout(),this.resolveConstraints(),iF(e=>{if(!iQ(e,t,null))return;let r=this.getAxisMotionValue(e),{min:n,max:s}=this.constraints[e];r.set(A(n,s,i[e]))})}addListeners(){if(!this.visualElement.current)return;iK.set(this.visualElement,this);let t=iD(this.visualElement.current,"pointerdown",t=>{let{drag:e,dragListener:r=!0}=this.getProps();e&&r&&this.start(t)}),e=()=>{let{dragConstraints:t}=this.getProps();e5(t)&&t.current&&(this.constraints=this.resolveRefConstraints())},{projection:r}=this.visualElement,i=r.addEventListener("measure",e);r&&!r.layout&&(r.root&&r.root.updateScroll(),r.updateLayout()),tu.read(e);let n=iE(window,"resize",()=>this.scalePositionWithinConstraints()),s=r.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e})=>{this.isDragging&&e&&(iF(e=>{let r=this.getAxisMotionValue(e);r&&(this.originPoint[e]+=t[e].translate,r.set(r.get()+t[e].translate))}),this.visualElement.render())});return()=>{n(),t(),i(),s&&s()}}getProps(){let t=this.visualElement.getProps(),{drag:e=!1,dragDirectionLock:r=!1,dragPropagation:i=!1,dragConstraints:n=!1,dragElastic:s=.35,dragMomentum:o=!0}=t;return{...t,drag:e,dragDirectionLock:r,dragPropagation:i,dragConstraints:n,dragElastic:s,dragMomentum:o}}}function iQ(t,e,r){return(!0===e||e===t)&&(null===r||r===t)}class iJ extends iA{constructor(t){super(t),this.removeGroupControls=tn,this.removeListeners=tn,this.controls=new iZ(t)}mount(){let{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||tn}unmount(){this.removeGroupControls(),this.removeListeners()}}let i0=t=>(e,r)=>{t&&tu.postRender(()=>t(e,r))};class i1 extends iA{constructor(){super(...arguments),this.removePointerDownListener=tn}onPointerDown(t){this.session=new iN(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:iB(this.node)})}createPanHandlers(){let{onPanSessionStart:t,onPanStart:e,onPan:r,onPanEnd:i}=this.node.getProps();return{onSessionStart:i0(t),onStart:i0(e),onMove:r,onEnd:(t,e)=>{delete this.session,i&&tu.postRender(()=>i(t,e))}}}mount(){this.removePointerDownListener=iD(this.node.current,"pointerdown",t=>this.onPointerDown(t))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}var i2=r(2082);let i5={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function i3(t,e){return e.max===e.min?0:t/(e.max-e.min)*100}let i4={correct:(t,e)=>{if(!e.target)return t;if("string"==typeof t)if(!U.test(t))return t;else t=parseFloat(t);let r=i3(t,e.target.x),i=i3(t,e.target.y);return`${r}% ${i}%`}},i6=!1;class i9 extends n.Component{componentDidMount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:r,layoutId:i}=this.props,{projection:n}=t;for(let t in i7)eT[t]=i7[t],y(t)&&(eT[t].isCSSVariable=!0);n&&(e.group&&e.group.add(n),r&&r.register&&i&&r.register(n),i6&&n.root.didUpdate(),n.addEventListener("animationComplete",()=>{this.safeToRemove()}),n.setOptions({...n.options,onExitComplete:()=>this.safeToRemove()})),i5.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){let{layoutDependency:e,visualElement:r,drag:i,isPresent:n}=this.props,{projection:s}=r;return s&&(s.isPresent=n,i6=!0,i||t.layoutDependency!==e||void 0===e||t.isPresent!==n?s.willUpdate():this.safeToRemove(),t.isPresent!==n&&(n?s.promote():s.relegate()||tu.postRender(()=>{let t=s.getStack();t&&t.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),t9.postRender(()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:r}=this.props,{projection:i}=t;i6=!0,i&&(i.scheduleCheckAfterUnmount(),e&&e.group&&e.group.remove(i),r&&r.deregister&&r.deregister(i))}safeToRemove(){let{safeToRemove:t}=this.props;t&&t()}render(){return null}}function i8(t){let[e,r]=(0,i2.xQ)(),i=(0,n.useContext)(eB.L);return(0,eF.jsx)(i9,{...t,layoutGroup:i,switchLayoutGroup:(0,n.useContext)(e4),isPresent:e,safeToRemove:r})}let i7={borderRadius:{...i4,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:i4,borderTopRightRadius:i4,borderBottomLeftRadius:i4,borderBottomRightRadius:i4,boxShadow:{correct:(t,{treeScale:e,projectionDelta:r})=>{let i=tB.parse(t);if(i.length>5)return t;let n=tB.createTransformer(t),s=+("number"!=typeof i[0]),o=r.x.scale*e.x,a=r.y.scale*e.y;i[0+s]/=o,i[1+s]/=a;let l=A(o,a,.5);return"number"==typeof i[2+s]&&(i[2+s]/=l),"number"==typeof i[3+s]&&(i[3+s]/=l),n(i)}}};var nt=r(6983);function ne(t){return(0,nt.G)(t)&&"ownerSVGElement"in t}let nr=(t,e)=>t.depth-e.depth;class ni{constructor(){this.children=[],this.isDirty=!1}add(t){t0(this.children,t),this.isDirty=!0}remove(t){t1(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(nr),this.isDirty=!1,this.children.forEach(t)}}let nn=["TopLeft","TopRight","BottomLeft","BottomRight"],ns=nn.length,no=t=>"string"==typeof t?parseFloat(t):t,na=t=>"number"==typeof t||U.test(t);function nl(t,e){return void 0!==t[e]?t[e]:t.borderRadius}let nu=nc(0,.5,rU),nh=nc(.5,.95,tn);function nc(t,e,r){return i=>i<t?0:i>e?1:r(rq(t,e,i))}function nd(t,e){t.min=e.min,t.max=e.max}function np(t,e){nd(t.x,e.x),nd(t.y,e.y)}function nf(t,e){t.translate=e.translate,t.scale=e.scale,t.originPoint=e.originPoint,t.origin=e.origin}function nm(t,e,r,i,n){return t-=e,t=i+1/r*(t-i),void 0!==n&&(t=i+1/n*(t-i)),t}function ng(t,e,[r,i,n],s,o){!function(t,e=0,r=1,i=.5,n,s=t,o=t){if(Y.test(e)&&(e=parseFloat(e),e=A(o.min,o.max,e/100)-o.min),"number"!=typeof e)return;let a=A(s.min,s.max,i);t===s&&(a-=e),t.min=nm(t.min,e,r,a,n),t.max=nm(t.max,e,r,a,n)}(t,e[r],e[i],e[n],e.scale,s,o)}let nv=["x","scaleX","originX"],ny=["y","scaleY","originY"];function nx(t,e,r,i){ng(t.x,e,nv,r?r.x:void 0,i?i.x:void 0),ng(t.y,e,ny,r?r.y:void 0,i?i.y:void 0)}function nb(t){return 0===t.translate&&1===t.scale}function nw(t){return nb(t.x)&&nb(t.y)}function nk(t,e){return t.min===e.min&&t.max===e.max}function nA(t,e){return Math.round(t.min)===Math.round(e.min)&&Math.round(t.max)===Math.round(e.max)}function nT(t,e){return nA(t.x,e.x)&&nA(t.y,e.y)}function nM(t){return iO(t.x)/iO(t.y)}function nP(t,e){return t.translate===e.translate&&t.scale===e.scale&&t.originPoint===e.originPoint}class nS{constructor(){this.members=[]}add(t){t0(this.members,t),t.scheduleRender()}remove(t){if(t1(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){let t=this.members[this.members.length-1];t&&this.promote(t)}}relegate(t){let e,r=this.members.findIndex(e=>t===e);if(0===r)return!1;for(let t=r;t>=0;t--){let r=this.members[t];if(!1!==r.isPresent){e=r;break}}return!!e&&(this.promote(e),!0)}promote(t,e){let r=this.lead;if(t!==r&&(this.prevLead=r,this.lead=t,t.show(),r)){r.instance&&r.scheduleRender(),t.scheduleRender(),t.resumeFrom=r,e&&(t.resumeFrom.preserveOpacity=!0),r.snapshot&&(t.snapshot=r.snapshot,t.snapshot.latestValues=r.animationValues||r.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);let{crossfade:i}=t.options;!1===i&&r.hide()}}exitAnimationComplete(){this.members.forEach(t=>{let{options:e,resumingFrom:r}=t;e.onExitComplete&&e.onExitComplete(),r&&r.options.onExitComplete&&r.options.onExitComplete()})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}let nE={nodes:0,calculatedTargetDeltas:0,calculatedProjections:0},nC=["","X","Y","Z"],nV=0;function nD(t,e,r,i){let{latestValues:n}=e;n[t]&&(r[t]=n[t],e.setStaticValue(t,0),i&&(i[t]=0))}function nO({attachResizeListener:t,defaultParent:e,measureScroll:r,checkIsScrollRoot:i,resetTransform:n}){return class{constructor(t={},r=e?.()){this.id=nV++,this.animationId=0,this.animationCommitId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,ta.value&&(nE.nodes=nE.calculatedTargetDeltas=nE.calculatedProjections=0),this.nodes.forEach(nj),this.nodes.forEach(nY),this.nodes.forEach(nU),this.nodes.forEach(nL),ta.addProjectionMetrics&&ta.addProjectionMetrics(nE)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=t,this.root=r?r.root||r:this,this.path=r?[...r.path,r]:[],this.parent=r,this.depth=r?r.depth+1:0;for(let t=0;t<this.path.length;t++)this.path[t].shouldResetTransform=!0;this.root===this&&(this.nodes=new ni)}addEventListener(t,e){return this.eventHandlers.has(t)||this.eventHandlers.set(t,new t2),this.eventHandlers.get(t).add(e)}notifyListeners(t,...e){let r=this.eventHandlers.get(t);r&&r.notify(...e)}hasListeners(t){return this.eventHandlers.has(t)}mount(e){if(this.instance)return;this.isSVG=ne(e)&&!(ne(e)&&"svg"===e.tagName),this.instance=e;let{layoutId:r,layout:i,visualElement:n}=this.options;if(n&&!n.current&&n.mount(e),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(i||r)&&(this.isLayoutDirty=!0),t){let r,i=0,n=()=>this.root.updateBlockedByResize=!1;tu.read(()=>{i=window.innerWidth}),t(e,()=>{let t=window.innerWidth;t!==i&&(i=t,this.root.updateBlockedByResize=!0,r&&r(),r=function(t,e){let r=tJ.now(),i=({timestamp:e})=>{let n=e-r;n>=250&&(th(i),t(n-250))};return tu.setup(i,!0),()=>th(i)}(n,250),i5.hasAnimatedSinceResize&&(i5.hasAnimatedSinceResize=!1,this.nodes.forEach(nN)))})}r&&this.root.registerSharedNode(r,this),!1!==this.options.animate&&n&&(r||i)&&this.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e,hasRelativeLayoutChanged:r,layout:i})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let s=this.options.transition||n.getDefaultTransition()||nG,{onLayoutAnimationStart:o,onLayoutAnimationComplete:a}=n.getProps(),l=!this.targetLayout||!nT(this.targetLayout,i),u=!e&&r;if(this.options.layoutRoot||this.resumeFrom||u||e&&(l||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0);let e={...e7(s,"layout"),onPlay:o,onComplete:a};(n.shouldReduceMotion||this.options.layoutRoot)&&(e.delay=0,e.type=!1),this.startAnimation(e),this.setAnimationOrigin(t,u)}else e||nN(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=i})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let t=this.getStack();t&&t.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),th(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(nW),this.animationId++)}getTransformTemplate(){let{visualElement:t}=this.options;return t&&t.getProps().transformTemplate}willUpdate(t=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&function t(e){if(e.hasCheckedOptimisedAppear=!0,e.root===e)return;let{visualElement:r}=e.options;if(!r)return;let i=r.props[e3];if(window.MotionHasOptimisedAnimation(i,"transform")){let{layout:t,layoutId:r}=e.options;window.MotionCancelOptimisedAnimation(i,"transform",tu,!(t||r))}let{parent:n}=e;n&&!n.hasCheckedOptimisedAppear&&t(n)}(this),this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let t=0;t<this.path.length;t++){let e=this.path[t];e.shouldResetTransform=!0,e.updateScroll("snapshot"),e.options.layoutRoot&&e.willUpdate(!1)}let{layoutId:e,layout:r}=this.options;if(void 0===e&&!r)return;let i=this.getTransformTemplate();this.prevTransformTemplateValue=i?i(this.latestValues,""):void 0,this.updateSnapshot(),t&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(nF);return}if(this.animationId<=this.animationCommitId)return void this.nodes.forEach(nB);this.animationCommitId=this.animationId,this.isUpdating?(this.isUpdating=!1,this.nodes.forEach(nI),this.nodes.forEach(nR),this.nodes.forEach(n_)):this.nodes.forEach(nB),this.clearAllSnapshots();let t=tJ.now();tc.delta=L(0,1e3/60,t-tc.timestamp),tc.timestamp=t,tc.isProcessing=!0,td.update.process(tc),td.preRender.process(tc),td.render.process(tc),tc.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,t9.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(nz),this.sharedNodes.forEach(nX)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,tu.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){tu.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure(),!this.snapshot||iO(this.snapshot.measuredBox.x)||iO(this.snapshot.measuredBox.y)||(this.snapshot=void 0))}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let t=0;t<this.path.length;t++)this.path[t].updateScroll();let t=this.layout;this.layout=this.measure(!1),this.layoutCorrected=ei(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:e}=this.options;e&&e.notify("LayoutMeasure",this.layout.layoutBox,t?t.layoutBox:void 0)}updateScroll(t="measure"){let e=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===t&&(e=!1),e&&this.instance){let e=i(this.instance);this.scroll={animationId:this.root.animationId,phase:t,isRoot:e,offset:r(this.instance),wasRoot:this.scroll?this.scroll.isRoot:e}}}resetTransform(){if(!n)return;let t=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,e=this.projectionDelta&&!nw(this.projectionDelta),r=this.getTransformTemplate(),i=r?r(this.latestValues,""):void 0,s=i!==this.prevTransformTemplateValue;t&&this.instance&&(e||P(this.latestValues)||s)&&(n(this.instance,i),this.shouldResetTransform=!1,this.scheduleRender())}measure(t=!0){var e;let r=this.measurePageBox(),i=this.removeElementScroll(r);return t&&(i=this.removeTransform(i)),nQ((e=i).x),nQ(e.y),{animationId:this.root.animationId,measuredBox:r,layoutBox:i,latestValues:{},source:this.id}}measurePageBox(){let{visualElement:t}=this.options;if(!t)return ei();let e=t.measureViewportBox();if(!(this.scroll?.wasRoot||this.path.some(n0))){let{scroll:t}=this.root;t&&(D(e.x,t.offset.x),D(e.y,t.offset.y))}return e}removeElementScroll(t){let e=ei();if(np(e,t),this.scroll?.wasRoot)return e;for(let r=0;r<this.path.length;r++){let i=this.path[r],{scroll:n,options:s}=i;i!==this.root&&n&&s.layoutScroll&&(n.wasRoot&&np(e,t),D(e.x,n.offset.x),D(e.y,n.offset.y))}return e}applyTransform(t,e=!1){let r=ei();np(r,t);for(let t=0;t<this.path.length;t++){let i=this.path[t];!e&&i.options.layoutScroll&&i.scroll&&i!==i.root&&R(r,{x:-i.scroll.offset.x,y:-i.scroll.offset.y}),P(i.latestValues)&&R(r,i.latestValues)}return P(this.latestValues)&&R(r,this.latestValues),r}removeTransform(t){let e=ei();np(e,t);for(let t=0;t<this.path.length;t++){let r=this.path[t];if(!r.instance||!P(r.latestValues))continue;M(r.latestValues)&&r.updateSnapshot();let i=ei();np(i,r.measurePageBox()),nx(e,r.latestValues,r.snapshot?r.snapshot.layoutBox:void 0,i)}return P(this.latestValues)&&nx(e,this.latestValues),e}setTargetDelta(t){this.targetDelta=t,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(t){this.options={...this.options,...t,crossfade:void 0===t.crossfade||t.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==tc.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(t=!1){let e=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=e.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=e.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=e.isSharedProjectionDirty);let r=!!this.resumingFrom||this!==e;if(!(t||r&&this.isSharedProjectionDirty||this.isProjectionDirty||this.parent?.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;let{layout:i,layoutId:n}=this.options;if(this.layout&&(i||n)){if(this.resolvedRelativeTargetAt=tc.timestamp,!this.targetDelta&&!this.relativeTarget){let t=this.getClosestProjectingParent();t&&t.layout&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=ei(),this.relativeTargetOrigin=ei(),iz(this.relativeTargetOrigin,this.layout.layoutBox,t.layout.layoutBox),np(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if(this.target||(this.target=ei(),this.targetWithTransforms=ei()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target){var s,o,a;this.forceRelativeParentToResolveTarget(),s=this.target,o=this.relativeTarget,a=this.relativeParent.target,ij(s.x,o.x,a.x),ij(s.y,o.y,a.y)}else this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):np(this.target,this.layout.layoutBox),V(this.target,this.targetDelta)):np(this.target,this.layout.layoutBox);if(this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let t=this.getClosestProjectingParent();t&&!!t.resumingFrom==!!this.resumingFrom&&!t.options.layoutScroll&&t.target&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=ei(),this.relativeTargetOrigin=ei(),iz(this.relativeTargetOrigin,this.target,t.target),np(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}ta.value&&nE.calculatedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||M(this.parent.latestValues)||S(this.parent.latestValues)))if(this.parent.isProjecting())return this.parent;else return this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){let t=this.getLead(),e=!!this.resumingFrom||this!==t,r=!0;if((this.isProjectionDirty||this.parent?.isProjectionDirty)&&(r=!1),e&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(r=!1),this.resolvedRelativeTargetAt===tc.timestamp&&(r=!1),r)return;let{layout:i,layoutId:n}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(i||n))return;np(this.layoutCorrected,this.layout.layoutBox);let s=this.treeScale.x,o=this.treeScale.y;!function(t,e,r,i=!1){let n,s,o=r.length;if(o){e.x=e.y=1;for(let a=0;a<o;a++){s=(n=r[a]).projectionDelta;let{visualElement:o}=n.options;(!o||!o.props.style||"contents"!==o.props.style.display)&&(i&&n.options.layoutScroll&&n.scroll&&n!==n.root&&R(t,{x:-n.scroll.offset.x,y:-n.scroll.offset.y}),s&&(e.x*=s.x.scale,e.y*=s.y.scale,V(t,s)),i&&P(n.latestValues)&&R(t,n.latestValues))}e.x<1.0000000000001&&e.x>.999999999999&&(e.x=1),e.y<1.0000000000001&&e.y>.999999999999&&(e.y=1)}}(this.layoutCorrected,this.treeScale,this.path,e),t.layout&&!t.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(t.target=t.layout.layoutBox,t.targetWithTransforms=ei());let{target:a}=t;if(!a){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}this.projectionDelta&&this.prevProjectionDelta?(nf(this.prevProjectionDelta.x,this.projectionDelta.x),nf(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),i_(this.projectionDelta,this.layoutCorrected,a,this.latestValues),this.treeScale.x===s&&this.treeScale.y===o&&nP(this.projectionDelta.x,this.prevProjectionDelta.x)&&nP(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",a)),ta.value&&nE.calculatedProjections++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(t=!0){if(this.options.visualElement?.scheduleRender(),t){let t=this.getStack();t&&t.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=ee(),this.projectionDelta=ee(),this.projectionDeltaWithTransform=ee()}setAnimationOrigin(t,e=!1){let r,i=this.snapshot,n=i?i.latestValues:{},s={...this.latestValues},o=ee();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!e;let a=ei(),l=(i?i.source:void 0)!==(this.layout?this.layout.source:void 0),u=this.getStack(),h=!u||u.members.length<=1,c=!!(l&&!h&&!0===this.options.crossfade&&!this.path.some(nq));this.animationProgress=0,this.mixTargetDelta=e=>{let i=e/1e3;if(n$(o.x,t.x,i),n$(o.y,t.y,i),this.setTargetDelta(o),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout){var u,d,p,f,m,g;iz(a,this.layout.layoutBox,this.relativeParent.layout.layoutBox),p=this.relativeTarget,f=this.relativeTargetOrigin,m=a,g=i,nH(p.x,f.x,m.x,g),nH(p.y,f.y,m.y,g),r&&(u=this.relativeTarget,d=r,nk(u.x,d.x)&&nk(u.y,d.y))&&(this.isProjectionDirty=!1),r||(r=ei()),np(r,this.relativeTarget)}l&&(this.animationValues=s,function(t,e,r,i,n,s){n?(t.opacity=A(0,r.opacity??1,nu(i)),t.opacityExit=A(e.opacity??1,0,nh(i))):s&&(t.opacity=A(e.opacity??1,r.opacity??1,i));for(let n=0;n<ns;n++){let s=`border${nn[n]}Radius`,o=nl(e,s),a=nl(r,s);(void 0!==o||void 0!==a)&&(o||(o=0),a||(a=0),0===o||0===a||na(o)===na(a)?(t[s]=Math.max(A(no(o),no(a),i),0),(Y.test(a)||Y.test(o))&&(t[s]+="%")):t[s]=a)}(e.rotate||r.rotate)&&(t.rotate=A(e.rotate||0,r.rotate||0,i))}(s,n,this.latestValues,i,c,h)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=i},this.mixTargetDelta(1e3*!!this.options.layoutRoot)}startAnimation(t){this.notifyListeners("animationStart"),this.currentAnimation?.stop(),this.resumingFrom?.currentAnimation?.stop(),this.pendingAnimation&&(th(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=tu.update(()=>{i5.hasAnimatedSinceResize=!0,ro.layout++,this.motionValue||(this.motionValue=t4(0)),this.currentAnimation=function(t,e,r){let i=tZ(t)?t:t4(t);return i.start(id("",i,e,r)),i.animation}(this.motionValue,[0,1e3],{...t,velocity:0,isSync:!0,onUpdate:e=>{this.mixTargetDelta(e),t.onUpdate&&t.onUpdate(e)},onStop:()=>{ro.layout--},onComplete:()=>{ro.layout--,t.onComplete&&t.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let t=this.getStack();t&&t.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){let t=this.getLead(),{targetWithTransforms:e,target:r,layout:i,latestValues:n}=t;if(e&&r&&i){if(this!==t&&this.layout&&i&&nJ(this.options.animationType,this.layout.layoutBox,i.layoutBox)){r=this.target||ei();let e=iO(this.layout.layoutBox.x);r.x.min=t.target.x.min,r.x.max=r.x.min+e;let i=iO(this.layout.layoutBox.y);r.y.min=t.target.y.min,r.y.max=r.y.min+i}np(e,r),R(e,n),i_(this.projectionDeltaWithTransform,this.layoutCorrected,e,n)}}registerSharedNode(t,e){this.sharedNodes.has(t)||this.sharedNodes.set(t,new nS),this.sharedNodes.get(t).add(e);let r=e.options.initialPromotionConfig;e.promote({transition:r?r.transition:void 0,preserveFollowOpacity:r&&r.shouldPreserveFollowOpacity?r.shouldPreserveFollowOpacity(e):void 0})}isLead(){let t=this.getStack();return!t||t.lead===this}getLead(){let{layoutId:t}=this.options;return t&&this.getStack()?.lead||this}getPrevLead(){let{layoutId:t}=this.options;return t?this.getStack()?.prevLead:void 0}getStack(){let{layoutId:t}=this.options;if(t)return this.root.sharedNodes.get(t)}promote({needsReset:t,transition:e,preserveFollowOpacity:r}={}){let i=this.getStack();i&&i.promote(this,r),t&&(this.projectionDelta=void 0,this.needsReset=!0),e&&this.setOptions({transition:e})}relegate(){let t=this.getStack();return!!t&&t.relegate(this)}resetSkewAndRotation(){let{visualElement:t}=this.options;if(!t)return;let e=!1,{latestValues:r}=t;if((r.z||r.rotate||r.rotateX||r.rotateY||r.rotateZ||r.skewX||r.skewY)&&(e=!0),!e)return;let i={};r.z&&nD("z",t,i,this.animationValues);for(let e=0;e<nC.length;e++)nD(`rotate${nC[e]}`,t,i,this.animationValues),nD(`skew${nC[e]}`,t,i,this.animationValues);for(let e in t.render(),i)t.setStaticValue(e,i[e]),this.animationValues&&(this.animationValues[e]=i[e]);t.scheduleRender()}applyProjectionStyles(t,e){if(!this.instance||this.isSVG)return;if(!this.isVisible){t.visibility="hidden";return}let r=this.getTransformTemplate();if(this.needsReset){this.needsReset=!1,t.visibility="",t.opacity="",t.pointerEvents=eQ(e?.pointerEvents)||"",t.transform=r?r(this.latestValues,""):"none";return}let i=this.getLead();if(!this.projectionDelta||!this.layout||!i.target){this.options.layoutId&&(t.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,t.pointerEvents=eQ(e?.pointerEvents)||""),this.hasProjected&&!P(this.latestValues)&&(t.transform=r?r({},""):"none",this.hasProjected=!1);return}t.visibility="";let n=i.animationValues||i.latestValues;this.applyTransformsToTarget();let s=function(t,e,r){let i="",n=t.x.translate/e.x,s=t.y.translate/e.y,o=r?.z||0;if((n||s||o)&&(i=`translate3d(${n}px, ${s}px, ${o}px) `),(1!==e.x||1!==e.y)&&(i+=`scale(${1/e.x}, ${1/e.y}) `),r){let{transformPerspective:t,rotate:e,rotateX:n,rotateY:s,skewX:o,skewY:a}=r;t&&(i=`perspective(${t}px) ${i}`),e&&(i+=`rotate(${e}deg) `),n&&(i+=`rotateX(${n}deg) `),s&&(i+=`rotateY(${s}deg) `),o&&(i+=`skewX(${o}deg) `),a&&(i+=`skewY(${a}deg) `)}let a=t.x.scale*e.x,l=t.y.scale*e.y;return(1!==a||1!==l)&&(i+=`scale(${a}, ${l})`),i||"none"}(this.projectionDeltaWithTransform,this.treeScale,n);r&&(s=r(n,s)),t.transform=s;let{x:o,y:a}=this.projectionDelta;for(let e in t.transformOrigin=`${100*o.origin}% ${100*a.origin}% 0`,i.animationValues?t.opacity=i===this?n.opacity??this.latestValues.opacity??1:this.preserveOpacity?this.latestValues.opacity:n.opacityExit:t.opacity=i===this?void 0!==n.opacity?n.opacity:"":void 0!==n.opacityExit?n.opacityExit:0,eT){if(void 0===n[e])continue;let{correct:r,applyTo:o,isCSSVariable:a}=eT[e],l="none"===s?n[e]:r(n[e],i);if(o){let e=o.length;for(let r=0;r<e;r++)t[o[r]]=l}else a?this.options.visualElement.renderState.vars[e]=l:t[e]=l}this.options.layoutId&&(t.pointerEvents=i===this?eQ(e?.pointerEvents)||"":"none")}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(t=>t.currentAnimation?.stop()),this.root.nodes.forEach(nF),this.root.sharedNodes.clear()}}}function nR(t){t.updateLayout()}function n_(t){let e=t.resumeFrom?.snapshot||t.snapshot;if(t.isLead()&&t.layout&&e&&t.hasListeners("didUpdate")){let{layoutBox:r,measuredBox:i}=t.layout,{animationType:n}=t.options,s=e.source!==t.layout.source;"size"===n?iF(t=>{let i=s?e.measuredBox[t]:e.layoutBox[t],n=iO(i);i.min=r[t].min,i.max=i.min+n}):nJ(n,e.layoutBox,r)&&iF(i=>{let n=s?e.measuredBox[i]:e.layoutBox[i],o=iO(r[i]);n.max=n.min+o,t.relativeTarget&&!t.currentAnimation&&(t.isProjectionDirty=!0,t.relativeTarget[i].max=t.relativeTarget[i].min+o)});let o=ee();i_(o,r,e.layoutBox);let a=ee();s?i_(a,t.applyTransform(i,!0),e.measuredBox):i_(a,r,e.layoutBox);let l=!nw(o),u=!1;if(!t.resumeFrom){let i=t.getClosestProjectingParent();if(i&&!i.resumeFrom){let{snapshot:n,layout:s}=i;if(n&&s){let o=ei();iz(o,e.layoutBox,n.layoutBox);let a=ei();iz(a,r,s.layoutBox),nT(o,a)||(u=!0),i.options.layoutRoot&&(t.relativeTarget=a,t.relativeTargetOrigin=o,t.relativeParent=i)}}}t.notifyListeners("didUpdate",{layout:r,snapshot:e,delta:a,layoutDelta:o,hasLayoutChanged:l,hasRelativeLayoutChanged:u})}else if(t.isLead()){let{onExitComplete:e}=t.options;e&&e()}t.options.transition=void 0}function nj(t){ta.value&&nE.nodes++,t.parent&&(t.isProjecting()||(t.isProjectionDirty=t.parent.isProjectionDirty),t.isSharedProjectionDirty||(t.isSharedProjectionDirty=!!(t.isProjectionDirty||t.parent.isProjectionDirty||t.parent.isSharedProjectionDirty)),t.isTransformDirty||(t.isTransformDirty=t.parent.isTransformDirty))}function nL(t){t.isProjectionDirty=t.isSharedProjectionDirty=t.isTransformDirty=!1}function nz(t){t.clearSnapshot()}function nF(t){t.clearMeasurements()}function nB(t){t.isLayoutDirty=!1}function nI(t){let{visualElement:e}=t.options;e&&e.getProps().onBeforeLayoutMeasure&&e.notify("BeforeLayoutMeasure"),t.resetTransform()}function nN(t){t.finishAnimation(),t.targetDelta=t.relativeTarget=t.target=void 0,t.isProjectionDirty=!0}function nY(t){t.resolveTargetDelta()}function nU(t){t.calcProjection()}function nW(t){t.resetSkewAndRotation()}function nX(t){t.removeLeadSnapshot()}function n$(t,e,r){t.translate=A(e.translate,0,r),t.scale=A(e.scale,1,r),t.origin=e.origin,t.originPoint=e.originPoint}function nH(t,e,r,i){t.min=A(e.min,r.min,i),t.max=A(e.max,r.max,i)}function nq(t){return t.animationValues&&void 0!==t.animationValues.opacityExit}let nG={duration:.45,ease:[.4,0,.1,1]},nK=t=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(t),nZ=nK("applewebkit/")&&!nK("chrome/")?Math.round:tn;function nQ(t){t.min=nZ(t.min),t.max=nZ(t.max)}function nJ(t,e,r){return"position"===t||"preserve-aspect"===t&&!(.2>=Math.abs(nM(e)-nM(r)))}function n0(t){return t!==t.root&&t.scroll?.wasRoot}let n1=nO({attachResizeListener:(t,e)=>iE(t,"resize",e),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),n2={current:void 0},n5=nO({measureScroll:t=>({x:t.scrollLeft,y:t.scrollTop}),defaultParent:()=>{if(!n2.current){let t=new n1({});t.mount(window),t.setOptions({layoutScroll:!0}),n2.current=t}return n2.current},resetTransform:(t,e)=>{t.style.transform=void 0!==e?e:"none"},checkIsScrollRoot:t=>"fixed"===window.getComputedStyle(t).position});function n3(t,e){let r=function(t,e,r){if(t instanceof EventTarget)return[t];if("string"==typeof t){let e=document,r=(void 0)??e.querySelectorAll(t);return r?Array.from(r):[]}return Array.from(t)}(t),i=new AbortController;return[r,{passive:!0,...e,signal:i.signal},()=>i.abort()]}function n4(t){return!("touch"===t.pointerType||iS.x||iS.y)}function n6(t,e,r){let{props:i}=t;t.animationState&&i.whileHover&&t.animationState.setActive("whileHover","Start"===r);let n=i["onHover"+r];n&&tu.postRender(()=>n(e,iV(e)))}class n9 extends iA{mount(){let{current:t}=this.node;t&&(this.unmount=function(t,e,r={}){let[i,n,s]=n3(t,r),o=t=>{if(!n4(t))return;let{target:r}=t,i=e(r,t);if("function"!=typeof i||!r)return;let s=t=>{n4(t)&&(i(t),r.removeEventListener("pointerleave",s))};r.addEventListener("pointerleave",s,n)};return i.forEach(t=>{t.addEventListener("pointerenter",o,n)}),s}(t,(t,e)=>(n6(this.node,e,"Start"),t=>n6(this.node,t,"End"))))}unmount(){}}class n8 extends iA{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch(e){t=!0}t&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=rn(iE(this.node.current,"focus",()=>this.onFocus()),iE(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}var n7=r(7351);let st=(t,e)=>!!e&&(t===e||st(t,e.parentElement)),se=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]),sr=new WeakSet;function si(t){return e=>{"Enter"===e.key&&t(e)}}function sn(t,e){t.dispatchEvent(new PointerEvent("pointer"+e,{isPrimary:!0,bubbles:!0}))}function ss(t){return iC(t)&&!(iS.x||iS.y)}function so(t,e,r){let{props:i}=t;if(t.current instanceof HTMLButtonElement&&t.current.disabled)return;t.animationState&&i.whileTap&&t.animationState.setActive("whileTap","Start"===r);let n=i["onTap"+("End"===r?"":r)];n&&tu.postRender(()=>n(e,iV(e)))}class sa extends iA{mount(){let{current:t}=this.node;t&&(this.unmount=function(t,e,r={}){let[i,n,s]=n3(t,r),o=t=>{let i=t.currentTarget;if(!ss(t))return;sr.add(i);let s=e(i,t),o=(t,e)=>{window.removeEventListener("pointerup",a),window.removeEventListener("pointercancel",l),sr.has(i)&&sr.delete(i),ss(t)&&"function"==typeof s&&s(t,{success:e})},a=t=>{o(t,i===window||i===document||r.useGlobalTarget||st(i,t.target))},l=t=>{o(t,!1)};window.addEventListener("pointerup",a,n),window.addEventListener("pointercancel",l,n)};return i.forEach(t=>{((r.useGlobalTarget?window:t).addEventListener("pointerdown",o,n),(0,n7.s)(t))&&(t.addEventListener("focus",t=>((t,e)=>{let r=t.currentTarget;if(!r)return;let i=si(()=>{if(sr.has(r))return;sn(r,"down");let t=si(()=>{sn(r,"up")});r.addEventListener("keyup",t,e),r.addEventListener("blur",()=>sn(r,"cancel"),e)});r.addEventListener("keydown",i,e),r.addEventListener("blur",()=>r.removeEventListener("keydown",i),e)})(t,n)),se.has(t.tagName)||-1!==t.tabIndex||t.hasAttribute("tabindex")||(t.tabIndex=0))}),s}(t,(t,e)=>(so(this.node,e,"Start"),(t,{success:e})=>so(this.node,t,e?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}let sl=new WeakMap,su=new WeakMap,sh=t=>{let e=sl.get(t.target);e&&e(t)},sc=t=>{t.forEach(sh)},sd={some:0,all:1};class sp extends iA{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();let{viewport:t={}}=this.node.getProps(),{root:e,margin:r,amount:i="some",once:n}=t,s={root:e?e.current:void 0,rootMargin:r,threshold:"number"==typeof i?i:sd[i]},o=t=>{let{isIntersecting:e}=t;if(this.isInView===e||(this.isInView=e,n&&!e&&this.hasEnteredView))return;e&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",e);let{onViewportEnter:r,onViewportLeave:i}=this.node.getProps(),s=e?r:i;s&&s(t)};var a=this.node.current;let l=function({root:t,...e}){let r=t||document;su.has(r)||su.set(r,{});let i=su.get(r),n=JSON.stringify(e);return i[n]||(i[n]=new IntersectionObserver(sc,{root:t,...e})),i[n]}(s);return sl.set(a,o),l.observe(a),()=>{sl.delete(a),l.unobserve(a)}}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;let{props:t,prevProps:e}=this.node;["amount","margin","root"].some(function({viewport:t={}},{viewport:e={}}={}){return r=>t[r]!==e[r]}(t,e))&&this.startObserver()}unmount(){}}let sf=function(t,e){if("undefined"==typeof Proxy)return e9;let r=new Map,i=(r,i)=>e9(r,i,t,e);return new Proxy((t,e)=>i(t,e),{get:(n,s)=>"create"===s?i:(r.has(s)||r.set(s,e9(s,void 0,t,e)),r.get(s))})}({animation:{Feature:iT},exit:{Feature:iP},inView:{Feature:sp},tap:{Feature:sa},focus:{Feature:n8},hover:{Feature:n9},pan:{Feature:i1},drag:{Feature:iJ,ProjectionNode:n5,MeasureLayout:i8},layout:{ProjectionNode:n5,MeasureLayout:i8}},(t,e)=>ez(t)?new ej(e):new eS(e,{allowProjection:t!==n.Fragment}))},8564:(t,e,r)=>{r.d(e,{A:()=>i});let i=(0,r(9946).A)("star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]])},8883:(t,e,r)=>{r.d(e,{A:()=>i});let i=(0,r(9946).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},8972:(t,e,r)=>{r.d(e,{B:()=>i});let i="undefined"!=typeof window},9074:(t,e,r)=>{r.d(e,{A:()=>i});let i=(0,r(9946).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},9088:(t,e,r)=>{r.d(e,{u:()=>e1});var i,n,s,o,a,l,u,h,c,d,p,f,m,g=function(){return i||"undefined"!=typeof window&&(i=window.gsap)&&i.registerPlugin&&i},v=1,y=[],x=[],b=[],w=Date.now,k=function(t,e){return e},A=function(){var t=c.core,e=t.bridge||{},r=t._scrollers,i=t._proxies;r.push.apply(r,x),i.push.apply(i,b),x=r,b=i,k=function(t,r){return e[t](r)}},T=function(t,e){return~b.indexOf(t)&&b[b.indexOf(t)+1][e]},M=function(t){return!!~d.indexOf(t)},P=function(t,e,r,i,n){return t.addEventListener(e,r,{passive:!1!==i,capture:!!n})},S=function(t,e,r,i){return t.removeEventListener(e,r,!!i)},E="scrollLeft",C="scrollTop",V=function(){return p&&p.isPressed||x.cache++},D=function(t,e){var r=function r(i){if(i||0===i){v&&(s.history.scrollRestoration="manual");var n=p&&p.isPressed;t(i=r.v=Math.round(i)||(p&&p.iOS?1:0)),r.cacheID=x.cache,n&&k("ss",i)}else(e||x.cache!==r.cacheID||k("ref"))&&(r.cacheID=x.cache,r.v=t());return r.v+r.offset};return r.offset=0,t&&r},O={s:E,p:"left",p2:"Left",os:"right",os2:"Right",d:"width",d2:"Width",a:"x",sc:D(function(t){return arguments.length?s.scrollTo(t,R.sc()):s.pageXOffset||o[E]||a[E]||l[E]||0})},R={s:C,p:"top",p2:"Top",os:"bottom",os2:"Bottom",d:"height",d2:"Height",a:"y",op:O,sc:D(function(t){return arguments.length?s.scrollTo(O.sc(),t):s.pageYOffset||o[C]||a[C]||l[C]||0})},_=function(t,e){return(e&&e._ctx&&e._ctx.selector||i.utils.toArray)(t)[0]||("string"==typeof t&&!1!==i.config().nullTargetWarn?console.warn("Element not found:",t):null)},j=function(t,e){for(var r=e.length;r--;)if(e[r]===t||e[r].contains(t))return!0;return!1},L=function(t,e){var r=e.s,n=e.sc;M(t)&&(t=o.scrollingElement||a);var s=x.indexOf(t),l=n===R.sc?1:2;~s||(s=x.push(t)-1),x[s+l]||P(t,"scroll",V);var u=x[s+l],h=u||(x[s+l]=D(T(t,r),!0)||(M(t)?n:D(function(e){return arguments.length?t[r]=e:t[r]})));return h.target=t,u||(h.smooth="smooth"===i.getProperty(t,"scrollBehavior")),h},z=function(t,e,r){var i=t,n=t,s=w(),o=s,a=e||50,l=Math.max(500,3*a),u=function(t,e){var l=w();e||l-s>a?(n=i,i=t,o=s,s=l):r?i+=t:i=n+(t-n)/(l-o)*(s-o)};return{update:u,reset:function(){n=i=r?0:i,o=s=0},getVelocity:function(t){var e=o,a=n,h=w();return(t||0===t)&&t!==i&&u(t),s===o||h-o>l?0:(i+(r?a:-a))/((r?h:s)-e)*1e3}}},F=function(t,e){return e&&!t._gsapAllow&&t.preventDefault(),t.changedTouches?t.changedTouches[0]:t},B=function(t){var e=Math.max.apply(Math,t),r=Math.min.apply(Math,t);return Math.abs(e)>=Math.abs(r)?e:r},I=function(){(c=i.core.globals().ScrollTrigger)&&c.core&&A()},N=function(t){return i=t||g(),!n&&i&&"undefined"!=typeof document&&document.body&&(s=window,a=(o=document).documentElement,l=o.body,d=[s,o,a,l],i.utils.clamp,m=i.core.context||function(){},h="onpointerenter"in l?"pointer":"mouse",u=Y.isTouch=s.matchMedia&&s.matchMedia("(hover: none), (pointer: coarse)").matches?1:2*("ontouchstart"in s||navigator.maxTouchPoints>0||navigator.msMaxTouchPoints>0),f=Y.eventTypes=("ontouchstart"in a?"touchstart,touchmove,touchcancel,touchend":!("onpointerdown"in a)?"mousedown,mousemove,mouseup,mouseup":"pointerdown,pointermove,pointercancel,pointerup").split(","),setTimeout(function(){return v=0},500),I(),n=1),n};O.op=R,x.cache=0;var Y=function(){var t;function e(t){this.init(t)}return e.prototype.init=function(t){n||N(i)||console.warn("Please gsap.registerPlugin(Observer)"),c||I();var e=t.tolerance,r=t.dragMinimum,d=t.type,g=t.target,v=t.lineHeight,x=t.debounce,b=t.preventDefault,k=t.onStop,A=t.onStopDelay,T=t.ignore,E=t.wheelSpeed,C=t.event,D=t.onDragStart,Y=t.onDragEnd,U=t.onDrag,W=t.onPress,X=t.onRelease,$=t.onRight,H=t.onLeft,q=t.onUp,G=t.onDown,K=t.onChangeX,Z=t.onChangeY,Q=t.onChange,J=t.onToggleX,tt=t.onToggleY,te=t.onHover,tr=t.onHoverEnd,ti=t.onMove,tn=t.ignoreCheck,ts=t.isNormalizer,to=t.onGestureStart,ta=t.onGestureEnd,tl=t.onWheel,tu=t.onEnable,th=t.onDisable,tc=t.onClick,td=t.scrollSpeed,tp=t.capture,tf=t.allowClicks,tm=t.lockAxis,tg=t.onLockAxis;this.target=g=_(g)||a,this.vars=t,T&&(T=i.utils.toArray(T)),e=e||1e-9,r=r||0,E=E||1,td=td||1,d=d||"wheel,touch,pointer",x=!1!==x,v||(v=parseFloat(s.getComputedStyle(l).lineHeight)||22);var tv,ty,tx,tb,tw,tk,tA,tT=this,tM=0,tP=0,tS=t.passive||!b&&!1!==t.passive,tE=L(g,O),tC=L(g,R),tV=tE(),tD=tC(),tO=~d.indexOf("touch")&&!~d.indexOf("pointer")&&"pointerdown"===f[0],tR=M(g),t_=g.ownerDocument||o,tj=[0,0,0],tL=[0,0,0],tz=0,tF=function(){return tz=w()},tB=function(t,e){return(tT.event=t)&&T&&j(t.target,T)||e&&tO&&"touch"!==t.pointerType||tn&&tn(t,e)},tI=function(){var t=tT.deltaX=B(tj),r=tT.deltaY=B(tL),i=Math.abs(t)>=e,n=Math.abs(r)>=e;Q&&(i||n)&&Q(tT,t,r,tj,tL),i&&($&&tT.deltaX>0&&$(tT),H&&tT.deltaX<0&&H(tT),K&&K(tT),J&&tT.deltaX<0!=tM<0&&J(tT),tM=tT.deltaX,tj[0]=tj[1]=tj[2]=0),n&&(G&&tT.deltaY>0&&G(tT),q&&tT.deltaY<0&&q(tT),Z&&Z(tT),tt&&tT.deltaY<0!=tP<0&&tt(tT),tP=tT.deltaY,tL[0]=tL[1]=tL[2]=0),(tb||tx)&&(ti&&ti(tT),tx&&(D&&1===tx&&D(tT),U&&U(tT),tx=0),tb=!1),tk&&(tk=!1,1)&&tg&&tg(tT),tw&&(tl(tT),tw=!1),tv=0},tN=function(t,e,r){tj[r]+=t,tL[r]+=e,tT._vx.update(t),tT._vy.update(e),x?tv||(tv=requestAnimationFrame(tI)):tI()},tY=function(t,e){tm&&!tA&&(tT.axis=tA=Math.abs(t)>Math.abs(e)?"x":"y",tk=!0),"y"!==tA&&(tj[2]+=t,tT._vx.update(t,!0)),"x"!==tA&&(tL[2]+=e,tT._vy.update(e,!0)),x?tv||(tv=requestAnimationFrame(tI)):tI()},tU=function(t){if(!tB(t,1)){var e=(t=F(t,b)).clientX,i=t.clientY,n=e-tT.x,s=i-tT.y,o=tT.isDragging;tT.x=e,tT.y=i,(o||(n||s)&&(Math.abs(tT.startX-e)>=r||Math.abs(tT.startY-i)>=r))&&(tx=o?2:1,o||(tT.isDragging=!0),tY(n,s))}},tW=tT.onPress=function(t){tB(t,1)||t&&t.button||(tT.axis=tA=null,ty.pause(),tT.isPressed=!0,t=F(t),tM=tP=0,tT.startX=tT.x=t.clientX,tT.startY=tT.y=t.clientY,tT._vx.reset(),tT._vy.reset(),P(ts?g:t_,f[1],tU,tS,!0),tT.deltaX=tT.deltaY=0,W&&W(tT))},tX=tT.onRelease=function(t){if(!tB(t,1)){S(ts?g:t_,f[1],tU,!0);var e=!isNaN(tT.y-tT.startY),r=tT.isDragging,n=r&&(Math.abs(tT.x-tT.startX)>3||Math.abs(tT.y-tT.startY)>3),o=F(t);!n&&e&&(tT._vx.reset(),tT._vy.reset(),b&&tf&&i.delayedCall(.08,function(){if(w()-tz>300&&!t.defaultPrevented){if(t.target.click)t.target.click();else if(t_.createEvent){var e=t_.createEvent("MouseEvents");e.initMouseEvent("click",!0,!0,s,1,o.screenX,o.screenY,o.clientX,o.clientY,!1,!1,!1,!1,0,null),t.target.dispatchEvent(e)}}})),tT.isDragging=tT.isGesturing=tT.isPressed=!1,k&&r&&!ts&&ty.restart(!0),tx&&tI(),Y&&r&&Y(tT),X&&X(tT,n)}},t$=function(t){return t.touches&&t.touches.length>1&&(tT.isGesturing=!0)&&to(t,tT.isDragging)},tH=function(){return tT.isGesturing=!1,ta(tT)},tq=function(t){if(!tB(t)){var e=tE(),r=tC();tN((e-tV)*td,(r-tD)*td,1),tV=e,tD=r,k&&ty.restart(!0)}},tG=function(t){if(!tB(t)){t=F(t,b),tl&&(tw=!0);var e=(1===t.deltaMode?v:2===t.deltaMode?s.innerHeight:1)*E;tN(t.deltaX*e,t.deltaY*e,0),k&&!ts&&ty.restart(!0)}},tK=function(t){if(!tB(t)){var e=t.clientX,r=t.clientY,i=e-tT.x,n=r-tT.y;tT.x=e,tT.y=r,tb=!0,k&&ty.restart(!0),(i||n)&&tY(i,n)}},tZ=function(t){tT.event=t,te(tT)},tQ=function(t){tT.event=t,tr(tT)},tJ=function(t){return tB(t)||F(t,b)&&tc(tT)};ty=tT._dc=i.delayedCall(A||.25,function(){tT._vx.reset(),tT._vy.reset(),ty.pause(),k&&k(tT)}).pause(),tT.deltaX=tT.deltaY=0,tT._vx=z(0,50,!0),tT._vy=z(0,50,!0),tT.scrollX=tE,tT.scrollY=tC,tT.isDragging=tT.isGesturing=tT.isPressed=!1,m(this),tT.enable=function(t){return!tT.isEnabled&&(P(tR?t_:g,"scroll",V),d.indexOf("scroll")>=0&&P(tR?t_:g,"scroll",tq,tS,tp),d.indexOf("wheel")>=0&&P(g,"wheel",tG,tS,tp),(d.indexOf("touch")>=0&&u||d.indexOf("pointer")>=0)&&(P(g,f[0],tW,tS,tp),P(t_,f[2],tX),P(t_,f[3],tX),tf&&P(g,"click",tF,!0,!0),tc&&P(g,"click",tJ),to&&P(t_,"gesturestart",t$),ta&&P(t_,"gestureend",tH),te&&P(g,h+"enter",tZ),tr&&P(g,h+"leave",tQ),ti&&P(g,h+"move",tK)),tT.isEnabled=!0,tT.isDragging=tT.isGesturing=tT.isPressed=tb=tx=!1,tT._vx.reset(),tT._vy.reset(),tV=tE(),tD=tC(),t&&t.type&&tW(t),tu&&tu(tT)),tT},tT.disable=function(){tT.isEnabled&&(y.filter(function(t){return t!==tT&&M(t.target)}).length||S(tR?t_:g,"scroll",V),tT.isPressed&&(tT._vx.reset(),tT._vy.reset(),S(ts?g:t_,f[1],tU,!0)),S(tR?t_:g,"scroll",tq,tp),S(g,"wheel",tG,tp),S(g,f[0],tW,tp),S(t_,f[2],tX),S(t_,f[3],tX),S(g,"click",tF,!0),S(g,"click",tJ),S(t_,"gesturestart",t$),S(t_,"gestureend",tH),S(g,h+"enter",tZ),S(g,h+"leave",tQ),S(g,h+"move",tK),tT.isEnabled=tT.isPressed=tT.isDragging=!1,th&&th(tT))},tT.kill=tT.revert=function(){tT.disable();var t=y.indexOf(tT);t>=0&&y.splice(t,1),p===tT&&(p=0)},y.push(tT),ts&&M(g)&&(p=tT),tT.enable(C)},t=[{key:"velocityX",get:function(){return this._vx.getVelocity()}},{key:"velocityY",get:function(){return this._vy.getVelocity()}}],function(t,e){for(var r=0;r<e.length;r++){var i=e[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,i.key,i)}}(e.prototype,t),e}();Y.version="3.13.0",Y.create=function(t){return new Y(t)},Y.register=N,Y.getAll=function(){return y.slice()},Y.getById=function(t){return y.filter(function(e){return e.vars.id===t})[0]},g()&&i.registerPlugin(Y);var U,W,X,$,H,q,G,K,Z,Q,J,tt,te,tr,ti,tn,ts,to,ta,tl,tu,th,tc,td,tp,tf,tm,tg,tv,ty,tx,tb,tw,tk,tA,tT,tM,tP,tS=1,tE=Date.now,tC=tE(),tV=0,tD=0,tO=function(t,e,r){var i=t$(t)&&("clamp("===t.substr(0,6)||t.indexOf("max")>-1);return r["_"+e+"Clamp"]=i,i?t.substr(6,t.length-7):t},tR=function(t,e){return e&&(!t$(t)||"clamp("!==t.substr(0,6))?"clamp("+t+")":t},t_=function(){return tr=1},tj=function(){return tr=0},tL=function(t){return t},tz=function(t){return Math.round(1e5*t)/1e5||0},tF=function(){return"undefined"!=typeof window},tB=function(){return U||tF()&&(U=window.gsap)&&U.registerPlugin&&U},tI=function(t){return!!~G.indexOf(t)},tN=function(t){return("Height"===t?tx:X["inner"+t])||H["client"+t]||q["client"+t]},tY=function(t){return T(t,"getBoundingClientRect")||(tI(t)?function(){return eq.width=X.innerWidth,eq.height=tx,eq}:function(){return en(t)})},tU=function(t,e,r){var i=r.d,n=r.d2,s=r.a;return(s=T(t,"getBoundingClientRect"))?function(){return s()[i]}:function(){return(e?tN(n):t["client"+n])||0}},tW=function(t,e){var r=e.s,i=e.d2,n=e.d,s=e.a;return Math.max(0,(s=T(t,r="scroll"+i))?s()-tY(t)()[n]:tI(t)?(H[r]||q[r])-tN(i):t[r]-t["offset"+i])},tX=function(t,e){for(var r=0;r<ta.length;r+=3)(!e||~e.indexOf(ta[r+1]))&&t(ta[r],ta[r+1],ta[r+2])},t$=function(t){return"string"==typeof t},tH=function(t){return"function"==typeof t},tq=function(t){return"number"==typeof t},tG=function(t){return"object"==typeof t},tK=function(t,e,r){return t&&t.progress(+!e)&&r&&t.pause()},tZ=function(t,e){if(t.enabled){var r=t._ctx?t._ctx.add(function(){return e(t)}):e(t);r&&r.totalTime&&(t.callbackAnimation=r)}},tQ=Math.abs,tJ="left",t0="right",t1="bottom",t2="width",t5="height",t3="Right",t4="Left",t6="Bottom",t9="padding",t8="margin",t7="Width",et="Height",ee=function(t){return X.getComputedStyle(t)},er=function(t){var e=ee(t).position;t.style.position="absolute"===e||"fixed"===e?e:"relative"},ei=function(t,e){for(var r in e)r in t||(t[r]=e[r]);return t},en=function(t,e){var r=e&&"matrix(1, 0, 0, 1, 0, 0)"!==ee(t)[ti]&&U.to(t,{x:0,y:0,xPercent:0,yPercent:0,rotation:0,rotationX:0,rotationY:0,scale:1,skewX:0,skewY:0}).progress(1),i=t.getBoundingClientRect();return r&&r.progress(0).kill(),i},es=function(t,e){var r=e.d2;return t["offset"+r]||t["client"+r]||0},eo=function(t){var e,r=[],i=t.labels,n=t.duration();for(e in i)r.push(i[e]/n);return r},ea=function(t){var e=U.utils.snap(t),r=Array.isArray(t)&&t.slice(0).sort(function(t,e){return t-e});return r?function(t,i,n){var s;if(void 0===n&&(n=.001),!i)return e(t);if(i>0){for(t-=n,s=0;s<r.length;s++)if(r[s]>=t)return r[s];return r[s-1]}for(s=r.length,t+=n;s--;)if(r[s]<=t)return r[s];return r[0]}:function(r,i,n){void 0===n&&(n=.001);var s=e(r);return!i||Math.abs(s-r)<n||s-r<0==i<0?s:e(i<0?r-t:r+t)}},el=function(t,e,r,i){return r.split(",").forEach(function(r){return t(e,r,i)})},eu=function(t,e,r,i,n){return t.addEventListener(e,r,{passive:!i,capture:!!n})},eh=function(t,e,r,i){return t.removeEventListener(e,r,!!i)},ec=function(t,e,r){(r=r&&r.wheelHandler)&&(t(e,"wheel",r),t(e,"touchmove",r))},ed={startColor:"green",endColor:"red",indent:0,fontSize:"16px",fontWeight:"normal"},ep={toggleActions:"play",anticipatePin:0},ef={top:0,left:0,center:.5,bottom:1,right:1},em=function(t,e){if(t$(t)){var r=t.indexOf("="),i=~r?(t.charAt(r-1)+1)*parseFloat(t.substr(r+1)):0;~r&&(t.indexOf("%")>r&&(i*=e/100),t=t.substr(0,r-1)),t=i+(t in ef?ef[t]*e:~t.indexOf("%")?parseFloat(t)*e/100:parseFloat(t)||0)}return t},eg=function(t,e,r,i,n,s,o,a){var l=n.startColor,u=n.endColor,h=n.fontSize,c=n.indent,d=n.fontWeight,p=$.createElement("div"),f=tI(r)||"fixed"===T(r,"pinType"),m=-1!==t.indexOf("scroller"),g=f?q:r,v=-1!==t.indexOf("start"),y=v?l:u,x="border-color:"+y+";font-size:"+h+";color:"+y+";font-weight:"+d+";pointer-events:none;white-space:nowrap;font-family:sans-serif,Arial;z-index:1000;padding:4px 8px;border-width:0;border-style:solid;";return x+="position:"+((m||a)&&f?"fixed;":"absolute;"),(m||a||!f)&&(x+=(i===R?t0:t1)+":"+(s+parseFloat(c))+"px;"),o&&(x+="box-sizing:border-box;text-align:left;width:"+o.offsetWidth+"px;"),p._isStart=v,p.setAttribute("class","gsap-marker-"+t+(e?" marker-"+e:"")),p.style.cssText=x,p.innerText=e||0===e?t+"-"+e:t,g.children[0]?g.insertBefore(p,g.children[0]):g.appendChild(p),p._offset=p["offset"+i.op.d2],ev(p,0,i,v),p},ev=function(t,e,r,i){var n={display:"block"},s=r[i?"os2":"p2"],o=r[i?"p2":"os2"];t._isFlipped=i,n[r.a+"Percent"]=i?-100:0,n[r.a]=i?"1px":0,n["border"+s+t7]=1,n["border"+o+t7]=0,n[r.p]=e+"px",U.set(t,n)},ey=[],ex={},eb=function(){return tE()-tV>34&&(tA||(tA=requestAnimationFrame(eB)))},ew=function(){tc&&tc.isPressed&&!(tc.startX>q.clientWidth)||(x.cache++,tc?tA||(tA=requestAnimationFrame(eB)):eB(),tV||eS("scrollStart"),tV=tE())},ek=function(){tf=X.innerWidth,tp=X.innerHeight},eA=function(t){x.cache++,(!0===t||!te&&!th&&!$.fullscreenElement&&!$.webkitFullscreenElement&&(!td||tf!==X.innerWidth||Math.abs(X.innerHeight-tp)>.25*X.innerHeight))&&K.restart(!0)},eT={},eM=[],eP=function t(){return eh(e1,"scrollEnd",t)||eL(!0)},eS=function(t){return eT[t]&&eT[t].map(function(t){return t()})||eM},eE=[],eC=function(t){for(var e=0;e<eE.length;e+=5)(!t||eE[e+4]&&eE[e+4].query===t)&&(eE[e].style.cssText=eE[e+1],eE[e].getBBox&&eE[e].setAttribute("transform",eE[e+2]||""),eE[e+3].uncache=1)},eV=function(t,e){var r;for(tn=0;tn<ey.length;tn++)(r=ey[tn])&&(!e||r._ctx===e)&&(t?r.kill(1):r.revert(!0,!0));tb=!0,e&&eC(e),e||eS("revert")},eD=function(t,e){x.cache++,(e||!tT)&&x.forEach(function(t){return tH(t)&&t.cacheID++&&(t.rec=0)}),t$(t)&&(X.history.scrollRestoration=tv=t)},eO=0,eR=function(){if(tM!==eO){var t=tM=eO;requestAnimationFrame(function(){return t===eO&&eL(!0)})}},e_=function(){q.appendChild(ty),tx=!tc&&ty.offsetHeight||X.innerHeight,q.removeChild(ty)},ej=function(t){return Z(".gsap-marker-start, .gsap-marker-end, .gsap-marker-scroller-start, .gsap-marker-scroller-end").forEach(function(e){return e.style.display=t?"none":"block"})},eL=function(t,e){if(H=$.documentElement,q=$.body,G=[X,$,H,q],tV&&!t&&!tb)return void eu(e1,"scrollEnd",eP);e_(),tT=e1.isRefreshing=!0,x.forEach(function(t){return tH(t)&&++t.cacheID&&(t.rec=t())});var r=eS("refreshInit");tl&&e1.sort(),e||eV(),x.forEach(function(t){tH(t)&&(t.smooth&&(t.target.style.scrollBehavior="auto"),t(0))}),ey.slice(0).forEach(function(t){return t.refresh()}),tb=!1,ey.forEach(function(t){if(t._subPinOffset&&t.pin){var e=t.vars.horizontal?"offsetWidth":"offsetHeight",r=t.pin[e];t.revert(!0,1),t.adjustPinSpacing(t.pin[e]-r),t.refresh()}}),tw=1,ej(!0),ey.forEach(function(t){var e=tW(t.scroller,t._dir),r="max"===t.vars.end||t._endClamp&&t.end>e,i=t._startClamp&&t.start>=e;(r||i)&&t.setPositions(i?e-1:t.start,r?Math.max(i?e:t.start+1,e):t.end,!0)}),ej(!1),tw=0,r.forEach(function(t){return t&&t.render&&t.render(-1)}),x.forEach(function(t){tH(t)&&(t.smooth&&requestAnimationFrame(function(){return t.target.style.scrollBehavior="smooth"}),t.rec&&t(t.rec))}),eD(tv,1),K.pause(),eO++,tT=2,eB(2),ey.forEach(function(t){return tH(t.vars.onRefresh)&&t.vars.onRefresh(t)}),tT=e1.isRefreshing=!1,eS("refresh")},ez=0,eF=1,eB=function(t){if(2===t||!tT&&!tb){e1.isUpdating=!0,tP&&tP.update(0);var e=ey.length,r=tE(),i=r-tC>=50,n=e&&ey[0].scroll();if(eF=ez>n?-1:1,tT||(ez=n),i&&(tV&&!tr&&r-tV>200&&(tV=0,eS("scrollEnd")),J=tC,tC=r),eF<0){for(tn=e;tn-- >0;)ey[tn]&&ey[tn].update(0,i);eF=1}else for(tn=0;tn<e;tn++)ey[tn]&&ey[tn].update(0,i);e1.isUpdating=!1}tA=0},eI=[tJ,"top",t1,t0,t8+t6,t8+t3,t8+"Top",t8+t4,"display","flexShrink","float","zIndex","gridColumnStart","gridColumnEnd","gridRowStart","gridRowEnd","gridArea","justifySelf","alignSelf","placeSelf","order"],eN=eI.concat([t2,t5,"boxSizing","max"+t7,"max"+et,"position",t8,t9,t9+"Top",t9+t3,t9+t6,t9+t4]),eY=function(t,e,r){eX(r);var i=t._gsap;if(i.spacerIsNative)eX(i.spacerState);else if(t._gsap.swappedIn){var n=e.parentNode;n&&(n.insertBefore(t,e),n.removeChild(e))}t._gsap.swappedIn=!1},eU=function(t,e,r,i){if(!t._gsap.swappedIn){for(var n,s=eI.length,o=e.style,a=t.style;s--;)o[n=eI[s]]=r[n];o.position="absolute"===r.position?"absolute":"relative","inline"===r.display&&(o.display="inline-block"),a[t1]=a[t0]="auto",o.flexBasis=r.flexBasis||"auto",o.overflow="visible",o.boxSizing="border-box",o[t2]=es(t,O)+"px",o[t5]=es(t,R)+"px",o[t9]=a[t8]=a.top=a[tJ]="0",eX(i),a[t2]=a["max"+t7]=r[t2],a[t5]=a["max"+et]=r[t5],a[t9]=r[t9],t.parentNode!==e&&(t.parentNode.insertBefore(e,t),e.appendChild(t)),t._gsap.swappedIn=!0}},eW=/([A-Z])/g,eX=function(t){if(t){var e,r,i=t.t.style,n=t.length,s=0;for((t.t._gsap||U.core.getCache(t.t)).uncache=1;s<n;s+=2)r=t[s+1],e=t[s],r?i[e]=r:i[e]&&i.removeProperty(e.replace(eW,"-$1").toLowerCase())}},e$=function(t){for(var e=eN.length,r=t.style,i=[],n=0;n<e;n++)i.push(eN[n],r[eN[n]]);return i.t=t,i},eH=function(t,e,r){for(var i,n=[],s=t.length,o=8*!!r;o<s;o+=2)i=t[o],n.push(i,i in e?e[i]:t[o+1]);return n.t=t.t,n},eq={left:0,top:0},eG=function(t,e,r,i,n,s,o,a,l,u,h,c,d,p){tH(t)&&(t=t(a)),t$(t)&&"max"===t.substr(0,3)&&(t=c+("="===t.charAt(4)?em("0"+t.substr(3),r):0));var f,m,g,v=d?d.time():0;if(d&&d.seek(0),isNaN(t)||(t*=1),tq(t))d&&(t=U.utils.mapRange(d.scrollTrigger.start,d.scrollTrigger.end,0,c,t)),o&&ev(o,r,i,!0);else{tH(e)&&(e=e(a));var y,x,b,w,k=(t||"0").split(" ");(y=en(g=_(e,a)||q)||{}).left||y.top||"none"!==ee(g).display||(w=g.style.display,g.style.display="block",y=en(g),w?g.style.display=w:g.style.removeProperty("display")),x=em(k[0],y[i.d]),b=em(k[1]||"0",r),t=y[i.p]-l[i.p]-u+x+n-b,o&&ev(o,b,i,r-b<20||o._isStart&&b>20),r-=r-b}if(p&&(a[p]=t||-.001,t<0&&(t=0)),s){var A=t+r,T=s._isStart;f="scroll"+i.d2,ev(s,A,i,T&&A>20||!T&&(h?Math.max(q[f],H[f]):s.parentNode[f])<=A+1),h&&(l=en(o),h&&(s.style[i.op.p]=l[i.op.p]-i.op.m-s._offset+"px"))}return d&&g&&(f=en(g),d.seek(c),m=en(g),d._caScrollDist=f[i.p]-m[i.p],t=t/d._caScrollDist*c),d&&d.seek(v),d?t:Math.round(t)},eK=/(webkit|moz|length|cssText|inset)/i,eZ=function(t,e,r,i){if(t.parentNode!==e){var n,s,o=t.style;if(e===q){for(n in t._stOrig=o.cssText,s=ee(t))+n||eK.test(n)||!s[n]||"string"!=typeof o[n]||"0"===n||(o[n]=s[n]);o.top=r,o.left=i}else o.cssText=t._stOrig;U.core.getCache(t).uncache=1,e.appendChild(t)}},eQ=function(t,e,r){var i=e,n=i;return function(e){var s=Math.round(t());return s!==i&&s!==n&&Math.abs(s-i)>3&&Math.abs(s-n)>3&&(e=s,r&&r()),n=i,i=Math.round(e)}},eJ=function(t,e,r){var i={};i[e.p]="+="+r,U.set(t,i)},e0=function(t,e){var r=L(t,e),i="_scroll"+e.p2,n=function e(n,s,o,a,l){var u=e.tween,h=s.onComplete,c={};o=o||r();var d=eQ(r,o,function(){u.kill(),e.tween=0});return l=a&&l||0,a=a||n-o,u&&u.kill(),s[i]=n,s.inherit=!1,s.modifiers=c,c[i]=function(){return d(o+a*u.ratio+l*u.ratio*u.ratio)},s.onUpdate=function(){x.cache++,e.tween&&eB()},s.onComplete=function(){e.tween=0,h&&h.call(u)},u=e.tween=U.to(t,s)};return t[i]=r,r.wheelHandler=function(){return n.tween&&n.tween.kill()&&(n.tween=0)},eu(t,"wheel",r.wheelHandler),e1.isTouch&&eu(t,"touchmove",r.wheelHandler),n},e1=function(){function t(e,r){W||t.register(U)||console.warn("Please gsap.registerPlugin(ScrollTrigger)"),tg(this),this.init(e,r)}return t.prototype.init=function(e,r){if(this.progress=this.start=0,this.vars&&this.kill(!0,!0),!tD){this.update=this.refresh=this.kill=tL;return}var i,n,s,o,a,l,u,h,c,d,p,f,m,g,v,y,w,k,A,M,P,S,E,C,V,D,j,z,F,B,I,N,Y,W,G,K,tt,ti,ts,to,ta,th=e=ei(t$(e)||tq(e)||e.nodeType?{trigger:e}:e,ep),tc=th.onUpdate,td=th.toggleClass,tp=th.id,tf=th.onToggle,tm=th.onRefresh,tg=th.scrub,tv=th.trigger,ty=th.pin,tx=th.pinSpacing,tb=th.invalidateOnRefresh,tA=th.anticipatePin,tM=th.onScrubComplete,tC=th.onSnapComplete,t_=th.once,tj=th.snap,tF=th.pinReparent,tB=th.pinSpacer,tN=th.containerAnimation,tX=th.fastScrollEnd,tJ=th.preventOverlaps,t0=e.horizontal||e.containerAnimation&&!1!==e.horizontal?O:R,t1=!tg&&0!==tg,el=_(e.scroller||X),ec=U.core.getCache(el),ef=tI(el),ev=("pinType"in e?e.pinType:T(el,"pinType")||ef&&"fixed")==="fixed",eb=[e.onEnter,e.onLeave,e.onEnterBack,e.onLeaveBack],ek=t1&&e.toggleActions.split(" "),eT="markers"in e?e.markers:ep.markers,eM=ef?0:parseFloat(ee(el)["border"+t0.p2+t7])||0,eS=this,eE=e.onRefreshInit&&function(){return e.onRefreshInit(eS)},eC=tU(el,ef,t0),eV=!ef||~b.indexOf(el)?tY(el):function(){return eq},eD=0,eO=0,e_=0,ej=L(el,t0);if(eS._startClamp=eS._endClamp=!1,eS._dir=t0,tA*=45,eS.scroller=el,eS.scroll=tN?tN.time.bind(tN):ej,l=ej(),eS.vars=e,r=r||e.animation,"refreshPriority"in e&&(tl=1,-9999===e.refreshPriority&&(tP=eS)),ec.tweenScroll=ec.tweenScroll||{top:e0(el,R),left:e0(el,O)},eS.tweenTo=s=ec.tweenScroll[t0.p],eS.scrubDuration=function(t){(G=tq(t)&&t)?W?W.duration(t):W=U.to(r,{ease:"expo",totalProgress:"+=0",inherit:!1,duration:G,paused:!0,onComplete:function(){return tM&&tM(eS)}}):(W&&W.progress(1).kill(),W=0)},r&&(r.vars.lazy=!1,r._initted&&!eS.isReverted||!1!==r.vars.immediateRender&&!1!==e.immediateRender&&r.duration()&&r.render(0,!0,!0),eS.animation=r.pause(),r.scrollTrigger=eS,eS.scrubDuration(tg),N=0,tp||(tp=r.vars.id)),tj&&((!tG(tj)||tj.push)&&(tj={snapTo:tj}),"scrollBehavior"in q.style&&U.set(ef?[q,H]:el,{scrollBehavior:"auto"}),x.forEach(function(t){return tH(t)&&t.target===(ef?$.scrollingElement||H:el)&&(t.smooth=!1)}),a=tH(tj.snapTo)?tj.snapTo:"labels"===tj.snapTo?(i=r,function(t){return U.utils.snap(eo(i),t)}):"labelsDirectional"===tj.snapTo?(n=r,function(t,e){return ea(eo(n))(t,e.direction)}):!1!==tj.directional?function(t,e){return ea(tj.snapTo)(t,tE()-eO<500?0:e.direction)}:U.utils.snap(tj.snapTo),K=tG(K=tj.duration||{min:.1,max:2})?Q(K.min,K.max):Q(K,K),tt=U.delayedCall(tj.delay||G/2||.1,function(){var t=ej(),e=tE()-eO<500,i=s.tween;if((e||10>Math.abs(eS.getVelocity()))&&!i&&!tr&&eD!==t){var n,o,l=(t-h)/y,u=r&&!t1?r.totalProgress():l,d=e?0:(u-Y)/(tE()-J)*1e3||0,p=U.utils.clamp(-l,1-l,tQ(d/2)*d/.185),f=l+(!1===tj.inertia?0:p),m=tj,g=m.onStart,v=m.onInterrupt,x=m.onComplete;if(tq(n=a(f,eS))||(n=f),o=Math.max(0,Math.round(h+n*y)),t<=c&&t>=h&&o!==t){if(i&&!i._initted&&i.data<=tQ(o-t))return;!1===tj.inertia&&(p=n-l),s(o,{duration:K(tQ(.185*Math.max(tQ(f-u),tQ(n-u))/d/.05||0)),ease:tj.ease||"power3",data:tQ(o-t),onInterrupt:function(){return tt.restart(!0)&&v&&v(eS)},onComplete:function(){eS.update(),eD=ej(),r&&!t1&&(W?W.resetTo("totalProgress",n,r._tTime/r._tDur):r.progress(n)),N=Y=r&&!t1?r.totalProgress():eS.progress,tC&&tC(eS),x&&x(eS)}},t,p*y,o-t-p*y),g&&g(eS,s.tween)}}else eS.isActive&&eD!==t&&tt.restart(!0)}).pause()),tp&&(ex[tp]=eS),(ta=(tv=eS.trigger=_(tv||!0!==ty&&ty))&&tv._gsap&&tv._gsap.stRevert)&&(ta=ta(eS)),ty=!0===ty?tv:_(ty),t$(td)&&(td={targets:tv,className:td}),ty&&(!1===tx||tx===t8||(tx=(!!tx||!ty.parentNode||!ty.parentNode.style||"flex"!==ee(ty.parentNode).display)&&t9),eS.pin=ty,(o=U.core.getCache(ty)).spacer?w=o.pinState:(tB&&((tB=_(tB))&&!tB.nodeType&&(tB=tB.current||tB.nativeElement),o.spacerIsNative=!!tB,tB&&(o.spacerState=e$(tB))),o.spacer=M=tB||$.createElement("div"),M.classList.add("pin-spacer"),tp&&M.classList.add("pin-spacer-"+tp),o.pinState=w=e$(ty)),!1!==e.force3D&&U.set(ty,{force3D:!0}),eS.spacer=M=o.spacer,D=(I=ee(ty))[tx+t0.os2],S=U.getProperty(ty),E=U.quickSetter(ty,t0.a,"px"),eU(ty,M,I),A=e$(ty)),eT){g=tG(eT)?ei(eT,ed):ed,f=eg("scroller-start",tp,el,t0,g,0),m=eg("scroller-end",tp,el,t0,g,0,f),P=f["offset"+t0.op.d2];var eL=_(T(el,"content")||el);d=this.markerStart=eg("start",tp,eL,t0,g,P,0,tN),p=this.markerEnd=eg("end",tp,eL,t0,g,P,0,tN),tN&&(to=U.quickSetter([d,p],t0.a,"px")),ev||b.length&&!0===T(el,"fixedMarkers")||(er(ef?q:el),U.set([f,m],{force3D:!0}),z=U.quickSetter(f,t0.a,"px"),B=U.quickSetter(m,t0.a,"px"))}if(tN){var ez=tN.vars.onUpdate,eB=tN.vars.onUpdateParams;tN.eventCallback("onUpdate",function(){eS.update(0,0,1),ez&&ez.apply(tN,eB||[])})}if(eS.previous=function(){return ey[ey.indexOf(eS)-1]},eS.next=function(){return ey[ey.indexOf(eS)+1]},eS.revert=function(t,e){if(!e)return eS.kill(!0);var i=!1!==t||!eS.enabled,n=te;i!==eS.isReverted&&(i&&(ti=Math.max(ej(),eS.scroll.rec||0),e_=eS.progress,ts=r&&r.progress()),d&&[d,p,f,m].forEach(function(t){return t.style.display=i?"none":"block"}),i&&(te=eS,eS.update(i)),!ty||tF&&eS.isActive||(i?eY(ty,M,w):eU(ty,M,ee(ty),j)),i||eS.update(i),te=n,eS.isReverted=i)},eS.refresh=function(i,n,o,a){if(!te&&eS.enabled||n){if(ty&&i&&tV)return void eu(t,"scrollEnd",eP);!tT&&eE&&eE(eS),te=eS,s.tween&&!o&&(s.tween.kill(),s.tween=0),W&&W.pause(),tb&&r&&(r.revert({kill:!1}).invalidate(),r.getChildren&&r.getChildren(!0,!0,!1).forEach(function(t){return t.vars.immediateRender&&t.render(0,!0,!0)})),eS.isReverted||eS.revert(!0,!0),eS._subPinOffset=!1;var g,x,b,T,P,E,D,z,B,I,N,Y,X,G=eC(),K=eV(),Z=tN?tN.duration():tW(el,t0),Q=y<=.01||!y,J=0,tr=a||0,tn=tG(o)?o.end:e.end,to=e.endTrigger||tv,ta=tG(o)?o.start:e.start||(0!==e.start&&tv?ty?"0 0":"0 100%":0),tl=eS.pinnedContainer=e.pinnedContainer&&_(e.pinnedContainer,eS),th=tv&&Math.max(0,ey.indexOf(eS))||0,tc=th;for(eT&&tG(o)&&(Y=U.getProperty(f,t0.p),X=U.getProperty(m,t0.p));tc-- >0;)(E=ey[tc]).end||E.refresh(0,1)||(te=eS),(D=E.pin)&&(D===tv||D===ty||D===tl)&&!E.isReverted&&(I||(I=[]),I.unshift(E),E.revert(!0,!0)),E!==ey[tc]&&(th--,tc--);for(tH(ta)&&(ta=ta(eS)),h=eG(ta=tO(ta,"start",eS),tv,G,t0,ej(),d,f,eS,K,eM,ev,Z,tN,eS._startClamp&&"_startClamp")||(ty?-.001:0),tH(tn)&&(tn=tn(eS)),t$(tn)&&!tn.indexOf("+=")&&(~tn.indexOf(" ")?tn=(t$(ta)?ta.split(" ")[0]:"")+tn:(J=em(tn.substr(2),G),tn=t$(ta)?ta:(tN?U.utils.mapRange(0,tN.duration(),tN.scrollTrigger.start,tN.scrollTrigger.end,h):h)+J,to=tv)),tn=tO(tn,"end",eS),c=Math.max(h,eG(tn||(to?"100% 0":Z),to,G,t0,ej()+J,p,m,eS,K,eM,ev,Z,tN,eS._endClamp&&"_endClamp"))||-.001,J=0,tc=th;tc--;)(D=(E=ey[tc]).pin)&&E.start-E._pinPush<=h&&!tN&&E.end>0&&(g=E.end-(eS._startClamp?Math.max(0,E.start):E.start),(D===tv&&E.start-E._pinPush<h||D===tl)&&isNaN(ta)&&(J+=g*(1-E.progress)),D===ty&&(tr+=g));if(h+=J,c+=J,eS._startClamp&&(eS._startClamp+=J),eS._endClamp&&!tT&&(eS._endClamp=c||-.001,c=Math.min(c,tW(el,t0))),y=c-h||(h-=.01)&&.001,Q&&(e_=U.utils.clamp(0,1,U.utils.normalize(h,c,ti))),eS._pinPush=tr,d&&J&&((g={})[t0.a]="+="+J,tl&&(g[t0.p]="-="+ej()),U.set([d,p],g)),ty&&!(tw&&eS.end>=tW(el,t0)))g=ee(ty),T=t0===R,b=ej(),C=parseFloat(S(t0.a))+tr,!Z&&c>1&&(N={style:N=(ef?$.scrollingElement||H:el).style,value:N["overflow"+t0.a.toUpperCase()]},ef&&"scroll"!==ee(q)["overflow"+t0.a.toUpperCase()]&&(N.style["overflow"+t0.a.toUpperCase()]="scroll")),eU(ty,M,g),A=e$(ty),x=en(ty,!0),z=ev&&L(el,T?O:R)(),tx?((j=[tx+t0.os2,y+tr+"px"]).t=M,(tc=tx===t9?es(ty,t0)+y+tr:0)&&(j.push(t0.d,tc+"px"),"auto"!==M.style.flexBasis&&(M.style.flexBasis=tc+"px")),eX(j),tl&&ey.forEach(function(t){t.pin===tl&&!1!==t.vars.pinSpacing&&(t._subPinOffset=!0)}),ev&&ej(ti)):(tc=es(ty,t0))&&"auto"!==M.style.flexBasis&&(M.style.flexBasis=tc+"px"),ev&&((P={top:x.top+(T?b-h:z)+"px",left:x.left+(T?z:b-h)+"px",boxSizing:"border-box",position:"fixed"})[t2]=P["max"+t7]=Math.ceil(x.width)+"px",P[t5]=P["max"+et]=Math.ceil(x.height)+"px",P[t8]=P[t8+"Top"]=P[t8+t3]=P[t8+t6]=P[t8+t4]="0",P[t9]=g[t9],P[t9+"Top"]=g[t9+"Top"],P[t9+t3]=g[t9+t3],P[t9+t6]=g[t9+t6],P[t9+t4]=g[t9+t4],k=eH(w,P,tF),tT&&ej(0)),r?(B=r._initted,tu(1),r.render(r.duration(),!0,!0),V=S(t0.a)-C+y+tr,F=Math.abs(y-V)>1,ev&&F&&k.splice(k.length-2,2),r.render(0,!0,!0),B||r.invalidate(!0),r.parent||r.totalTime(r.totalTime()),tu(0)):V=y,N&&(N.value?N.style["overflow"+t0.a.toUpperCase()]=N.value:N.style.removeProperty("overflow-"+t0.a));else if(tv&&ej()&&!tN)for(x=tv.parentNode;x&&x!==q;)x._pinOffset&&(h-=x._pinOffset,c-=x._pinOffset),x=x.parentNode;I&&I.forEach(function(t){return t.revert(!1,!0)}),eS.start=h,eS.end=c,l=u=tT?ti:ej(),tN||tT||(l<ti&&ej(ti),eS.scroll.rec=0),eS.revert(!1,!0),eO=tE(),tt&&(eD=-1,tt.restart(!0)),te=0,r&&t1&&(r._initted||ts)&&r.progress()!==ts&&r.progress(ts||0,!0).render(r.time(),!0,!0),(Q||e_!==eS.progress||tN||tb||r&&!r._initted)&&(r&&!t1&&(r._initted||e_||!1!==r.vars.immediateRender)&&r.totalProgress(tN&&h<-.001&&!e_?U.utils.normalize(h,c,0):e_,!0),eS.progress=Q||(l-h)/y===e_?0:e_),ty&&tx&&(M._pinOffset=Math.round(eS.progress*V)),W&&W.invalidate(),isNaN(Y)||(Y-=U.getProperty(f,t0.p),X-=U.getProperty(m,t0.p),eJ(f,t0,Y),eJ(d,t0,Y-(a||0)),eJ(m,t0,X),eJ(p,t0,X-(a||0))),Q&&!tT&&eS.update(),!tm||tT||v||(v=!0,tm(eS),v=!1)}},eS.getVelocity=function(){return(ej()-u)/(tE()-J)*1e3||0},eS.endAnimation=function(){tK(eS.callbackAnimation),r&&(W?W.progress(1):r.paused()?t1||tK(r,eS.direction<0,1):tK(r,r.reversed()))},eS.labelToScroll=function(t){return r&&r.labels&&(h||eS.refresh()||h)+r.labels[t]/r.duration()*y||0},eS.getTrailing=function(t){var e=ey.indexOf(eS),r=eS.direction>0?ey.slice(0,e).reverse():ey.slice(e+1);return(t$(t)?r.filter(function(e){return e.vars.preventOverlaps===t}):r).filter(function(t){return eS.direction>0?t.end<=h:t.start>=c})},eS.update=function(t,e,i){if(!tN||i||t){var n,o,a,d,p,m,g,v=!0===tT?ti:eS.scroll(),x=t?0:(v-h)/y,b=x<0?0:x>1?1:x||0,w=eS.progress;if(e&&(u=l,l=tN?ej():v,tj&&(Y=N,N=r&&!t1?r.totalProgress():b)),tA&&ty&&!te&&!tS&&tV&&(!b&&h<v+(v-u)/(tE()-J)*tA?b=1e-4:1===b&&c>v+(v-u)/(tE()-J)*tA&&(b=.9999)),b!==w&&eS.enabled){if(d=(p=(n=eS.isActive=!!b&&b<1)!=(!!w&&w<1))||!!b!=!!w,eS.direction=b>w?1:-1,eS.progress=b,d&&!te&&(o=b&&!w?0:1===b?1:1===w?2:3,t1&&(a=!p&&"none"!==ek[o+1]&&ek[o+1]||ek[o],g=r&&("complete"===a||"reset"===a||a in r))),tJ&&(p||g)&&(g||tg||!r)&&(tH(tJ)?tJ(eS):eS.getTrailing(tJ).forEach(function(t){return t.endAnimation()})),!t1&&(!W||te||tS?r&&r.totalProgress(b,!!(te&&(eO||t))):(W._dp._time-W._start!==W._time&&W.render(W._dp._time-W._start),W.resetTo?W.resetTo("totalProgress",b,r._tTime/r._tDur):(W.vars.totalProgress=b,W.invalidate().restart()))),ty)if(t&&tx&&(M.style[tx+t0.os2]=D),ev){if(d){if(m=!t&&b>w&&c+1>v&&v+1>=tW(el,t0),tF)if(!t&&(n||m)){var T=en(ty,!0),P=v-h;eZ(ty,q,T.top+(t0===R?P:0)+"px",T.left+(t0===R?0:P)+"px")}else eZ(ty,M);eX(n||m?k:A),F&&b<1&&n||E(C+(1!==b||m?0:V))}}else E(tz(C+V*b));!tj||s.tween||te||tS||tt.restart(!0),td&&(p||t_&&b&&(b<1||!tk))&&Z(td.targets).forEach(function(t){return t.classList[n||t_?"add":"remove"](td.className)}),!tc||t1||t||tc(eS),d&&!te?(t1&&(g&&("complete"===a?r.pause().totalProgress(1):"reset"===a?r.restart(!0).pause():"restart"===a?r.restart(!0):r[a]()),tc&&tc(eS)),(p||!tk)&&(tf&&p&&tZ(eS,tf),eb[o]&&tZ(eS,eb[o]),t_&&(1===b?eS.kill(!1,1):eb[o]=0),!p&&eb[o=1===b?1:3]&&tZ(eS,eb[o])),tX&&!n&&Math.abs(eS.getVelocity())>(tq(tX)?tX:2500)&&(tK(eS.callbackAnimation),W?W.progress(1):tK(r,"reverse"===a?1:!b,1))):t1&&tc&&!te&&tc(eS)}if(B){var S=tN?v/tN.duration()*(tN._caScrollDist||0):v;z(S+ +!!f._isFlipped),B(S)}to&&to(-v/tN.duration()*(tN._caScrollDist||0))}},eS.enable=function(e,r){eS.enabled||(eS.enabled=!0,eu(el,"resize",eA),ef||eu(el,"scroll",ew),eE&&eu(t,"refreshInit",eE),!1!==e&&(eS.progress=e_=0,l=u=eD=ej()),!1!==r&&eS.refresh())},eS.getTween=function(t){return t&&s?s.tween:W},eS.setPositions=function(t,e,r,i){if(tN){var n=tN.scrollTrigger,s=tN.duration(),o=n.end-n.start;t=n.start+o*t/s,e=n.start+o*e/s}eS.refresh(!1,!1,{start:tR(t,r&&!!eS._startClamp),end:tR(e,r&&!!eS._endClamp)},i),eS.update()},eS.adjustPinSpacing=function(t){if(j&&t){var e=j.indexOf(t0.d)+1;j[e]=parseFloat(j[e])+t+"px",j[1]=parseFloat(j[1])+t+"px",eX(j)}},eS.disable=function(e,r){if(eS.enabled&&(!1!==e&&eS.revert(!0,!0),eS.enabled=eS.isActive=!1,r||W&&W.pause(),ti=0,o&&(o.uncache=1),eE&&eh(t,"refreshInit",eE),tt&&(tt.pause(),s.tween&&s.tween.kill()&&(s.tween=0)),!ef)){for(var i=ey.length;i--;)if(ey[i].scroller===el&&ey[i]!==eS)return;eh(el,"resize",eA),ef||eh(el,"scroll",ew)}},eS.kill=function(t,i){eS.disable(t,i),W&&!i&&W.kill(),tp&&delete ex[tp];var n=ey.indexOf(eS);n>=0&&ey.splice(n,1),n===tn&&eF>0&&tn--,n=0,ey.forEach(function(t){return t.scroller===eS.scroller&&(n=1)}),n||tT||(eS.scroll.rec=0),r&&(r.scrollTrigger=null,t&&r.revert({kill:!1}),i||r.kill()),d&&[d,p,f,m].forEach(function(t){return t.parentNode&&t.parentNode.removeChild(t)}),tP===eS&&(tP=0),ty&&(o&&(o.uncache=1),n=0,ey.forEach(function(t){return t.pin===ty&&n++}),n||(o.spacer=0)),e.onKill&&e.onKill(eS)},ey.push(eS),eS.enable(!1,!1),ta&&ta(eS),r&&r.add&&!y){var eI=eS.update;eS.update=function(){eS.update=eI,x.cache++,h||c||eS.refresh()},U.delayedCall(.01,eS.update),y=.01,h=c=0}else eS.refresh();ty&&eR()},t.register=function(e){return W||(U=e||tB(),tF()&&window.document&&t.enable(),W=tD),W},t.defaults=function(t){if(t)for(var e in t)ep[e]=t[e];return ep},t.disable=function(t,e){tD=0,ey.forEach(function(r){return r[e?"kill":"disable"](t)}),eh(X,"wheel",ew),eh($,"scroll",ew),clearInterval(tt),eh($,"touchcancel",tL),eh(q,"touchstart",tL),el(eh,$,"pointerdown,touchstart,mousedown",t_),el(eh,$,"pointerup,touchend,mouseup",tj),K.kill(),tX(eh);for(var r=0;r<x.length;r+=3)ec(eh,x[r],x[r+1]),ec(eh,x[r],x[r+2])},t.enable=function(){if(X=window,H=($=document).documentElement,q=$.body,U&&(Z=U.utils.toArray,Q=U.utils.clamp,tg=U.core.context||tL,tu=U.core.suppressOverwrites||tL,tv=X.history.scrollRestoration||"auto",ez=X.pageYOffset||0,U.core.globals("ScrollTrigger",t),q)){tD=1,(ty=document.createElement("div")).style.height="100vh",ty.style.position="absolute",e_(),function t(){return tD&&requestAnimationFrame(t)}(),Y.register(U),t.isTouch=Y.isTouch,tm=Y.isTouch&&/(iPad|iPhone|iPod|Mac)/g.test(navigator.userAgent),td=1===Y.isTouch,eu(X,"wheel",ew),G=[X,$,H,q],U.matchMedia?(t.matchMedia=function(t){var e,r=U.matchMedia();for(e in t)r.add(e,t[e]);return r},U.addEventListener("matchMediaInit",function(){return eV()}),U.addEventListener("matchMediaRevert",function(){return eC()}),U.addEventListener("matchMedia",function(){eL(0,1),eS("matchMedia")}),U.matchMedia().add("(orientation: portrait)",function(){return ek(),ek})):console.warn("Requires GSAP 3.11.0 or later"),ek(),eu($,"scroll",ew);var e,r,i=q.hasAttribute("style"),n=q.style,s=n.borderTopStyle,o=U.core.Animation.prototype;for(o.revert||Object.defineProperty(o,"revert",{value:function(){return this.time(-.01,!0)}}),n.borderTopStyle="solid",R.m=Math.round((e=en(q)).top+R.sc())||0,O.m=Math.round(e.left+O.sc())||0,s?n.borderTopStyle=s:n.removeProperty("border-top-style"),i||(q.setAttribute("style",""),q.removeAttribute("style")),tt=setInterval(eb,250),U.delayedCall(.5,function(){return tS=0}),eu($,"touchcancel",tL),eu(q,"touchstart",tL),el(eu,$,"pointerdown,touchstart,mousedown",t_),el(eu,$,"pointerup,touchend,mouseup",tj),ti=U.utils.checkPrefix("transform"),eN.push(ti),W=tE(),K=U.delayedCall(.2,eL).pause(),ta=[$,"visibilitychange",function(){var t=X.innerWidth,e=X.innerHeight;$.hidden?(ts=t,to=e):(ts!==t||to!==e)&&eA()},$,"DOMContentLoaded",eL,X,"load",eL,X,"resize",eA],tX(eu),ey.forEach(function(t){return t.enable(0,1)}),r=0;r<x.length;r+=3)ec(eh,x[r],x[r+1]),ec(eh,x[r],x[r+2])}},t.config=function(e){"limitCallbacks"in e&&(tk=!!e.limitCallbacks);var r=e.syncInterval;r&&clearInterval(tt)||(tt=r)&&setInterval(eb,r),"ignoreMobileResize"in e&&(td=1===t.isTouch&&e.ignoreMobileResize),"autoRefreshEvents"in e&&(tX(eh)||tX(eu,e.autoRefreshEvents||"none"),th=-1===(e.autoRefreshEvents+"").indexOf("resize"))},t.scrollerProxy=function(t,e){var r=_(t),i=x.indexOf(r),n=tI(r);~i&&x.splice(i,n?6:2),e&&(n?b.unshift(X,e,q,e,H,e):b.unshift(r,e))},t.clearMatchMedia=function(t){ey.forEach(function(e){return e._ctx&&e._ctx.query===t&&e._ctx.kill(!0,!0)})},t.isInViewport=function(t,e,r){var i=(t$(t)?_(t):t).getBoundingClientRect(),n=i[r?t2:t5]*e||0;return r?i.right-n>0&&i.left+n<X.innerWidth:i.bottom-n>0&&i.top+n<X.innerHeight},t.positionInViewport=function(t,e,r){t$(t)&&(t=_(t));var i=t.getBoundingClientRect(),n=i[r?t2:t5],s=null==e?n/2:e in ef?ef[e]*n:~e.indexOf("%")?parseFloat(e)*n/100:parseFloat(e)||0;return r?(i.left+s)/X.innerWidth:(i.top+s)/X.innerHeight},t.killAll=function(t){if(ey.slice(0).forEach(function(t){return"ScrollSmoother"!==t.vars.id&&t.kill()}),!0!==t){var e=eT.killAll||[];eT={},e.forEach(function(t){return t()})}},t}();e1.version="3.13.0",e1.saveStyles=function(t){return t?Z(t).forEach(function(t){if(t&&t.style){var e=eE.indexOf(t);e>=0&&eE.splice(e,5),eE.push(t,t.style.cssText,t.getBBox&&t.getAttribute("transform"),U.core.getCache(t),tg())}}):eE},e1.revert=function(t,e){return eV(!t,e)},e1.create=function(t,e){return new e1(t,e)},e1.refresh=function(t){return t?eA(!0):(W||e1.register())&&eL(!0)},e1.update=function(t){return++x.cache&&eB(2*(!0===t))},e1.clearScrollMemory=eD,e1.maxScroll=function(t,e){return tW(t,e?O:R)},e1.getScrollFunc=function(t,e){return L(_(t),e?O:R)},e1.getById=function(t){return ex[t]},e1.getAll=function(){return ey.filter(function(t){return"ScrollSmoother"!==t.vars.id})},e1.isScrolling=function(){return!!tV},e1.snapDirectional=ea,e1.addEventListener=function(t,e){var r=eT[t]||(eT[t]=[]);~r.indexOf(e)||r.push(e)},e1.removeEventListener=function(t,e){var r=eT[t],i=r&&r.indexOf(e);i>=0&&r.splice(i,1)},e1.batch=function(t,e){var r,i=[],n={},s=e.interval||.016,o=e.batchMax||1e9,a=function(t,e){var r=[],i=[],n=U.delayedCall(s,function(){e(r,i),r=[],i=[]}).pause();return function(t){r.length||n.restart(!0),r.push(t.trigger),i.push(t),o<=r.length&&n.progress(1)}};for(r in e)n[r]="on"===r.substr(0,2)&&tH(e[r])&&"onRefreshInit"!==r?a(r,e[r]):e[r];return tH(o)&&(o=o(),eu(e1,"refresh",function(){return o=e.batchMax()})),Z(t).forEach(function(t){var e={};for(r in n)e[r]=n[r];e.trigger=t,i.push(e1.create(e))}),i};var e2,e5=function(t,e,r,i){return e>i?t(i):e<0&&t(0),r>i?(i-e)/(r-e):r<0?e/(e-r):1},e3=function t(e,r){!0===r?e.style.removeProperty("touch-action"):e.style.touchAction=!0===r?"auto":r?"pan-"+r+(Y.isTouch?" pinch-zoom":""):"none",e===H&&t(q,r)},e4={auto:1,scroll:1},e6=function(t){var e,r=t.event,i=t.target,n=t.axis,s=(r.changedTouches?r.changedTouches[0]:r).target,o=s._gsap||U.core.getCache(s),a=tE();if(!o._isScrollT||a-o._isScrollT>2e3){for(;s&&s!==q&&(s.scrollHeight<=s.clientHeight&&s.scrollWidth<=s.clientWidth||!(e4[(e=ee(s)).overflowY]||e4[e.overflowX]));)s=s.parentNode;o._isScroll=s&&s!==i&&!tI(s)&&(e4[(e=ee(s)).overflowY]||e4[e.overflowX]),o._isScrollT=a}(o._isScroll||"x"===n)&&(r.stopPropagation(),r._gsapAllow=!0)},e9=function(t,e,r,i){return Y.create({target:t,capture:!0,debounce:!1,lockAxis:!0,type:e,onWheel:i=i&&e6,onPress:i,onDrag:i,onScroll:i,onEnable:function(){return r&&eu($,Y.eventTypes[0],e7,!1,!0)},onDisable:function(){return eh($,Y.eventTypes[0],e7,!0)}})},e8=/(input|label|select|textarea)/i,e7=function(t){var e=e8.test(t.target.tagName);(e||e2)&&(t._gsapAllow=!0,e2=e)},rt=function(t){tG(t)||(t={}),t.preventDefault=t.isNormalizer=t.allowClicks=!0,t.type||(t.type="wheel,touch"),t.debounce=!!t.debounce,t.id=t.id||"normalizer";var e,r,i,n,s,o,a,l,u=t,h=u.normalizeScrollX,c=u.momentum,d=u.allowNestedScroll,p=u.onRelease,f=_(t.target)||H,m=U.core.globals().ScrollSmoother,g=m&&m.get(),v=tm&&(t.content&&_(t.content)||g&&!1!==t.content&&!g.smooth()&&g.content()),y=L(f,R),b=L(f,O),w=1,k=(Y.isTouch&&X.visualViewport?X.visualViewport.scale*X.visualViewport.width:X.outerWidth)/X.innerWidth,A=0,T=tH(c)?function(){return c(e)}:function(){return c||2.8},M=e9(f,t.type,!0,d),P=function(){return n=!1},S=tL,E=tL,C=function(){r=tW(f,R),E=Q(+!!tm,r),h&&(S=Q(0,tW(f,O))),i=eO},V=function(){v._gsap.y=tz(parseFloat(v._gsap.y)+y.offset)+"px",v.style.transform="matrix3d(1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, "+parseFloat(v._gsap.y)+", 0, 1)",y.offset=y.cacheID=0},D=function(){if(n){requestAnimationFrame(P);var t=tz(e.deltaY/2),r=E(y.v-t);if(v&&r!==y.v+y.offset){y.offset=r-y.v;var i=tz((parseFloat(v&&v._gsap.y)||0)-y.offset);v.style.transform="matrix3d(1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, "+i+", 0, 1)",v._gsap.y=i+"px",y.cacheID=x.cache,eB()}return!0}y.offset&&V(),n=!0},j=function(){C(),s.isActive()&&s.vars.scrollY>r&&(y()>r?s.progress(1)&&y(r):s.resetTo("scrollY",r))};return v&&U.set(v,{y:"+=0"}),t.ignoreCheck=function(t){return tm&&"touchmove"===t.type&&D(t)||w>1.05&&"touchstart"!==t.type||e.isGesturing||t.touches&&t.touches.length>1},t.onPress=function(){n=!1;var t=w;w=tz((X.visualViewport&&X.visualViewport.scale||1)/k),s.pause(),t!==w&&e3(f,w>1.01||!h&&"x"),o=b(),a=y(),C(),i=eO},t.onRelease=t.onGestureStart=function(t,e){if(y.offset&&V(),e){x.cache++;var i,n,o=T();h&&(n=(i=b())+-(.05*o*t.velocityX)/.227,o*=e5(b,i,n,tW(f,O)),s.vars.scrollX=S(n)),n=(i=y())+-(.05*o*t.velocityY)/.227,o*=e5(y,i,n,tW(f,R)),s.vars.scrollY=E(n),s.invalidate().duration(o).play(.01),(tm&&s.vars.scrollY>=r||i>=r-1)&&U.to({},{onUpdate:j,duration:o})}else l.restart(!0);p&&p(t)},t.onWheel=function(){s._ts&&s.pause(),tE()-A>1e3&&(i=0,A=tE())},t.onChange=function(t,e,r,n,s){if(eO!==i&&C(),e&&h&&b(S(n[2]===e?o+(t.startX-t.x):b()+e-n[1])),r){y.offset&&V();var l=s[2]===r,u=l?a+t.startY-t.y:y()+r-s[1],c=E(u);l&&u!==c&&(a+=c-u),y(c)}(r||e)&&eB()},t.onEnable=function(){e3(f,!h&&"x"),e1.addEventListener("refresh",j),eu(X,"resize",j),y.smooth&&(y.target.style.scrollBehavior="auto",y.smooth=b.smooth=!1),M.enable()},t.onDisable=function(){e3(f,!0),eh(X,"resize",j),e1.removeEventListener("refresh",j),M.kill()},t.lockAxis=!1!==t.lockAxis,(e=new Y(t)).iOS=tm,tm&&!y()&&y(1),tm&&U.ticker.add(tL),l=e._dc,s=U.to(e,{ease:"power4",paused:!0,inherit:!1,scrollX:h?"+=0.1":"+=0",scrollY:"+=0.1",modifiers:{scrollY:eQ(y,y(),function(){return s.pause()})},onUpdate:eB,onComplete:l.vars.onComplete}),e};e1.sort=function(t){if(tH(t))return ey.sort(t);var e=X.pageYOffset||0;return e1.getAll().forEach(function(t){return t._sortY=t.trigger?e+t.trigger.getBoundingClientRect().top:t.start+X.innerHeight}),ey.sort(t||function(t,e){return -1e6*(t.vars.refreshPriority||0)+(t.vars.containerAnimation?1e6:t._sortY)-((e.vars.containerAnimation?1e6:e._sortY)+-1e6*(e.vars.refreshPriority||0))})},e1.observe=function(t){return new Y(t)},e1.normalizeScroll=function(t){if(void 0===t)return tc;if(!0===t&&tc)return tc.enable();if(!1===t){tc&&tc.kill(),tc=t;return}var e=t instanceof Y?t:rt(t);return tc&&tc.target===e.target&&tc.kill(),tI(e.target)&&(tc=e),e},e1.core={_getVelocityProp:z,_inputObserver:e9,_scrollers:x,_proxies:b,bridge:{ss:function(){tV||eS("scrollStart"),tV=tE()},ref:function(){return te}}},tB()&&U.registerPlugin(e1)},9099:(t,e,r)=>{r.d(e,{A:()=>i});let i=(0,r(9946).A)("github",[["path",{d:"M15 22v-4a4.8 4.8 0 0 0-1-3.5c3 0 6-2 6-5.5.08-1.25-.27-2.48-1-3.5.28-1.15.28-2.35 0-3.5 0 0-1 0-3 1.5-2.64-.5-5.36-.5-8 0C6 2 5 2 5 2c-.3 1.15-.3 2.35 0 3.5A5.403 5.403 0 0 0 4 9c0 3.5 3 5.5 6 5.5-.39.49-.68 1.05-.85 1.65-.17.6-.22 1.23-.15 1.85v4",key:"tonef"}],["path",{d:"M9 18c-4.51 2-5-2-7-2",key:"9comsn"}]])},9420:(t,e,r)=>{r.d(e,{A:()=>i});let i=(0,r(9946).A)("phone",[["path",{d:"M13.832 16.568a1 1 0 0 0 1.213-.303l.355-.465A2 2 0 0 1 17 15h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2A18 18 0 0 1 2 4a2 2 0 0 1 2-2h3a2 2 0 0 1 2 2v3a2 2 0 0 1-.8 1.6l-.468.351a1 1 0 0 0-.292 1.233 14 14 0 0 0 6.392 6.384",key:"9njp5v"}]])},9688:(t,e,r)=>{r.d(e,{QP:()=>tt});let i=(t,e)=>{if(0===t.length)return e.classGroupId;let r=t[0],n=e.nextPart.get(r),s=n?i(t.slice(1),n):void 0;if(s)return s;if(0===e.validators.length)return;let o=t.join("-");return e.validators.find(({validator:t})=>t(o))?.classGroupId},n=/^\[(.+)\]$/,s=(t,e,r,i)=>{t.forEach(t=>{if("string"==typeof t){(""===t?e:o(e,t)).classGroupId=r;return}if("function"==typeof t)return a(t)?void s(t(i),e,r,i):void e.validators.push({validator:t,classGroupId:r});Object.entries(t).forEach(([t,n])=>{s(n,o(e,t),r,i)})})},o=(t,e)=>{let r=t;return e.split("-").forEach(t=>{r.nextPart.has(t)||r.nextPart.set(t,{nextPart:new Map,validators:[]}),r=r.nextPart.get(t)}),r},a=t=>t.isThemeGetter,l=/\s+/;function u(){let t,e,r=0,i="";for(;r<arguments.length;)(t=arguments[r++])&&(e=h(t))&&(i&&(i+=" "),i+=e);return i}let h=t=>{let e;if("string"==typeof t)return t;let r="";for(let i=0;i<t.length;i++)t[i]&&(e=h(t[i]))&&(r&&(r+=" "),r+=e);return r},c=t=>{let e=e=>e[t]||[];return e.isThemeGetter=!0,e},d=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,p=/^\((?:(\w[\w-]*):)?(.+)\)$/i,f=/^\d+\/\d+$/,m=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,g=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,v=/^(rgba?|hsla?|hwb|(ok)?(lab|lch)|color-mix)\(.+\)$/,y=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,x=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,b=t=>f.test(t),w=t=>!!t&&!Number.isNaN(Number(t)),k=t=>!!t&&Number.isInteger(Number(t)),A=t=>t.endsWith("%")&&w(t.slice(0,-1)),T=t=>m.test(t),M=()=>!0,P=t=>g.test(t)&&!v.test(t),S=()=>!1,E=t=>y.test(t),C=t=>x.test(t),V=t=>!O(t)&&!F(t),D=t=>X(t,G,S),O=t=>d.test(t),R=t=>X(t,K,P),_=t=>X(t,Z,w),j=t=>X(t,H,S),L=t=>X(t,q,C),z=t=>X(t,J,E),F=t=>p.test(t),B=t=>$(t,K),I=t=>$(t,Q),N=t=>$(t,H),Y=t=>$(t,G),U=t=>$(t,q),W=t=>$(t,J,!0),X=(t,e,r)=>{let i=d.exec(t);return!!i&&(i[1]?e(i[1]):r(i[2]))},$=(t,e,r=!1)=>{let i=p.exec(t);return!!i&&(i[1]?e(i[1]):r)},H=t=>"position"===t||"percentage"===t,q=t=>"image"===t||"url"===t,G=t=>"length"===t||"size"===t||"bg-size"===t,K=t=>"length"===t,Z=t=>"number"===t,Q=t=>"family-name"===t,J=t=>"shadow"===t;Symbol.toStringTag;let tt=function(t,...e){let r,o,a,h=function(l){let u;return o=(r={cache:(t=>{if(t<1)return{get:()=>void 0,set:()=>{}};let e=0,r=new Map,i=new Map,n=(n,s)=>{r.set(n,s),++e>t&&(e=0,i=r,r=new Map)};return{get(t){let e=r.get(t);return void 0!==e?e:void 0!==(e=i.get(t))?(n(t,e),e):void 0},set(t,e){r.has(t)?r.set(t,e):n(t,e)}}})((u=e.reduce((t,e)=>e(t),t())).cacheSize),parseClassName:(t=>{let{prefix:e,experimentalParseClassName:r}=t,i=t=>{let e,r,i=[],n=0,s=0,o=0;for(let r=0;r<t.length;r++){let a=t[r];if(0===n&&0===s){if(":"===a){i.push(t.slice(o,r)),o=r+1;continue}if("/"===a){e=r;continue}}"["===a?n++:"]"===a?n--:"("===a?s++:")"===a&&s--}let a=0===i.length?t:t.substring(o),l=(r=a).endsWith("!")?r.substring(0,r.length-1):r.startsWith("!")?r.substring(1):r;return{modifiers:i,hasImportantModifier:l!==a,baseClassName:l,maybePostfixModifierPosition:e&&e>o?e-o:void 0}};if(e){let t=e+":",r=i;i=e=>e.startsWith(t)?r(e.substring(t.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:e,maybePostfixModifierPosition:void 0}}if(r){let t=i;i=e=>r({className:e,parseClassName:t})}return i})(u),sortModifiers:(t=>{let e=Object.fromEntries(t.orderSensitiveModifiers.map(t=>[t,!0]));return t=>{if(t.length<=1)return t;let r=[],i=[];return t.forEach(t=>{"["===t[0]||e[t]?(r.push(...i.sort(),t),i=[]):i.push(t)}),r.push(...i.sort()),r}})(u),...(t=>{let e=(t=>{let{theme:e,classGroups:r}=t,i={nextPart:new Map,validators:[]};for(let t in r)s(r[t],i,t,e);return i})(t),{conflictingClassGroups:r,conflictingClassGroupModifiers:o}=t;return{getClassGroupId:t=>{let r=t.split("-");return""===r[0]&&1!==r.length&&r.shift(),i(r,e)||(t=>{if(n.test(t)){let e=n.exec(t)[1],r=e?.substring(0,e.indexOf(":"));if(r)return"arbitrary.."+r}})(t)},getConflictingClassGroupIds:(t,e)=>{let i=r[t]||[];return e&&o[t]?[...i,...o[t]]:i}}})(u)}).cache.get,a=r.cache.set,h=c,c(l)};function c(t){let e=o(t);if(e)return e;let i=((t,e)=>{let{parseClassName:r,getClassGroupId:i,getConflictingClassGroupIds:n,sortModifiers:s}=e,o=[],a=t.trim().split(l),u="";for(let t=a.length-1;t>=0;t-=1){let e=a[t],{isExternal:l,modifiers:h,hasImportantModifier:c,baseClassName:d,maybePostfixModifierPosition:p}=r(e);if(l){u=e+(u.length>0?" "+u:u);continue}let f=!!p,m=i(f?d.substring(0,p):d);if(!m){if(!f||!(m=i(d))){u=e+(u.length>0?" "+u:u);continue}f=!1}let g=s(h).join(":"),v=c?g+"!":g,y=v+m;if(o.includes(y))continue;o.push(y);let x=n(m,f);for(let t=0;t<x.length;++t){let e=x[t];o.push(v+e)}u=e+(u.length>0?" "+u:u)}return u})(t,r);return a(t,i),i}return function(){return h(u.apply(null,arguments))}}(()=>{let t=c("color"),e=c("font"),r=c("text"),i=c("font-weight"),n=c("tracking"),s=c("leading"),o=c("breakpoint"),a=c("container"),l=c("spacing"),u=c("radius"),h=c("shadow"),d=c("inset-shadow"),p=c("text-shadow"),f=c("drop-shadow"),m=c("blur"),g=c("perspective"),v=c("aspect"),y=c("ease"),x=c("animate"),P=()=>["auto","avoid","all","avoid-page","page","left","right","column"],S=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],E=()=>[...S(),F,O],C=()=>["auto","hidden","clip","visible","scroll"],X=()=>["auto","contain","none"],$=()=>[F,O,l],H=()=>[b,"full","auto",...$()],q=()=>[k,"none","subgrid",F,O],G=()=>["auto",{span:["full",k,F,O]},k,F,O],K=()=>[k,"auto",F,O],Z=()=>["auto","min","max","fr",F,O],Q=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],J=()=>["start","end","center","stretch","center-safe","end-safe"],tt=()=>["auto",...$()],te=()=>[b,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...$()],tr=()=>[t,F,O],ti=()=>[...S(),N,j,{position:[F,O]}],tn=()=>["no-repeat",{repeat:["","x","y","space","round"]}],ts=()=>["auto","cover","contain",Y,D,{size:[F,O]}],to=()=>[A,B,R],ta=()=>["","none","full",u,F,O],tl=()=>["",w,B,R],tu=()=>["solid","dashed","dotted","double"],th=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],tc=()=>[w,A,N,j],td=()=>["","none",m,F,O],tp=()=>["none",w,F,O],tf=()=>["none",w,F,O],tm=()=>[w,F,O],tg=()=>[b,"full",...$()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[T],breakpoint:[T],color:[M],container:[T],"drop-shadow":[T],ease:["in","out","in-out"],font:[V],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[T],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[T],shadow:[T],spacing:["px",w],text:[T],"text-shadow":[T],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",b,O,F,v]}],container:["container"],columns:[{columns:[w,O,F,a]}],"break-after":[{"break-after":P()}],"break-before":[{"break-before":P()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:E()}],overflow:[{overflow:C()}],"overflow-x":[{"overflow-x":C()}],"overflow-y":[{"overflow-y":C()}],overscroll:[{overscroll:X()}],"overscroll-x":[{"overscroll-x":X()}],"overscroll-y":[{"overscroll-y":X()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:H()}],"inset-x":[{"inset-x":H()}],"inset-y":[{"inset-y":H()}],start:[{start:H()}],end:[{end:H()}],top:[{top:H()}],right:[{right:H()}],bottom:[{bottom:H()}],left:[{left:H()}],visibility:["visible","invisible","collapse"],z:[{z:[k,"auto",F,O]}],basis:[{basis:[b,"full","auto",a,...$()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[w,b,"auto","initial","none",O]}],grow:[{grow:["",w,F,O]}],shrink:[{shrink:["",w,F,O]}],order:[{order:[k,"first","last","none",F,O]}],"grid-cols":[{"grid-cols":q()}],"col-start-end":[{col:G()}],"col-start":[{"col-start":K()}],"col-end":[{"col-end":K()}],"grid-rows":[{"grid-rows":q()}],"row-start-end":[{row:G()}],"row-start":[{"row-start":K()}],"row-end":[{"row-end":K()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":Z()}],"auto-rows":[{"auto-rows":Z()}],gap:[{gap:$()}],"gap-x":[{"gap-x":$()}],"gap-y":[{"gap-y":$()}],"justify-content":[{justify:[...Q(),"normal"]}],"justify-items":[{"justify-items":[...J(),"normal"]}],"justify-self":[{"justify-self":["auto",...J()]}],"align-content":[{content:["normal",...Q()]}],"align-items":[{items:[...J(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...J(),{baseline:["","last"]}]}],"place-content":[{"place-content":Q()}],"place-items":[{"place-items":[...J(),"baseline"]}],"place-self":[{"place-self":["auto",...J()]}],p:[{p:$()}],px:[{px:$()}],py:[{py:$()}],ps:[{ps:$()}],pe:[{pe:$()}],pt:[{pt:$()}],pr:[{pr:$()}],pb:[{pb:$()}],pl:[{pl:$()}],m:[{m:tt()}],mx:[{mx:tt()}],my:[{my:tt()}],ms:[{ms:tt()}],me:[{me:tt()}],mt:[{mt:tt()}],mr:[{mr:tt()}],mb:[{mb:tt()}],ml:[{ml:tt()}],"space-x":[{"space-x":$()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":$()}],"space-y-reverse":["space-y-reverse"],size:[{size:te()}],w:[{w:[a,"screen",...te()]}],"min-w":[{"min-w":[a,"screen","none",...te()]}],"max-w":[{"max-w":[a,"screen","none","prose",{screen:[o]},...te()]}],h:[{h:["screen","lh",...te()]}],"min-h":[{"min-h":["screen","lh","none",...te()]}],"max-h":[{"max-h":["screen","lh",...te()]}],"font-size":[{text:["base",r,B,R]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[i,F,_]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",A,O]}],"font-family":[{font:[I,O,e]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[n,F,O]}],"line-clamp":[{"line-clamp":[w,"none",F,_]}],leading:[{leading:[s,...$()]}],"list-image":[{"list-image":["none",F,O]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",F,O]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:tr()}],"text-color":[{text:tr()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...tu(),"wavy"]}],"text-decoration-thickness":[{decoration:[w,"from-font","auto",F,R]}],"text-decoration-color":[{decoration:tr()}],"underline-offset":[{"underline-offset":[w,"auto",F,O]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:$()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",F,O]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",F,O]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:ti()}],"bg-repeat":[{bg:tn()}],"bg-size":[{bg:ts()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},k,F,O],radial:["",F,O],conic:[k,F,O]},U,L]}],"bg-color":[{bg:tr()}],"gradient-from-pos":[{from:to()}],"gradient-via-pos":[{via:to()}],"gradient-to-pos":[{to:to()}],"gradient-from":[{from:tr()}],"gradient-via":[{via:tr()}],"gradient-to":[{to:tr()}],rounded:[{rounded:ta()}],"rounded-s":[{"rounded-s":ta()}],"rounded-e":[{"rounded-e":ta()}],"rounded-t":[{"rounded-t":ta()}],"rounded-r":[{"rounded-r":ta()}],"rounded-b":[{"rounded-b":ta()}],"rounded-l":[{"rounded-l":ta()}],"rounded-ss":[{"rounded-ss":ta()}],"rounded-se":[{"rounded-se":ta()}],"rounded-ee":[{"rounded-ee":ta()}],"rounded-es":[{"rounded-es":ta()}],"rounded-tl":[{"rounded-tl":ta()}],"rounded-tr":[{"rounded-tr":ta()}],"rounded-br":[{"rounded-br":ta()}],"rounded-bl":[{"rounded-bl":ta()}],"border-w":[{border:tl()}],"border-w-x":[{"border-x":tl()}],"border-w-y":[{"border-y":tl()}],"border-w-s":[{"border-s":tl()}],"border-w-e":[{"border-e":tl()}],"border-w-t":[{"border-t":tl()}],"border-w-r":[{"border-r":tl()}],"border-w-b":[{"border-b":tl()}],"border-w-l":[{"border-l":tl()}],"divide-x":[{"divide-x":tl()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":tl()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...tu(),"hidden","none"]}],"divide-style":[{divide:[...tu(),"hidden","none"]}],"border-color":[{border:tr()}],"border-color-x":[{"border-x":tr()}],"border-color-y":[{"border-y":tr()}],"border-color-s":[{"border-s":tr()}],"border-color-e":[{"border-e":tr()}],"border-color-t":[{"border-t":tr()}],"border-color-r":[{"border-r":tr()}],"border-color-b":[{"border-b":tr()}],"border-color-l":[{"border-l":tr()}],"divide-color":[{divide:tr()}],"outline-style":[{outline:[...tu(),"none","hidden"]}],"outline-offset":[{"outline-offset":[w,F,O]}],"outline-w":[{outline:["",w,B,R]}],"outline-color":[{outline:tr()}],shadow:[{shadow:["","none",h,W,z]}],"shadow-color":[{shadow:tr()}],"inset-shadow":[{"inset-shadow":["none",d,W,z]}],"inset-shadow-color":[{"inset-shadow":tr()}],"ring-w":[{ring:tl()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:tr()}],"ring-offset-w":[{"ring-offset":[w,R]}],"ring-offset-color":[{"ring-offset":tr()}],"inset-ring-w":[{"inset-ring":tl()}],"inset-ring-color":[{"inset-ring":tr()}],"text-shadow":[{"text-shadow":["none",p,W,z]}],"text-shadow-color":[{"text-shadow":tr()}],opacity:[{opacity:[w,F,O]}],"mix-blend":[{"mix-blend":[...th(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":th()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[w]}],"mask-image-linear-from-pos":[{"mask-linear-from":tc()}],"mask-image-linear-to-pos":[{"mask-linear-to":tc()}],"mask-image-linear-from-color":[{"mask-linear-from":tr()}],"mask-image-linear-to-color":[{"mask-linear-to":tr()}],"mask-image-t-from-pos":[{"mask-t-from":tc()}],"mask-image-t-to-pos":[{"mask-t-to":tc()}],"mask-image-t-from-color":[{"mask-t-from":tr()}],"mask-image-t-to-color":[{"mask-t-to":tr()}],"mask-image-r-from-pos":[{"mask-r-from":tc()}],"mask-image-r-to-pos":[{"mask-r-to":tc()}],"mask-image-r-from-color":[{"mask-r-from":tr()}],"mask-image-r-to-color":[{"mask-r-to":tr()}],"mask-image-b-from-pos":[{"mask-b-from":tc()}],"mask-image-b-to-pos":[{"mask-b-to":tc()}],"mask-image-b-from-color":[{"mask-b-from":tr()}],"mask-image-b-to-color":[{"mask-b-to":tr()}],"mask-image-l-from-pos":[{"mask-l-from":tc()}],"mask-image-l-to-pos":[{"mask-l-to":tc()}],"mask-image-l-from-color":[{"mask-l-from":tr()}],"mask-image-l-to-color":[{"mask-l-to":tr()}],"mask-image-x-from-pos":[{"mask-x-from":tc()}],"mask-image-x-to-pos":[{"mask-x-to":tc()}],"mask-image-x-from-color":[{"mask-x-from":tr()}],"mask-image-x-to-color":[{"mask-x-to":tr()}],"mask-image-y-from-pos":[{"mask-y-from":tc()}],"mask-image-y-to-pos":[{"mask-y-to":tc()}],"mask-image-y-from-color":[{"mask-y-from":tr()}],"mask-image-y-to-color":[{"mask-y-to":tr()}],"mask-image-radial":[{"mask-radial":[F,O]}],"mask-image-radial-from-pos":[{"mask-radial-from":tc()}],"mask-image-radial-to-pos":[{"mask-radial-to":tc()}],"mask-image-radial-from-color":[{"mask-radial-from":tr()}],"mask-image-radial-to-color":[{"mask-radial-to":tr()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":S()}],"mask-image-conic-pos":[{"mask-conic":[w]}],"mask-image-conic-from-pos":[{"mask-conic-from":tc()}],"mask-image-conic-to-pos":[{"mask-conic-to":tc()}],"mask-image-conic-from-color":[{"mask-conic-from":tr()}],"mask-image-conic-to-color":[{"mask-conic-to":tr()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:ti()}],"mask-repeat":[{mask:tn()}],"mask-size":[{mask:ts()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",F,O]}],filter:[{filter:["","none",F,O]}],blur:[{blur:td()}],brightness:[{brightness:[w,F,O]}],contrast:[{contrast:[w,F,O]}],"drop-shadow":[{"drop-shadow":["","none",f,W,z]}],"drop-shadow-color":[{"drop-shadow":tr()}],grayscale:[{grayscale:["",w,F,O]}],"hue-rotate":[{"hue-rotate":[w,F,O]}],invert:[{invert:["",w,F,O]}],saturate:[{saturate:[w,F,O]}],sepia:[{sepia:["",w,F,O]}],"backdrop-filter":[{"backdrop-filter":["","none",F,O]}],"backdrop-blur":[{"backdrop-blur":td()}],"backdrop-brightness":[{"backdrop-brightness":[w,F,O]}],"backdrop-contrast":[{"backdrop-contrast":[w,F,O]}],"backdrop-grayscale":[{"backdrop-grayscale":["",w,F,O]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[w,F,O]}],"backdrop-invert":[{"backdrop-invert":["",w,F,O]}],"backdrop-opacity":[{"backdrop-opacity":[w,F,O]}],"backdrop-saturate":[{"backdrop-saturate":[w,F,O]}],"backdrop-sepia":[{"backdrop-sepia":["",w,F,O]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":$()}],"border-spacing-x":[{"border-spacing-x":$()}],"border-spacing-y":[{"border-spacing-y":$()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",F,O]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[w,"initial",F,O]}],ease:[{ease:["linear","initial",y,F,O]}],delay:[{delay:[w,F,O]}],animate:[{animate:["none",x,F,O]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[g,F,O]}],"perspective-origin":[{"perspective-origin":E()}],rotate:[{rotate:tp()}],"rotate-x":[{"rotate-x":tp()}],"rotate-y":[{"rotate-y":tp()}],"rotate-z":[{"rotate-z":tp()}],scale:[{scale:tf()}],"scale-x":[{"scale-x":tf()}],"scale-y":[{"scale-y":tf()}],"scale-z":[{"scale-z":tf()}],"scale-3d":["scale-3d"],skew:[{skew:tm()}],"skew-x":[{"skew-x":tm()}],"skew-y":[{"skew-y":tm()}],transform:[{transform:[F,O,"","none","gpu","cpu"]}],"transform-origin":[{origin:E()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:tg()}],"translate-x":[{"translate-x":tg()}],"translate-y":[{"translate-y":tg()}],"translate-z":[{"translate-z":tg()}],"translate-none":["translate-none"],accent:[{accent:tr()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:tr()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",F,O]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":$()}],"scroll-mx":[{"scroll-mx":$()}],"scroll-my":[{"scroll-my":$()}],"scroll-ms":[{"scroll-ms":$()}],"scroll-me":[{"scroll-me":$()}],"scroll-mt":[{"scroll-mt":$()}],"scroll-mr":[{"scroll-mr":$()}],"scroll-mb":[{"scroll-mb":$()}],"scroll-ml":[{"scroll-ml":$()}],"scroll-p":[{"scroll-p":$()}],"scroll-px":[{"scroll-px":$()}],"scroll-py":[{"scroll-py":$()}],"scroll-ps":[{"scroll-ps":$()}],"scroll-pe":[{"scroll-pe":$()}],"scroll-pt":[{"scroll-pt":$()}],"scroll-pr":[{"scroll-pr":$()}],"scroll-pb":[{"scroll-pb":$()}],"scroll-pl":[{"scroll-pl":$()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",F,O]}],fill:[{fill:["none",...tr()]}],"stroke-w":[{stroke:[w,B,R,_]}],stroke:[{stroke:["none",...tr()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}})},9803:(t,e,r)=>{r.d(e,{A:()=>i});let i=(0,r(9946).A)("video",[["path",{d:"m16 13 5.223 3.482a.5.5 0 0 0 .777-.416V7.87a.5.5 0 0 0-.752-.432L16 10.5",key:"ftymec"}],["rect",{x:"2",y:"6",width:"14",height:"12",rx:"2",key:"158x01"}]])},9946:(t,e,r)=>{r.d(e,{A:()=>l});var i=r(2115);let n=t=>{let e=t.replace(/^([A-Z])|[\s-_]+(\w)/g,(t,e,r)=>r?r.toUpperCase():e.toLowerCase());return e.charAt(0).toUpperCase()+e.slice(1)},s=function(){for(var t=arguments.length,e=Array(t),r=0;r<t;r++)e[r]=arguments[r];return e.filter((t,e,r)=>!!t&&""!==t.trim()&&r.indexOf(t)===e).join(" ").trim()};var o={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let a=(0,i.forwardRef)((t,e)=>{let{color:r="currentColor",size:n=24,strokeWidth:a=2,absoluteStrokeWidth:l,className:u="",children:h,iconNode:c,...d}=t;return(0,i.createElement)("svg",{ref:e,...o,width:n,height:n,stroke:r,strokeWidth:l?24*Number(a)/Number(n):a,className:s("lucide",u),...!h&&!(t=>{for(let e in t)if(e.startsWith("aria-")||"role"===e||"title"===e)return!0})(d)&&{"aria-hidden":"true"},...d},[...c.map(t=>{let[e,r]=t;return(0,i.createElement)(e,r)}),...Array.isArray(h)?h:[h]])}),l=(t,e)=>{let r=(0,i.forwardRef)((r,o)=>{let{className:l,...u}=r;return(0,i.createElement)(a,{ref:o,iconNode:e,className:s("lucide-".concat(n(t).replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase()),"lucide-".concat(t),l),...u})});return r.displayName=n(t),r}},9947:(t,e,r)=>{r.d(e,{A:()=>i});let i=(0,r(9946).A)("circle-question-mark",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3",key:"1u773s"}],["path",{d:"M12 17h.01",key:"p32p05"}]])}}]);