(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[69],{1666:e=>{e.exports={style:{fontFamily:"'Inter', 'Inter Fallback'",fontStyle:"normal"},className:"__className_e8ce0c",variable:"__variable_e8ce0c"}},7040:(e,t,n)=>{"use strict";n.d(t,{Content:()=>tM,Provider:()=>tD,Root:()=>tk,Trigger:()=>tj});var r,o=n(2115),i=n.t(o,2);function l(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}function a(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function u(...e){return t=>{let n=!1,r=e.map(e=>{let r=a(e,t);return n||"function"!=typeof r||(n=!0),r});if(n)return()=>{for(let t=0;t<r.length;t++){let n=r[t];"function"==typeof n?n():a(e[t],null)}}}}function s(...e){return o.useCallback(u(...e),e)}var c=n(5155);function f(e,t=[]){let n=[],r=()=>{let t=n.map(e=>o.createContext(e));return function(n){let r=n?.[e]||t;return o.useMemo(()=>({[`__scope${e}`]:{...n,[e]:r}}),[n,r])}};return r.scopeName=e,[function(t,r){let i=o.createContext(r),l=n.length;n=[...n,r];let a=t=>{let{scope:n,children:r,...a}=t,u=n?.[e]?.[l]||i,s=o.useMemo(()=>a,Object.values(a));return(0,c.jsx)(u.Provider,{value:s,children:r})};return a.displayName=t+"Provider",[a,function(n,a){let u=a?.[e]?.[l]||i,s=o.useContext(u);if(s)return s;if(void 0!==r)return r;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let r=n.reduce((t,{useScope:n,scopeName:r})=>{let o=n(e)[`__scope${r}`];return{...t,...o}},{});return o.useMemo(()=>({[`__scope${t.scopeName}`]:r}),[r])}};return n.scopeName=t.scopeName,n}(r,...t)]}var d=n(7650),p=Symbol("radix.slottable");function m(e){return o.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===p}var h=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let n=function(e){let t=function(e){let t=o.forwardRef((e,t)=>{let{children:n,...r}=e;if(o.isValidElement(n)){var i;let e,l,a=(i=n,(l=(e=Object.getOwnPropertyDescriptor(i.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.ref:(l=(e=Object.getOwnPropertyDescriptor(i,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.props.ref:i.props.ref||i.ref),s=function(e,t){let n={...t};for(let r in t){let o=e[r],i=t[r];/^on[A-Z]/.test(r)?o&&i?n[r]=(...e)=>{let t=i(...e);return o(...e),t}:o&&(n[r]=o):"style"===r?n[r]={...o,...i}:"className"===r&&(n[r]=[o,i].filter(Boolean).join(" "))}return{...e,...n}}(r,n.props);return n.type!==o.Fragment&&(s.ref=t?u(t,a):a),o.cloneElement(n,s)}return o.Children.count(n)>1?o.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),n=o.forwardRef((e,n)=>{let{children:r,...i}=e,l=o.Children.toArray(r),a=l.find(m);if(a){let e=a.props.children,r=l.map(t=>t!==a?t:o.Children.count(e)>1?o.Children.only(null):o.isValidElement(e)?e.props.children:null);return(0,c.jsx)(t,{...i,ref:n,children:o.isValidElement(e)?o.cloneElement(e,void 0,r):null})}return(0,c.jsx)(t,{...i,ref:n,children:r})});return n.displayName=`${e}.Slot`,n}(`Primitive.${t}`),r=o.forwardRef((e,r)=>{let{asChild:o,...i}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,c.jsx)(o?n:t,{...i,ref:r})});return r.displayName=`Primitive.${t}`,{...e,[t]:r}},{});function g(e){let t=o.useRef(e);return o.useEffect(()=>{t.current=e}),o.useMemo(()=>(...e)=>t.current?.(...e),[])}var v="dismissableLayer.update",y=o.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),w=o.forwardRef((e,t)=>{var n,i;let{disableOutsidePointerEvents:a=!1,onEscapeKeyDown:u,onPointerDownOutside:f,onFocusOutside:d,onInteractOutside:p,onDismiss:m,...w}=e,E=o.useContext(y),[R,C]=o.useState(null),T=null!=(i=null==R?void 0:R.ownerDocument)?i:null==(n=globalThis)?void 0:n.document,[,L]=o.useState({}),O=s(t,e=>C(e)),P=Array.from(E.layers),[A]=[...E.layersWithOutsidePointerEventsDisabled].slice(-1),S=P.indexOf(A),N=R?P.indexOf(R):-1,D=E.layersWithOutsidePointerEventsDisabled.size>0,k=N>=S,j=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,r=g(e),i=o.useRef(!1),l=o.useRef(()=>{});return o.useEffect(()=>{let e=e=>{if(e.target&&!i.current){let t=function(){b("dismissableLayer.pointerDownOutside",r,o,{discrete:!0})},o={originalEvent:e};"touch"===e.pointerType?(n.removeEventListener("click",l.current),l.current=t,n.addEventListener("click",l.current,{once:!0})):t()}else n.removeEventListener("click",l.current);i.current=!1},t=window.setTimeout(()=>{n.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(t),n.removeEventListener("pointerdown",e),n.removeEventListener("click",l.current)}},[n,r]),{onPointerDownCapture:()=>i.current=!0}}(e=>{let t=e.target,n=[...E.branches].some(e=>e.contains(t));k&&!n&&(null==f||f(e),null==p||p(e),e.defaultPrevented||null==m||m())},T),M=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,r=g(e),i=o.useRef(!1);return o.useEffect(()=>{let e=e=>{e.target&&!i.current&&b("dismissableLayer.focusOutside",r,{originalEvent:e},{discrete:!1})};return n.addEventListener("focusin",e),()=>n.removeEventListener("focusin",e)},[n,r]),{onFocusCapture:()=>i.current=!0,onBlurCapture:()=>i.current=!1}}(e=>{let t=e.target;![...E.branches].some(e=>e.contains(t))&&(null==d||d(e),null==p||p(e),e.defaultPrevented||null==m||m())},T);return!function(e,t=globalThis?.document){let n=g(e);o.useEffect(()=>{let e=e=>{"Escape"===e.key&&n(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[n,t])}(e=>{N===E.layers.size-1&&(null==u||u(e),!e.defaultPrevented&&m&&(e.preventDefault(),m()))},T),o.useEffect(()=>{if(R)return a&&(0===E.layersWithOutsidePointerEventsDisabled.size&&(r=T.body.style.pointerEvents,T.body.style.pointerEvents="none"),E.layersWithOutsidePointerEventsDisabled.add(R)),E.layers.add(R),x(),()=>{a&&1===E.layersWithOutsidePointerEventsDisabled.size&&(T.body.style.pointerEvents=r)}},[R,T,a,E]),o.useEffect(()=>()=>{R&&(E.layers.delete(R),E.layersWithOutsidePointerEventsDisabled.delete(R),x())},[R,E]),o.useEffect(()=>{let e=()=>L({});return document.addEventListener(v,e),()=>document.removeEventListener(v,e)},[]),(0,c.jsx)(h.div,{...w,ref:O,style:{pointerEvents:D?k?"auto":"none":void 0,...e.style},onFocusCapture:l(e.onFocusCapture,M.onFocusCapture),onBlurCapture:l(e.onBlurCapture,M.onBlurCapture),onPointerDownCapture:l(e.onPointerDownCapture,j.onPointerDownCapture)})});function x(){let e=new CustomEvent(v);document.dispatchEvent(e)}function b(e,t,n,r){let{discrete:o}=r,i=n.originalEvent.target,l=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});if(t&&i.addEventListener(e,t,{once:!0}),o)i&&d.flushSync(()=>i.dispatchEvent(l));else i.dispatchEvent(l)}w.displayName="DismissableLayer",o.forwardRef((e,t)=>{let n=o.useContext(y),r=o.useRef(null),i=s(t,r);return o.useEffect(()=>{let e=r.current;if(e)return n.branches.add(e),()=>{n.branches.delete(e)}},[n.branches]),(0,c.jsx)(h.div,{...e,ref:i})}).displayName="DismissableLayerBranch";var E=globalThis?.document?o.useLayoutEffect:()=>{},R=i[" useId ".trim().toString()]||(()=>void 0),C=0;let T=["top","right","bottom","left"],L=Math.min,O=Math.max,P=Math.round,A=Math.floor,S=e=>({x:e,y:e}),N={left:"right",right:"left",bottom:"top",top:"bottom"},D={start:"end",end:"start"};function k(e,t){return"function"==typeof e?e(t):e}function j(e){return e.split("-")[0]}function M(e){return e.split("-")[1]}function _(e){return"x"===e?"y":"x"}function F(e){return"y"===e?"height":"width"}let W=new Set(["top","bottom"]);function I(e){return W.has(j(e))?"y":"x"}function H(e){return e.replace(/start|end/g,e=>D[e])}let B=["left","right"],z=["right","left"],$=["top","bottom"],V=["bottom","top"];function U(e){return e.replace(/left|right|bottom|top/g,e=>N[e])}function Y(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function X(e){let{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}function q(e,t,n){let r,{reference:o,floating:i}=e,l=I(t),a=_(I(t)),u=F(a),s=j(t),c="y"===l,f=o.x+o.width/2-i.width/2,d=o.y+o.height/2-i.height/2,p=o[u]/2-i[u]/2;switch(s){case"top":r={x:f,y:o.y-i.height};break;case"bottom":r={x:f,y:o.y+o.height};break;case"right":r={x:o.x+o.width,y:d};break;case"left":r={x:o.x-i.width,y:d};break;default:r={x:o.x,y:o.y}}switch(M(t)){case"start":r[a]-=p*(n&&c?-1:1);break;case"end":r[a]+=p*(n&&c?-1:1)}return r}let Z=async(e,t,n)=>{let{placement:r="bottom",strategy:o="absolute",middleware:i=[],platform:l}=n,a=i.filter(Boolean),u=await (null==l.isRTL?void 0:l.isRTL(t)),s=await l.getElementRects({reference:e,floating:t,strategy:o}),{x:c,y:f}=q(s,r,u),d=r,p={},m=0;for(let n=0;n<a.length;n++){let{name:i,fn:h}=a[n],{x:g,y:v,data:y,reset:w}=await h({x:c,y:f,initialPlacement:r,placement:d,strategy:o,middlewareData:p,rects:s,platform:l,elements:{reference:e,floating:t}});c=null!=g?g:c,f=null!=v?v:f,p={...p,[i]:{...p[i],...y}},w&&m<=50&&(m++,"object"==typeof w&&(w.placement&&(d=w.placement),w.rects&&(s=!0===w.rects?await l.getElementRects({reference:e,floating:t,strategy:o}):w.rects),{x:c,y:f}=q(s,d,u)),n=-1)}return{x:c,y:f,placement:d,strategy:o,middlewareData:p}};async function G(e,t){var n;void 0===t&&(t={});let{x:r,y:o,platform:i,rects:l,elements:a,strategy:u}=e,{boundary:s="clippingAncestors",rootBoundary:c="viewport",elementContext:f="floating",altBoundary:d=!1,padding:p=0}=k(t,e),m=Y(p),h=a[d?"floating"===f?"reference":"floating":f],g=X(await i.getClippingRect({element:null==(n=await (null==i.isElement?void 0:i.isElement(h)))||n?h:h.contextElement||await (null==i.getDocumentElement?void 0:i.getDocumentElement(a.floating)),boundary:s,rootBoundary:c,strategy:u})),v="floating"===f?{x:r,y:o,width:l.floating.width,height:l.floating.height}:l.reference,y=await (null==i.getOffsetParent?void 0:i.getOffsetParent(a.floating)),w=await (null==i.isElement?void 0:i.isElement(y))&&await (null==i.getScale?void 0:i.getScale(y))||{x:1,y:1},x=X(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({elements:a,rect:v,offsetParent:y,strategy:u}):v);return{top:(g.top-x.top+m.top)/w.y,bottom:(x.bottom-g.bottom+m.bottom)/w.y,left:(g.left-x.left+m.left)/w.x,right:(x.right-g.right+m.right)/w.x}}function J(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function K(e){return T.some(t=>e[t]>=0)}let Q=new Set(["left","top"]);async function ee(e,t){let{placement:n,platform:r,elements:o}=e,i=await (null==r.isRTL?void 0:r.isRTL(o.floating)),l=j(n),a=M(n),u="y"===I(n),s=Q.has(l)?-1:1,c=i&&u?-1:1,f=k(t,e),{mainAxis:d,crossAxis:p,alignmentAxis:m}="number"==typeof f?{mainAxis:f,crossAxis:0,alignmentAxis:null}:{mainAxis:f.mainAxis||0,crossAxis:f.crossAxis||0,alignmentAxis:f.alignmentAxis};return a&&"number"==typeof m&&(p="end"===a?-1*m:m),u?{x:p*c,y:d*s}:{x:d*s,y:p*c}}function et(){return"undefined"!=typeof window}function en(e){return ei(e)?(e.nodeName||"").toLowerCase():"#document"}function er(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function eo(e){var t;return null==(t=(ei(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function ei(e){return!!et()&&(e instanceof Node||e instanceof er(e).Node)}function el(e){return!!et()&&(e instanceof Element||e instanceof er(e).Element)}function ea(e){return!!et()&&(e instanceof HTMLElement||e instanceof er(e).HTMLElement)}function eu(e){return!!et()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof er(e).ShadowRoot)}let es=new Set(["inline","contents"]);function ec(e){let{overflow:t,overflowX:n,overflowY:r,display:o}=eb(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!es.has(o)}let ef=new Set(["table","td","th"]),ed=[":popover-open",":modal"];function ep(e){return ed.some(t=>{try{return e.matches(t)}catch(e){return!1}})}let em=["transform","translate","scale","rotate","perspective"],eh=["transform","translate","scale","rotate","perspective","filter"],eg=["paint","layout","strict","content"];function ev(e){let t=ey(),n=el(e)?eb(e):e;return em.some(e=>!!n[e]&&"none"!==n[e])||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||eh.some(e=>(n.willChange||"").includes(e))||eg.some(e=>(n.contain||"").includes(e))}function ey(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}let ew=new Set(["html","body","#document"]);function ex(e){return ew.has(en(e))}function eb(e){return er(e).getComputedStyle(e)}function eE(e){return el(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function eR(e){if("html"===en(e))return e;let t=e.assignedSlot||e.parentNode||eu(e)&&e.host||eo(e);return eu(t)?t.host:t}function eC(e,t,n){var r;void 0===t&&(t=[]),void 0===n&&(n=!0);let o=function e(t){let n=eR(t);return ex(n)?t.ownerDocument?t.ownerDocument.body:t.body:ea(n)&&ec(n)?n:e(n)}(e),i=o===(null==(r=e.ownerDocument)?void 0:r.body),l=er(o);if(i){let e=eT(l);return t.concat(l,l.visualViewport||[],ec(o)?o:[],e&&n?eC(e):[])}return t.concat(o,eC(o,[],n))}function eT(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function eL(e){let t=eb(e),n=parseFloat(t.width)||0,r=parseFloat(t.height)||0,o=ea(e),i=o?e.offsetWidth:n,l=o?e.offsetHeight:r,a=P(n)!==i||P(r)!==l;return a&&(n=i,r=l),{width:n,height:r,$:a}}function eO(e){return el(e)?e:e.contextElement}function eP(e){let t=eO(e);if(!ea(t))return S(1);let n=t.getBoundingClientRect(),{width:r,height:o,$:i}=eL(t),l=(i?P(n.width):n.width)/r,a=(i?P(n.height):n.height)/o;return l&&Number.isFinite(l)||(l=1),a&&Number.isFinite(a)||(a=1),{x:l,y:a}}let eA=S(0);function eS(e){let t=er(e);return ey()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:eA}function eN(e,t,n,r){var o;void 0===t&&(t=!1),void 0===n&&(n=!1);let i=e.getBoundingClientRect(),l=eO(e),a=S(1);t&&(r?el(r)&&(a=eP(r)):a=eP(e));let u=(void 0===(o=n)&&(o=!1),r&&(!o||r===er(l))&&o)?eS(l):S(0),s=(i.left+u.x)/a.x,c=(i.top+u.y)/a.y,f=i.width/a.x,d=i.height/a.y;if(l){let e=er(l),t=r&&el(r)?er(r):r,n=e,o=eT(n);for(;o&&r&&t!==n;){let e=eP(o),t=o.getBoundingClientRect(),r=eb(o),i=t.left+(o.clientLeft+parseFloat(r.paddingLeft))*e.x,l=t.top+(o.clientTop+parseFloat(r.paddingTop))*e.y;s*=e.x,c*=e.y,f*=e.x,d*=e.y,s+=i,c+=l,o=eT(n=er(o))}}return X({width:f,height:d,x:s,y:c})}function eD(e,t){let n=eE(e).scrollLeft;return t?t.left+n:eN(eo(e)).left+n}function ek(e,t,n){void 0===n&&(n=!1);let r=e.getBoundingClientRect();return{x:r.left+t.scrollLeft-(n?0:eD(e,r)),y:r.top+t.scrollTop}}let ej=new Set(["absolute","fixed"]);function eM(e,t,n){let r;if("viewport"===t)r=function(e,t){let n=er(e),r=eo(e),o=n.visualViewport,i=r.clientWidth,l=r.clientHeight,a=0,u=0;if(o){i=o.width,l=o.height;let e=ey();(!e||e&&"fixed"===t)&&(a=o.offsetLeft,u=o.offsetTop)}return{width:i,height:l,x:a,y:u}}(e,n);else if("document"===t)r=function(e){let t=eo(e),n=eE(e),r=e.ownerDocument.body,o=O(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),i=O(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight),l=-n.scrollLeft+eD(e),a=-n.scrollTop;return"rtl"===eb(r).direction&&(l+=O(t.clientWidth,r.clientWidth)-o),{width:o,height:i,x:l,y:a}}(eo(e));else if(el(t))r=function(e,t){let n=eN(e,!0,"fixed"===t),r=n.top+e.clientTop,o=n.left+e.clientLeft,i=ea(e)?eP(e):S(1),l=e.clientWidth*i.x,a=e.clientHeight*i.y;return{width:l,height:a,x:o*i.x,y:r*i.y}}(t,n);else{let n=eS(e);r={x:t.x-n.x,y:t.y-n.y,width:t.width,height:t.height}}return X(r)}function e_(e){return"static"===eb(e).position}function eF(e,t){if(!ea(e)||"fixed"===eb(e).position)return null;if(t)return t(e);let n=e.offsetParent;return eo(e)===n&&(n=n.ownerDocument.body),n}function eW(e,t){var n;let r=er(e);if(ep(e))return r;if(!ea(e)){let t=eR(e);for(;t&&!ex(t);){if(el(t)&&!e_(t))return t;t=eR(t)}return r}let o=eF(e,t);for(;o&&(n=o,ef.has(en(n)))&&e_(o);)o=eF(o,t);return o&&ex(o)&&e_(o)&&!ev(o)?r:o||function(e){let t=eR(e);for(;ea(t)&&!ex(t);){if(ev(t))return t;if(ep(t))break;t=eR(t)}return null}(e)||r}let eI=async function(e){let t=this.getOffsetParent||eW,n=this.getDimensions,r=await n(e.floating);return{reference:function(e,t,n){let r=ea(t),o=eo(t),i="fixed"===n,l=eN(e,!0,i,t),a={scrollLeft:0,scrollTop:0},u=S(0);if(r||!r&&!i)if(("body"!==en(t)||ec(o))&&(a=eE(t)),r){let e=eN(t,!0,i,t);u.x=e.x+t.clientLeft,u.y=e.y+t.clientTop}else o&&(u.x=eD(o));i&&!r&&o&&(u.x=eD(o));let s=!o||r||i?S(0):ek(o,a);return{x:l.left+a.scrollLeft-u.x-s.x,y:l.top+a.scrollTop-u.y-s.y,width:l.width,height:l.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},eH={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e,i="fixed"===o,l=eo(r),a=!!t&&ep(t.floating);if(r===l||a&&i)return n;let u={scrollLeft:0,scrollTop:0},s=S(1),c=S(0),f=ea(r);if((f||!f&&!i)&&(("body"!==en(r)||ec(l))&&(u=eE(r)),ea(r))){let e=eN(r);s=eP(r),c.x=e.x+r.clientLeft,c.y=e.y+r.clientTop}let d=!l||f||i?S(0):ek(l,u,!0);return{width:n.width*s.x,height:n.height*s.y,x:n.x*s.x-u.scrollLeft*s.x+c.x+d.x,y:n.y*s.y-u.scrollTop*s.y+c.y+d.y}},getDocumentElement:eo,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:r,strategy:o}=e,i=[..."clippingAncestors"===n?ep(t)?[]:function(e,t){let n=t.get(e);if(n)return n;let r=eC(e,[],!1).filter(e=>el(e)&&"body"!==en(e)),o=null,i="fixed"===eb(e).position,l=i?eR(e):e;for(;el(l)&&!ex(l);){let t=eb(l),n=ev(l);n||"fixed"!==t.position||(o=null),(i?!n&&!o:!n&&"static"===t.position&&!!o&&ej.has(o.position)||ec(l)&&!n&&function e(t,n){let r=eR(t);return!(r===n||!el(r)||ex(r))&&("fixed"===eb(r).position||e(r,n))}(e,l))?r=r.filter(e=>e!==l):o=t,l=eR(l)}return t.set(e,r),r}(t,this._c):[].concat(n),r],l=i[0],a=i.reduce((e,n)=>{let r=eM(t,n,o);return e.top=O(r.top,e.top),e.right=L(r.right,e.right),e.bottom=L(r.bottom,e.bottom),e.left=O(r.left,e.left),e},eM(t,l,o));return{width:a.right-a.left,height:a.bottom-a.top,x:a.left,y:a.top}},getOffsetParent:eW,getElementRects:eI,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:n}=eL(e);return{width:t,height:n}},getScale:eP,isElement:el,isRTL:function(e){return"rtl"===eb(e).direction}};function eB(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}let ez=e=>({name:"arrow",options:e,async fn(t){let{x:n,y:r,placement:o,rects:i,platform:l,elements:a,middlewareData:u}=t,{element:s,padding:c=0}=k(e,t)||{};if(null==s)return{};let f=Y(c),d={x:n,y:r},p=_(I(o)),m=F(p),h=await l.getDimensions(s),g="y"===p,v=g?"clientHeight":"clientWidth",y=i.reference[m]+i.reference[p]-d[p]-i.floating[m],w=d[p]-i.reference[p],x=await (null==l.getOffsetParent?void 0:l.getOffsetParent(s)),b=x?x[v]:0;b&&await (null==l.isElement?void 0:l.isElement(x))||(b=a.floating[v]||i.floating[m]);let E=b/2-h[m]/2-1,R=L(f[g?"top":"left"],E),C=L(f[g?"bottom":"right"],E),T=b-h[m]-C,P=b/2-h[m]/2+(y/2-w/2),A=O(R,L(P,T)),S=!u.arrow&&null!=M(o)&&P!==A&&i.reference[m]/2-(P<R?R:C)-h[m]/2<0,N=S?P<R?P-R:P-T:0;return{[p]:d[p]+N,data:{[p]:A,centerOffset:P-A-N,...S&&{alignmentOffset:N}},reset:S}}});var e$="undefined"!=typeof document?o.useLayoutEffect:function(){};function eV(e,t){let n,r,o;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((n=e.length)!==t.length)return!1;for(r=n;0!=r--;)if(!eV(e[r],t[r]))return!1;return!0}if((n=(o=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(r=n;0!=r--;)if(!({}).hasOwnProperty.call(t,o[r]))return!1;for(r=n;0!=r--;){let n=o[r];if(("_owner"!==n||!e.$$typeof)&&!eV(e[n],t[n]))return!1}return!0}return e!=e&&t!=t}function eU(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function eY(e,t){let n=eU(e);return Math.round(t*n)/n}function eX(e){let t=o.useRef(e);return e$(()=>{t.current=e}),t}var eq=o.forwardRef((e,t)=>{let{children:n,width:r=10,height:o=5,...i}=e;return(0,c.jsx)(h.svg,{...i,ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:(0,c.jsx)("polygon",{points:"0,0 30,0 15,10"})})});eq.displayName="Arrow";var eZ="Popper",[eG,eJ]=f(eZ),[eK,eQ]=eG(eZ),e0=e=>{let{__scopePopper:t,children:n}=e,[r,i]=o.useState(null);return(0,c.jsx)(eK,{scope:t,anchor:r,onAnchorChange:i,children:n})};e0.displayName=eZ;var e1="PopperAnchor",e2=o.forwardRef((e,t)=>{let{__scopePopper:n,virtualRef:r,...i}=e,l=eQ(e1,n),a=o.useRef(null),u=s(t,a);return o.useEffect(()=>{l.onAnchorChange((null==r?void 0:r.current)||a.current)}),r?null:(0,c.jsx)(h.div,{...i,ref:u})});e2.displayName=e1;var e5="PopperContent",[e3,e6]=eG(e5),e7=o.forwardRef((e,t)=>{var n,r,i,l,a,u,f,p;let{__scopePopper:m,side:v="bottom",sideOffset:y=0,align:w="center",alignOffset:x=0,arrowPadding:b=0,avoidCollisions:R=!0,collisionBoundary:C=[],collisionPadding:T=0,sticky:P="partial",hideWhenDetached:S=!1,updatePositionStrategy:N="optimized",onPlaced:D,...W}=e,Y=eQ(e5,m),[X,q]=o.useState(null),et=s(t,e=>q(e)),[en,er]=o.useState(null),ei=function(e){let[t,n]=o.useState(void 0);return E(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let r,o;if(!Array.isArray(t)||!t.length)return;let i=t[0];if("borderBoxSize"in i){let e=i.borderBoxSize,t=Array.isArray(e)?e[0]:e;r=t.inlineSize,o=t.blockSize}else r=e.offsetWidth,o=e.offsetHeight;n({width:r,height:o})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}n(void 0)},[e]),t}(en),el=null!=(f=null==ei?void 0:ei.width)?f:0,ea=null!=(p=null==ei?void 0:ei.height)?p:0,eu="number"==typeof T?T:{top:0,right:0,bottom:0,left:0,...T},es=Array.isArray(C)?C:[C],ec=es.length>0,ef={padding:eu,boundary:es.filter(te),altBoundary:ec},{refs:ed,floatingStyles:ep,placement:em,isPositioned:eh,middlewareData:eg}=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:n="absolute",middleware:r=[],platform:i,elements:{reference:l,floating:a}={},transform:u=!0,whileElementsMounted:s,open:c}=e,[f,p]=o.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[m,h]=o.useState(r);eV(m,r)||h(r);let[g,v]=o.useState(null),[y,w]=o.useState(null),x=o.useCallback(e=>{e!==C.current&&(C.current=e,v(e))},[]),b=o.useCallback(e=>{e!==T.current&&(T.current=e,w(e))},[]),E=l||g,R=a||y,C=o.useRef(null),T=o.useRef(null),L=o.useRef(f),O=null!=s,P=eX(s),A=eX(i),S=eX(c),N=o.useCallback(()=>{if(!C.current||!T.current)return;let e={placement:t,strategy:n,middleware:m};A.current&&(e.platform=A.current),((e,t,n)=>{let r=new Map,o={platform:eH,...n},i={...o.platform,_c:r};return Z(e,t,{...o,platform:i})})(C.current,T.current,e).then(e=>{let t={...e,isPositioned:!1!==S.current};D.current&&!eV(L.current,t)&&(L.current=t,d.flushSync(()=>{p(t)}))})},[m,t,n,A,S]);e$(()=>{!1===c&&L.current.isPositioned&&(L.current.isPositioned=!1,p(e=>({...e,isPositioned:!1})))},[c]);let D=o.useRef(!1);e$(()=>(D.current=!0,()=>{D.current=!1}),[]),e$(()=>{if(E&&(C.current=E),R&&(T.current=R),E&&R){if(P.current)return P.current(E,R,N);N()}},[E,R,N,P,O]);let k=o.useMemo(()=>({reference:C,floating:T,setReference:x,setFloating:b}),[x,b]),j=o.useMemo(()=>({reference:E,floating:R}),[E,R]),M=o.useMemo(()=>{let e={position:n,left:0,top:0};if(!j.floating)return e;let t=eY(j.floating,f.x),r=eY(j.floating,f.y);return u?{...e,transform:"translate("+t+"px, "+r+"px)",...eU(j.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:t,top:r}},[n,u,j.floating,f.x,f.y]);return o.useMemo(()=>({...f,update:N,refs:k,elements:j,floatingStyles:M}),[f,N,k,j,M])}({strategy:"fixed",placement:v+("center"!==w?"-"+w:""),whileElementsMounted:function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return function(e,t,n,r){let o;void 0===r&&(r={});let{ancestorScroll:i=!0,ancestorResize:l=!0,elementResize:a="function"==typeof ResizeObserver,layoutShift:u="function"==typeof IntersectionObserver,animationFrame:s=!1}=r,c=eO(e),f=i||l?[...c?eC(c):[],...eC(t)]:[];f.forEach(e=>{i&&e.addEventListener("scroll",n,{passive:!0}),l&&e.addEventListener("resize",n)});let d=c&&u?function(e,t){let n,r=null,o=eo(e);function i(){var e;clearTimeout(n),null==(e=r)||e.disconnect(),r=null}return!function l(a,u){void 0===a&&(a=!1),void 0===u&&(u=1),i();let s=e.getBoundingClientRect(),{left:c,top:f,width:d,height:p}=s;if(a||t(),!d||!p)return;let m=A(f),h=A(o.clientWidth-(c+d)),g={rootMargin:-m+"px "+-h+"px "+-A(o.clientHeight-(f+p))+"px "+-A(c)+"px",threshold:O(0,L(1,u))||1},v=!0;function y(t){let r=t[0].intersectionRatio;if(r!==u){if(!v)return l();r?l(!1,r):n=setTimeout(()=>{l(!1,1e-7)},1e3)}1!==r||eB(s,e.getBoundingClientRect())||l(),v=!1}try{r=new IntersectionObserver(y,{...g,root:o.ownerDocument})}catch(e){r=new IntersectionObserver(y,g)}r.observe(e)}(!0),i}(c,n):null,p=-1,m=null;a&&(m=new ResizeObserver(e=>{let[r]=e;r&&r.target===c&&m&&(m.unobserve(t),cancelAnimationFrame(p),p=requestAnimationFrame(()=>{var e;null==(e=m)||e.observe(t)})),n()}),c&&!s&&m.observe(c),m.observe(t));let h=s?eN(e):null;return s&&function t(){let r=eN(e);h&&!eB(h,r)&&n(),h=r,o=requestAnimationFrame(t)}(),n(),()=>{var e;f.forEach(e=>{i&&e.removeEventListener("scroll",n),l&&e.removeEventListener("resize",n)}),null==d||d(),null==(e=m)||e.disconnect(),m=null,s&&cancelAnimationFrame(o)}}(...t,{animationFrame:"always"===N})},elements:{reference:Y.anchor},middleware:[((e,t)=>({...function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var n,r;let{x:o,y:i,placement:l,middlewareData:a}=t,u=await ee(t,e);return l===(null==(n=a.offset)?void 0:n.placement)&&null!=(r=a.arrow)&&r.alignmentOffset?{}:{x:o+u.x,y:i+u.y,data:{...u,placement:l}}}}}(e),options:[e,t]}))({mainAxis:y+ea,alignmentAxis:x}),R&&((e,t)=>({...function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){let{x:n,y:r,placement:o}=t,{mainAxis:i=!0,crossAxis:l=!1,limiter:a={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...u}=k(e,t),s={x:n,y:r},c=await G(t,u),f=I(j(o)),d=_(f),p=s[d],m=s[f];if(i){let e="y"===d?"top":"left",t="y"===d?"bottom":"right",n=p+c[e],r=p-c[t];p=O(n,L(p,r))}if(l){let e="y"===f?"top":"left",t="y"===f?"bottom":"right",n=m+c[e],r=m-c[t];m=O(n,L(m,r))}let h=a.fn({...t,[d]:p,[f]:m});return{...h,data:{x:h.x-n,y:h.y-r,enabled:{[d]:i,[f]:l}}}}}}(e),options:[e,t]}))({mainAxis:!0,crossAxis:!1,limiter:"partial"===P?((e,t)=>({...function(e){return void 0===e&&(e={}),{options:e,fn(t){let{x:n,y:r,placement:o,rects:i,middlewareData:l}=t,{offset:a=0,mainAxis:u=!0,crossAxis:s=!0}=k(e,t),c={x:n,y:r},f=I(o),d=_(f),p=c[d],m=c[f],h=k(a,t),g="number"==typeof h?{mainAxis:h,crossAxis:0}:{mainAxis:0,crossAxis:0,...h};if(u){let e="y"===d?"height":"width",t=i.reference[d]-i.floating[e]+g.mainAxis,n=i.reference[d]+i.reference[e]-g.mainAxis;p<t?p=t:p>n&&(p=n)}if(s){var v,y;let e="y"===d?"width":"height",t=Q.has(j(o)),n=i.reference[f]-i.floating[e]+(t&&(null==(v=l.offset)?void 0:v[f])||0)+(t?0:g.crossAxis),r=i.reference[f]+i.reference[e]+(t?0:(null==(y=l.offset)?void 0:y[f])||0)-(t?g.crossAxis:0);m<n?m=n:m>r&&(m=r)}return{[d]:p,[f]:m}}}}(e),options:[e,t]}))():void 0,...ef}),R&&((e,t)=>({...function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var n,r,o,i,l;let{placement:a,middlewareData:u,rects:s,initialPlacement:c,platform:f,elements:d}=t,{mainAxis:p=!0,crossAxis:m=!0,fallbackPlacements:h,fallbackStrategy:g="bestFit",fallbackAxisSideDirection:v="none",flipAlignment:y=!0,...w}=k(e,t);if(null!=(n=u.arrow)&&n.alignmentOffset)return{};let x=j(a),b=I(c),E=j(c)===c,R=await (null==f.isRTL?void 0:f.isRTL(d.floating)),C=h||(E||!y?[U(c)]:function(e){let t=U(e);return[H(e),t,H(t)]}(c)),T="none"!==v;!h&&T&&C.push(...function(e,t,n,r){let o=M(e),i=function(e,t,n){switch(e){case"top":case"bottom":if(n)return t?z:B;return t?B:z;case"left":case"right":return t?$:V;default:return[]}}(j(e),"start"===n,r);return o&&(i=i.map(e=>e+"-"+o),t&&(i=i.concat(i.map(H)))),i}(c,y,v,R));let L=[c,...C],O=await G(t,w),P=[],A=(null==(r=u.flip)?void 0:r.overflows)||[];if(p&&P.push(O[x]),m){let e=function(e,t,n){void 0===n&&(n=!1);let r=M(e),o=_(I(e)),i=F(o),l="x"===o?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return t.reference[i]>t.floating[i]&&(l=U(l)),[l,U(l)]}(a,s,R);P.push(O[e[0]],O[e[1]])}if(A=[...A,{placement:a,overflows:P}],!P.every(e=>e<=0)){let e=((null==(o=u.flip)?void 0:o.index)||0)+1,t=L[e];if(t&&("alignment"!==m||b===I(t)||A.every(e=>e.overflows[0]>0&&I(e.placement)===b)))return{data:{index:e,overflows:A},reset:{placement:t}};let n=null==(i=A.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:i.placement;if(!n)switch(g){case"bestFit":{let e=null==(l=A.filter(e=>{if(T){let t=I(e.placement);return t===b||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:l[0];e&&(n=e);break}case"initialPlacement":n=c}if(a!==n)return{reset:{placement:n}}}return{}}}}(e),options:[e,t]}))({...ef}),((e,t)=>({...function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var n,r;let o,i,{placement:l,rects:a,platform:u,elements:s}=t,{apply:c=()=>{},...f}=k(e,t),d=await G(t,f),p=j(l),m=M(l),h="y"===I(l),{width:g,height:v}=a.floating;"top"===p||"bottom"===p?(o=p,i=m===(await (null==u.isRTL?void 0:u.isRTL(s.floating))?"start":"end")?"left":"right"):(i=p,o="end"===m?"top":"bottom");let y=v-d.top-d.bottom,w=g-d.left-d.right,x=L(v-d[o],y),b=L(g-d[i],w),E=!t.middlewareData.shift,R=x,C=b;if(null!=(n=t.middlewareData.shift)&&n.enabled.x&&(C=w),null!=(r=t.middlewareData.shift)&&r.enabled.y&&(R=y),E&&!m){let e=O(d.left,0),t=O(d.right,0),n=O(d.top,0),r=O(d.bottom,0);h?C=g-2*(0!==e||0!==t?e+t:O(d.left,d.right)):R=v-2*(0!==n||0!==r?n+r:O(d.top,d.bottom))}await c({...t,availableWidth:C,availableHeight:R});let T=await u.getDimensions(s.floating);return g!==T.width||v!==T.height?{reset:{rects:!0}}:{}}}}(e),options:[e,t]}))({...ef,apply:e=>{let{elements:t,rects:n,availableWidth:r,availableHeight:o}=e,{width:i,height:l}=n.reference,a=t.floating.style;a.setProperty("--radix-popper-available-width","".concat(r,"px")),a.setProperty("--radix-popper-available-height","".concat(o,"px")),a.setProperty("--radix-popper-anchor-width","".concat(i,"px")),a.setProperty("--radix-popper-anchor-height","".concat(l,"px"))}}),en&&((e,t)=>({...(e=>({name:"arrow",options:e,fn(t){let{element:n,padding:r}="function"==typeof e?e(t):e;return n&&({}).hasOwnProperty.call(n,"current")?null!=n.current?ez({element:n.current,padding:r}).fn(t):{}:n?ez({element:n,padding:r}).fn(t):{}}}))(e),options:[e,t]}))({element:en,padding:b}),tt({arrowWidth:el,arrowHeight:ea}),S&&((e,t)=>({...function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){let{rects:n}=t,{strategy:r="referenceHidden",...o}=k(e,t);switch(r){case"referenceHidden":{let e=J(await G(t,{...o,elementContext:"reference"}),n.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:K(e)}}}case"escaped":{let e=J(await G(t,{...o,altBoundary:!0}),n.floating);return{data:{escapedOffsets:e,escaped:K(e)}}}default:return{}}}}}(e),options:[e,t]}))({strategy:"referenceHidden",...ef})]}),[ev,ey]=tn(em),ew=g(D);E(()=>{eh&&(null==ew||ew())},[eh,ew]);let ex=null==(n=eg.arrow)?void 0:n.x,eb=null==(r=eg.arrow)?void 0:r.y,eE=(null==(i=eg.arrow)?void 0:i.centerOffset)!==0,[eR,eT]=o.useState();return E(()=>{X&&eT(window.getComputedStyle(X).zIndex)},[X]),(0,c.jsx)("div",{ref:ed.setFloating,"data-radix-popper-content-wrapper":"",style:{...ep,transform:eh?ep.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:eR,"--radix-popper-transform-origin":[null==(l=eg.transformOrigin)?void 0:l.x,null==(a=eg.transformOrigin)?void 0:a.y].join(" "),...(null==(u=eg.hide)?void 0:u.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,c.jsx)(e3,{scope:m,placedSide:ev,onArrowChange:er,arrowX:ex,arrowY:eb,shouldHideArrow:eE,children:(0,c.jsx)(h.div,{"data-side":ev,"data-align":ey,...W,ref:et,style:{...W.style,animation:eh?void 0:"none"}})})})});e7.displayName=e5;var e8="PopperArrow",e9={top:"bottom",right:"left",bottom:"top",left:"right"},e4=o.forwardRef(function(e,t){let{__scopePopper:n,...r}=e,o=e6(e8,n),i=e9[o.placedSide];return(0,c.jsx)("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[i]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:(0,c.jsx)(eq,{...r,ref:t,style:{...r.style,display:"block"}})})});function te(e){return null!==e}e4.displayName=e8;var tt=e=>({name:"transformOrigin",options:e,fn(t){var n,r,o,i,l;let{placement:a,rects:u,middlewareData:s}=t,c=(null==(n=s.arrow)?void 0:n.centerOffset)!==0,f=c?0:e.arrowWidth,d=c?0:e.arrowHeight,[p,m]=tn(a),h={start:"0%",center:"50%",end:"100%"}[m],g=(null!=(i=null==(r=s.arrow)?void 0:r.x)?i:0)+f/2,v=(null!=(l=null==(o=s.arrow)?void 0:o.y)?l:0)+d/2,y="",w="";return"bottom"===p?(y=c?h:"".concat(g,"px"),w="".concat(-d,"px")):"top"===p?(y=c?h:"".concat(g,"px"),w="".concat(u.floating.height+d,"px")):"right"===p?(y="".concat(-d,"px"),w=c?h:"".concat(v,"px")):"left"===p&&(y="".concat(u.floating.width+d,"px"),w=c?h:"".concat(v,"px")),{data:{x:y,y:w}}}});function tn(e){let[t,n="center"]=e.split("-");return[t,n]}o.forwardRef((e,t)=>{var n,r;let{container:i,...l}=e,[a,u]=o.useState(!1);E(()=>u(!0),[]);let s=i||a&&(null==(r=globalThis)||null==(n=r.document)?void 0:n.body);return s?d.createPortal((0,c.jsx)(h.div,{...l,ref:t}),s):null}).displayName="Portal";var tr=e=>{let{present:t,children:n}=e,r=function(e){var t,n;let[r,i]=o.useState(),l=o.useRef(null),a=o.useRef(e),u=o.useRef("none"),[s,c]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},o.useReducer((e,t)=>{let r=n[e][t];return null!=r?r:e},t));return o.useEffect(()=>{let e=to(l.current);u.current="mounted"===s?e:"none"},[s]),E(()=>{let t=l.current,n=a.current;if(n!==e){let r=u.current,o=to(t);e?c("MOUNT"):"none"===o||(null==t?void 0:t.display)==="none"?c("UNMOUNT"):n&&r!==o?c("ANIMATION_OUT"):c("UNMOUNT"),a.current=e}},[e,c]),E(()=>{if(r){var e;let t,n=null!=(e=r.ownerDocument.defaultView)?e:window,o=e=>{let o=to(l.current).includes(e.animationName);if(e.target===r&&o&&(c("ANIMATION_END"),!a.current)){let e=r.style.animationFillMode;r.style.animationFillMode="forwards",t=n.setTimeout(()=>{"forwards"===r.style.animationFillMode&&(r.style.animationFillMode=e)})}},i=e=>{e.target===r&&(u.current=to(l.current))};return r.addEventListener("animationstart",i),r.addEventListener("animationcancel",o),r.addEventListener("animationend",o),()=>{n.clearTimeout(t),r.removeEventListener("animationstart",i),r.removeEventListener("animationcancel",o),r.removeEventListener("animationend",o)}}c("ANIMATION_END")},[r,c]),{isPresent:["mounted","unmountSuspended"].includes(s),ref:o.useCallback(e=>{l.current=e?getComputedStyle(e):null,i(e)},[])}}(t),i="function"==typeof n?n({present:r.isPresent}):o.Children.only(n),l=s(r.ref,function(e){var t,n;let r=null==(t=Object.getOwnPropertyDescriptor(e.props,"ref"))?void 0:t.get,o=r&&"isReactWarning"in r&&r.isReactWarning;return o?e.ref:(o=(r=null==(n=Object.getOwnPropertyDescriptor(e,"ref"))?void 0:n.get)&&"isReactWarning"in r&&r.isReactWarning)?e.props.ref:e.props.ref||e.ref}(i));return"function"==typeof n||r.isPresent?o.cloneElement(i,{ref:l}):null};function to(e){return(null==e?void 0:e.animationName)||"none"}tr.displayName="Presence";var ti=i[" useInsertionEffect ".trim().toString()]||E;Symbol("RADIX:SYNC_STATE");var tl=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),ta=o.forwardRef((e,t)=>(0,c.jsx)(h.span,{...e,ref:t,style:{...tl,...e.style}}));ta.displayName="VisuallyHidden";var[tu,ts]=f("Tooltip",[eJ]),tc=eJ(),tf="TooltipProvider",td="tooltip.open",[tp,tm]=tu(tf),th=e=>{let{__scopeTooltip:t,delayDuration:n=700,skipDelayDuration:r=300,disableHoverableContent:i=!1,children:l}=e,a=o.useRef(!0),u=o.useRef(!1),s=o.useRef(0);return o.useEffect(()=>{let e=s.current;return()=>window.clearTimeout(e)},[]),(0,c.jsx)(tp,{scope:t,isOpenDelayedRef:a,delayDuration:n,onOpen:o.useCallback(()=>{window.clearTimeout(s.current),a.current=!1},[]),onClose:o.useCallback(()=>{window.clearTimeout(s.current),s.current=window.setTimeout(()=>a.current=!0,r)},[r]),isPointerInTransitRef:u,onPointerInTransitChange:o.useCallback(e=>{u.current=e},[]),disableHoverableContent:i,children:l})};th.displayName=tf;var tg="Tooltip",[tv,ty]=tu(tg),tw=e=>{let{__scopeTooltip:t,children:n,open:r,defaultOpen:i,onOpenChange:l,disableHoverableContent:a,delayDuration:u}=e,s=tm(tg,e.__scopeTooltip),f=tc(t),[d,p]=o.useState(null),m=function(e){let[t,n]=o.useState(R());return E(()=>{n(e=>e??String(C++))},[void 0]),e||(t?`radix-${t}`:"")}(),h=o.useRef(0),g=null!=a?a:s.disableHoverableContent,v=null!=u?u:s.delayDuration,y=o.useRef(!1),[w,x]=function({prop:e,defaultProp:t,onChange:n=()=>{},caller:r}){let[i,l,a]=function({defaultProp:e,onChange:t}){let[n,r]=o.useState(e),i=o.useRef(n),l=o.useRef(t);return ti(()=>{l.current=t},[t]),o.useEffect(()=>{i.current!==n&&(l.current?.(n),i.current=n)},[n,i]),[n,r,l]}({defaultProp:t,onChange:n}),u=void 0!==e,s=u?e:i;{let t=o.useRef(void 0!==e);o.useEffect(()=>{let e=t.current;if(e!==u){let t=u?"controlled":"uncontrolled";console.warn(`${r} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=u},[u,r])}return[s,o.useCallback(t=>{if(u){let n="function"==typeof t?t(e):t;n!==e&&a.current?.(n)}else l(t)},[u,e,l,a])]}({prop:r,defaultProp:null!=i&&i,onChange:e=>{e?(s.onOpen(),document.dispatchEvent(new CustomEvent(td))):s.onClose(),null==l||l(e)},caller:tg}),b=o.useMemo(()=>w?y.current?"delayed-open":"instant-open":"closed",[w]),T=o.useCallback(()=>{window.clearTimeout(h.current),h.current=0,y.current=!1,x(!0)},[x]),L=o.useCallback(()=>{window.clearTimeout(h.current),h.current=0,x(!1)},[x]),O=o.useCallback(()=>{window.clearTimeout(h.current),h.current=window.setTimeout(()=>{y.current=!0,x(!0),h.current=0},v)},[v,x]);return o.useEffect(()=>()=>{h.current&&(window.clearTimeout(h.current),h.current=0)},[]),(0,c.jsx)(e0,{...f,children:(0,c.jsx)(tv,{scope:t,contentId:m,open:w,stateAttribute:b,trigger:d,onTriggerChange:p,onTriggerEnter:o.useCallback(()=>{s.isOpenDelayedRef.current?O():T()},[s.isOpenDelayedRef,O,T]),onTriggerLeave:o.useCallback(()=>{g?L():(window.clearTimeout(h.current),h.current=0)},[L,g]),onOpen:T,onClose:L,disableHoverableContent:g,children:n})})};tw.displayName=tg;var tx="TooltipTrigger",tb=o.forwardRef((e,t)=>{let{__scopeTooltip:n,...r}=e,i=ty(tx,n),a=tm(tx,n),u=tc(n),f=s(t,o.useRef(null),i.onTriggerChange),d=o.useRef(!1),p=o.useRef(!1),m=o.useCallback(()=>d.current=!1,[]);return o.useEffect(()=>()=>document.removeEventListener("pointerup",m),[m]),(0,c.jsx)(e2,{asChild:!0,...u,children:(0,c.jsx)(h.button,{"aria-describedby":i.open?i.contentId:void 0,"data-state":i.stateAttribute,...r,ref:f,onPointerMove:l(e.onPointerMove,e=>{"touch"!==e.pointerType&&(p.current||a.isPointerInTransitRef.current||(i.onTriggerEnter(),p.current=!0))}),onPointerLeave:l(e.onPointerLeave,()=>{i.onTriggerLeave(),p.current=!1}),onPointerDown:l(e.onPointerDown,()=>{i.open&&i.onClose(),d.current=!0,document.addEventListener("pointerup",m,{once:!0})}),onFocus:l(e.onFocus,()=>{d.current||i.onOpen()}),onBlur:l(e.onBlur,i.onClose),onClick:l(e.onClick,i.onClose)})})});tb.displayName=tx;var[tE,tR]=tu("TooltipPortal",{forceMount:void 0}),tC="TooltipContent",tT=o.forwardRef((e,t)=>{let n=tR(tC,e.__scopeTooltip),{forceMount:r=n.forceMount,side:o="top",...i}=e,l=ty(tC,e.__scopeTooltip);return(0,c.jsx)(tr,{present:r||l.open,children:l.disableHoverableContent?(0,c.jsx)(tS,{side:o,...i,ref:t}):(0,c.jsx)(tL,{side:o,...i,ref:t})})}),tL=o.forwardRef((e,t)=>{let n=ty(tC,e.__scopeTooltip),r=tm(tC,e.__scopeTooltip),i=o.useRef(null),l=s(t,i),[a,u]=o.useState(null),{trigger:f,onClose:d}=n,p=i.current,{onPointerInTransitChange:m}=r,h=o.useCallback(()=>{u(null),m(!1)},[m]),g=o.useCallback((e,t)=>{let n=e.currentTarget,r={x:e.clientX,y:e.clientY},o=function(e,t){let n=Math.abs(t.top-e.y),r=Math.abs(t.bottom-e.y),o=Math.abs(t.right-e.x),i=Math.abs(t.left-e.x);switch(Math.min(n,r,o,i)){case i:return"left";case o:return"right";case n:return"top";case r:return"bottom";default:throw Error("unreachable")}}(r,n.getBoundingClientRect());u(function(e){let t=e.slice();return t.sort((e,t)=>e.x<t.x?-1:e.x>t.x?1:e.y<t.y?-1:1*!!(e.y>t.y)),function(e){if(e.length<=1)return e.slice();let t=[];for(let n=0;n<e.length;n++){let r=e[n];for(;t.length>=2;){let e=t[t.length-1],n=t[t.length-2];if((e.x-n.x)*(r.y-n.y)>=(e.y-n.y)*(r.x-n.x))t.pop();else break}t.push(r)}t.pop();let n=[];for(let t=e.length-1;t>=0;t--){let r=e[t];for(;n.length>=2;){let e=n[n.length-1],t=n[n.length-2];if((e.x-t.x)*(r.y-t.y)>=(e.y-t.y)*(r.x-t.x))n.pop();else break}n.push(r)}return(n.pop(),1===t.length&&1===n.length&&t[0].x===n[0].x&&t[0].y===n[0].y)?t:t.concat(n)}(t)}([...function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:5,r=[];switch(t){case"top":r.push({x:e.x-n,y:e.y+n},{x:e.x+n,y:e.y+n});break;case"bottom":r.push({x:e.x-n,y:e.y-n},{x:e.x+n,y:e.y-n});break;case"left":r.push({x:e.x+n,y:e.y-n},{x:e.x+n,y:e.y+n});break;case"right":r.push({x:e.x-n,y:e.y-n},{x:e.x-n,y:e.y+n})}return r}(r,o),...function(e){let{top:t,right:n,bottom:r,left:o}=e;return[{x:o,y:t},{x:n,y:t},{x:n,y:r},{x:o,y:r}]}(t.getBoundingClientRect())])),m(!0)},[m]);return o.useEffect(()=>()=>h(),[h]),o.useEffect(()=>{if(f&&p){let e=e=>g(e,p),t=e=>g(e,f);return f.addEventListener("pointerleave",e),p.addEventListener("pointerleave",t),()=>{f.removeEventListener("pointerleave",e),p.removeEventListener("pointerleave",t)}}},[f,p,g,h]),o.useEffect(()=>{if(a){let e=e=>{let t=e.target,n={x:e.clientX,y:e.clientY},r=(null==f?void 0:f.contains(t))||(null==p?void 0:p.contains(t)),o=!function(e,t){let{x:n,y:r}=e,o=!1;for(let e=0,i=t.length-1;e<t.length;i=e++){let l=t[e],a=t[i],u=l.x,s=l.y,c=a.x,f=a.y;s>r!=f>r&&n<(c-u)*(r-s)/(f-s)+u&&(o=!o)}return o}(n,a);r?h():o&&(h(),d())};return document.addEventListener("pointermove",e),()=>document.removeEventListener("pointermove",e)}},[f,p,a,d,h]),(0,c.jsx)(tS,{...e,ref:l})}),[tO,tP]=tu(tg,{isInside:!1}),tA=function(e){let t=({children:e})=>(0,c.jsx)(c.Fragment,{children:e});return t.displayName=`${e}.Slottable`,t.__radixId=p,t}("TooltipContent"),tS=o.forwardRef((e,t)=>{let{__scopeTooltip:n,children:r,"aria-label":i,onEscapeKeyDown:l,onPointerDownOutside:a,...u}=e,s=ty(tC,n),f=tc(n),{onClose:d}=s;return o.useEffect(()=>(document.addEventListener(td,d),()=>document.removeEventListener(td,d)),[d]),o.useEffect(()=>{if(s.trigger){let e=e=>{let t=e.target;(null==t?void 0:t.contains(s.trigger))&&d()};return window.addEventListener("scroll",e,{capture:!0}),()=>window.removeEventListener("scroll",e,{capture:!0})}},[s.trigger,d]),(0,c.jsx)(w,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:l,onPointerDownOutside:a,onFocusOutside:e=>e.preventDefault(),onDismiss:d,children:(0,c.jsxs)(e7,{"data-state":s.stateAttribute,...f,...u,ref:t,style:{...u.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[(0,c.jsx)(tA,{children:r}),(0,c.jsx)(tO,{scope:n,isInside:!0,children:(0,c.jsx)(ta,{id:s.contentId,role:"tooltip",children:i||r})})]})})});tT.displayName=tC;var tN="TooltipArrow";o.forwardRef((e,t)=>{let{__scopeTooltip:n,...r}=e,o=tc(n);return tP(tN,n).isInside?null:(0,c.jsx)(e4,{...o,...r,ref:t})}).displayName=tN;var tD=th,tk=tw,tj=tb,tM=tT}}]);