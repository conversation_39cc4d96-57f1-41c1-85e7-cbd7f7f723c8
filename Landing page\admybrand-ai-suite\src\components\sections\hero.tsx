'use client'

import { MorphingShape, ParticleField } from '@/components/ui/advanced-animations'
import { Button } from '@/components/ui/button'
import { motion } from 'framer-motion'
import { Sparkles } from 'lucide-react'

const Hero = () => {
  return (
    <section className="relative min-h-screen bg-gradient-to-br from-black via-gray-900 to-black flex flex-col overflow-hidden">
      {/* Advanced animated background elements */}
      <div className="absolute inset-0 overflow-hidden">
        <ParticleField />
        <div className="absolute top-1/4 left-1/4">
          <MorphingShape />
        </div>
        <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[800px] h-[800px] bg-gradient-conic from-blue-500/5 via-purple-500/5 to-blue-500/5 rounded-full blur-3xl animate-spin-slow"></div>
      </div>

      {/* Image Section - Modern Glass Card */}
      <div className="flex items-center justify-center px-2 sm:px-4 lg:px-6 pt-32 pb-4 relative z-10">
        <motion.div
          initial={{ opacity: 0, scale: 0.9, y: 20 }}
          animate={{ opacity: 1, scale: 1, y: 0 }}
          transition={{ duration: 1, ease: "easeOut" }}
          className="relative w-full max-w-7xl h-[45vh] rounded-2xl overflow-hidden shadow-2xl border border-white/10 bg-white/5 backdrop-blur-xl"
        >
          <div
            className="absolute inset-0 bg-cover bg-center bg-no-repeat"
            style={{
              backgroundImage: "url('/office-background.jpg')",
            }}
          />
          {/* Modern gradient overlay */}
          <div className="absolute inset-0 bg-gradient-to-br from-black/30 via-transparent to-purple-900/20" />


        </motion.div>
      </div>

      {/* Modern floating notification cards */}
      <motion.div
        className="absolute top-24 left-[15%] bg-gradient-to-r from-blue-500/20 to-purple-500/20 backdrop-blur-md rounded-xl px-4 py-3 text-sm text-white border border-white/20 z-20 shadow-lg"
        animate={{
          y: [0, -8, 0],
          opacity: [0.8, 1, 0.8],
          scale: [1, 1.02, 1],
        }}
        transition={{
          duration: 3,
          repeat: Infinity,
          repeatType: "reverse",
        }}
      >
        <div className="flex items-center gap-2">
          <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
          <span className="font-medium">27 menus analyzed</span>
        </div>
      </motion.div>

      <motion.div
        className="absolute top-32 right-[15%] bg-gradient-to-r from-purple-500/20 to-pink-500/20 backdrop-blur-md rounded-xl px-4 py-3 text-sm text-white border border-white/20 z-20 shadow-lg"
        animate={{
          y: [0, 8, 0],
          opacity: [0.8, 1, 0.8],
          scale: [1, 1.02, 1],
        }}
        transition={{
          duration: 4,
          repeat: Infinity,
          repeatType: "reverse",
          delay: 1,
        }}
      >
        <div className="flex items-center gap-2">
          <div className="w-2 h-2 bg-orange-400 rounded-full animate-pulse"></div>
          <span className="font-medium">4 churn risks detected</span>
        </div>
      </motion.div>

      <motion.div
        className="absolute top-40 left-[20%] bg-gradient-to-r from-green-500/20 to-blue-500/20 backdrop-blur-md rounded-xl px-4 py-3 text-sm text-white border border-white/20 z-20 shadow-lg"
        animate={{
          y: [0, -6, 0],
          opacity: [0.8, 1, 0.8],
          scale: [1, 1.02, 1],
        }}
        transition={{
          duration: 5,
          repeat: Infinity,
          repeatType: "reverse",
          delay: 2,
        }}
      >
        <div className="flex items-center gap-2">
          <div className="w-2 h-2 bg-blue-400 rounded-full animate-pulse"></div>
          <span className="font-medium">13 emails automated</span>
        </div>
      </motion.div>

      <motion.div
        className="absolute top-48 right-[20%] bg-gradient-to-r from-yellow-500/20 to-orange-500/20 backdrop-blur-md rounded-xl px-4 py-3 text-sm text-white border border-white/20 z-20 shadow-lg"
        animate={{
          y: [0, 10, 0],
          opacity: [0.8, 1, 0.8],
          scale: [1, 1.02, 1],
        }}
        transition={{
          duration: 3.5,
          repeat: Infinity,
          repeatType: "reverse",
          delay: 0.5,
        }}
      >
        <div className="flex items-center gap-2">
          <div className="w-2 h-2 bg-purple-400 rounded-full animate-pulse"></div>
          <span className="font-medium">34 orders processed</span>
        </div>
      </motion.div>

      {/* Text Content Section Below Image */}
      <div className="flex-1 flex flex-col justify-center px-2 sm:px-4 lg:px-6 pb-8 relative z-10">
        <div className="max-w-7xl mx-auto text-center">
          {/* Badge */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="inline-flex items-center gap-2 bg-white/10 backdrop-blur-md border border-white/20 rounded-full px-4 py-2 mb-6"
          >
            <Sparkles className="w-4 h-4 text-yellow-400" />
            <span className="text-white text-sm font-medium">Next-Gen AI Marketing</span>
          </motion.div>

          {/* Main Heading with Gradient Text */}
          <motion.h1
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="text-3xl md:text-5xl lg:text-6xl font-bold mb-6 leading-tight"
          >
            <span className="text-white">All-in-One AI Marketing Suite</span>
            <br />
            <span className="bg-gradient-to-r from-blue-400 via-purple-400 to-pink-400 bg-clip-text text-transparent">
              to Grow Your Brand
            </span>
          </motion.h1>

          {/* Subtitle */}
          <motion.p
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="text-lg md:text-xl text-gray-300 mb-8 max-w-2xl mx-auto leading-relaxed"
          >
            Automate content, campaigns, and customer insights — all in one
            <span className="text-blue-400 font-semibold"> intelligent marketing platform</span>.
          </motion.p>

          {/* CTA Buttons */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.6 }}
            className="flex flex-col sm:flex-row gap-4 justify-center items-center"
          >
            <Button
              size="lg"
              className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-8 py-4 text-lg font-semibold rounded-xl shadow-2xl transition-all duration-300 transform hover:scale-105 hover:shadow-blue-500/25 border border-white/10"
            >
              <span className="flex items-center gap-2">
                BOOK A DEMO
                <motion.div
                  animate={{ x: [0, 4, 0] }}
                  transition={{ duration: 1.5, repeat: Infinity }}
                >
                  →
                </motion.div>
              </span>
            </Button>
            <Button
              variant="outline"
              size="lg"
              className="border-white/20 text-white hover:bg-white/10 backdrop-blur-md px-8 py-4 text-lg font-semibold rounded-xl transition-all duration-300 hover:border-white/30"
            >
              Get Started Free
            </Button>
          </motion.div>

          {/* Trust indicators */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.8, delay: 0.8 }}
            className="mt-8 flex flex-wrap justify-center items-center gap-6 text-sm text-gray-400"
          >
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-green-400 rounded-full"></div>
              <span>No credit card required</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
              <span>14-day free trial</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-purple-400 rounded-full"></div>
              <span>Setup in 5 minutes</span>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  )
}

export default Hero
