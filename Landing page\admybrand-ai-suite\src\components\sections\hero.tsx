'use client'

import { Button } from '@/components/ui/button'
import { motion } from 'framer-motion'

const Hero = () => {
  return (
    <section className="relative min-h-screen bg-black flex flex-col">
      {/* Image Section - Clean and Centered */}
      <div className="flex items-center justify-center px-2 sm:px-4 lg:px-6 pt-24 pb-4">
        <div className="relative w-full max-w-7xl h-[45vh] rounded-xl overflow-hidden shadow-2xl">
          <div
            className="absolute inset-0 bg-cover bg-center bg-no-repeat"
            style={{
              backgroundImage: "url('/office-background.jpg')",
            }}
          />
          {/* Subtle overlay for better contrast */}
          <div className="absolute inset-0 bg-black/20" />
        </div>
      </div>

      {/* Floating notification cards positioned around the image */}
      <motion.div
        className="absolute top-16 left-[15%] bg-gray-800/70 backdrop-blur-sm rounded-lg px-3 py-2 text-xs text-gray-300 border border-gray-600/50 z-20"
        animate={{
          y: [0, -8, 0],
          opacity: [0.6, 1, 0.6],
        }}
        transition={{
          duration: 3,
          repeat: Infinity,
          repeatType: "reverse",
        }}
      >
        scanned 27 menus
      </motion.div>

      <motion.div
        className="absolute top-24 right-[15%] bg-gray-800/70 backdrop-blur-sm rounded-lg px-3 py-2 text-xs text-gray-300 border border-gray-600/50 z-20"
        animate={{
          y: [0, 8, 0],
          opacity: [0.6, 1, 0.6],
        }}
        transition={{
          duration: 4,
          repeat: Infinity,
          repeatType: "reverse",
          delay: 1,
        }}
      >
        detected 4 customers churning
      </motion.div>

      <motion.div
        className="absolute top-32 left-[20%] bg-gray-800/70 backdrop-blur-sm rounded-lg px-3 py-2 text-xs text-gray-300 border border-gray-600/50 z-20"
        animate={{
          y: [0, -6, 0],
          opacity: [0.6, 1, 0.6],
        }}
        transition={{
          duration: 5,
          repeat: Infinity,
          repeatType: "reverse",
          delay: 2,
        }}
      >
        replied to 13 emails
      </motion.div>

      <motion.div
        className="absolute top-40 right-[20%] bg-gray-800/70 backdrop-blur-sm rounded-lg px-3 py-2 text-xs text-gray-300 border border-gray-600/50 z-20"
        animate={{
          y: [0, 10, 0],
          opacity: [0.6, 1, 0.6],
        }}
        transition={{
          duration: 3.5,
          repeat: Infinity,
          repeatType: "reverse",
          delay: 0.5,
        }}
      >
        processed 34 orders
      </motion.div>

      {/* Text Content Section Below Image */}
      <div className="flex-1 flex flex-col justify-center px-2 sm:px-4 lg:px-6 pb-8">
        <div className="max-w-7xl mx-auto text-center">
          {/* Main Heading */}
          <motion.h1
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="text-3xl md:text-5xl lg:text-6xl font-bold text-white mb-3 leading-tight"
          >
            <span className="text-white">All-in-One AI Marketing Suite</span>
            <br />
            <span className="text-blue-400">to Grow Your Brand</span>
          </motion.h1>

          {/* Subtitle */}
          <motion.p
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="text-lg md:text-xl text-gray-300 mb-6 max-w-2xl mx-auto leading-relaxed"
          >
            Automate content, campaigns, and customer insights — all in one intelligent marketing platform.
          </motion.p>

          {/* CTA Buttons */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.6 }}
            className="flex flex-col sm:flex-row gap-4 justify-center items-center"
          >
            <Button
              size="lg"
              className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-4 text-lg font-semibold rounded-lg shadow-2xl transition-all duration-300 transform hover:scale-105"
            >
              BOOK A DEMO
            </Button>
            <Button
              variant="outline"
              size="lg"
              className="border-gray-600 text-gray-300 hover:bg-gray-800 px-8 py-4 text-lg font-semibold rounded-lg transition-all duration-300"
            >
              Get Started
            </Button>
          </motion.div>
        </div>
      </div>
    </section>
  )
}

export default Hero
