{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Landing%20page/admybrand-ai-suite/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatPrice(price: number): string {\n  return new Intl.NumberFormat('en-US', {\n    style: 'currency',\n    currency: 'USD',\n  }).format(price)\n}\n\nexport function formatNumber(num: number): string {\n  return new Intl.NumberFormat('en-US').format(num)\n}\n\nexport function slugify(text: string): string {\n  return text\n    .toLowerCase()\n    .replace(/[^\\w ]+/g, '')\n    .replace(/ +/g, '-')\n}\n\nexport function truncate(text: string, length: number): string {\n  if (text.length <= length) return text\n  return text.slice(0, length) + '...'\n}\n\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout)\n    timeout = setTimeout(() => func(...args), wait)\n  }\n}\n\nexport function throttle<T extends (...args: any[]) => any>(\n  func: T,\n  limit: number\n): (...args: Parameters<T>) => void {\n  let inThrottle: boolean\n  return (...args: Parameters<T>) => {\n    if (!inThrottle) {\n      func(...args)\n      inThrottle = true\n      setTimeout(() => (inThrottle = false), limit)\n    }\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;AAEO,SAAS;IAAG,IAAA,IAAA,OAAA,UAAA,QAAA,AAAG,SAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAG,OAAH,QAAA,SAAA,CAAA,KAAuB;;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,YAAY,KAAa;IACvC,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU;IACZ,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,aAAa,GAAW;IACtC,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS,MAAM,CAAC;AAC/C;AAEO,SAAS,QAAQ,IAAY;IAClC,OAAO,KACJ,WAAW,GACX,OAAO,CAAC,YAAY,IACpB,OAAO,CAAC,OAAO;AACpB;AAEO,SAAS,SAAS,IAAY,EAAE,MAAc;IACnD,IAAI,KAAK,MAAM,IAAI,QAAQ,OAAO;IAClC,OAAO,KAAK,KAAK,CAAC,GAAG,UAAU;AACjC;AAEO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IACJ,OAAO;yCAAI;YAAA;;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAEO,SAAS,SACd,IAAO,EACP,KAAa;IAEb,IAAI;IACJ,OAAO;yCAAI;YAAA;;QACT,IAAI,CAAC,YAAY;YACf,QAAQ;YACR,aAAa;YACb,WAAW,IAAO,aAAa,OAAQ;QACzC;IACF;AACF", "debugId": null}}, {"offset": {"line": 74, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Landing%20page/admybrand-ai-suite/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n        gradient: \"bg-gradient-to-r from-purple-600 to-blue-600 text-white hover:from-purple-700 hover:to-blue-700 shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200\",\n        glass: \"glass text-white hover:bg-white/20 backdrop-blur-md border border-white/20\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        xl: \"h-12 rounded-lg px-10 text-base\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n  loading?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, loading = false, children, disabled, ...props }, ref) => {\n    return (\n      <button\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        disabled={disabled || loading}\n        {...props}\n      >\n        {loading && (\n          <svg\n            className=\"animate-spin -ml-1 mr-2 h-4 w-4\"\n            xmlns=\"http://www.w3.org/2000/svg\"\n            fill=\"none\"\n            viewBox=\"0 0 24 24\"\n          >\n            <circle\n              className=\"opacity-25\"\n              cx=\"12\"\n              cy=\"12\"\n              r=\"10\"\n              stroke=\"currentColor\"\n              strokeWidth=\"4\"\n            ></circle>\n            <path\n              className=\"opacity-75\"\n              fill=\"currentColor\"\n              d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n            ></path>\n          </svg>\n        )}\n        {children}\n      </button>\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,0RACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;YACN,UAAU;YACV,OAAO;QACT;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAUF,MAAM,uBAAS,6JAAA,CAAA,aAAgB,MAC7B,QAA+F;QAA9F,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,UAAU,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,OAAO;IAC3F,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACL,UAAU,YAAY;QACrB,GAAG,KAAK;;YAER,yBACC,6LAAC;gBACC,WAAU;gBACV,OAAM;gBACN,MAAK;gBACL,SAAQ;;kCAER,6LAAC;wBACC,WAAU;wBACV,IAAG;wBACH,IAAG;wBACH,GAAE;wBACF,QAAO;wBACP,aAAY;;;;;;kCAEd,6LAAC;wBACC,WAAU;wBACV,MAAK;wBACL,GAAE;;;;;;;;;;;;YAIP;;;;;;;AAGP;;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 179, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Landing%20page/admybrand-ai-suite/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst inputVariants = cva(\n  \"flex w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n  {\n    variants: {\n      variant: {\n        default: \"\",\n        glass: \"glass border-white/20 backdrop-blur-md placeholder:text-white/70\",\n        filled: \"bg-muted border-transparent focus-visible:bg-background\",\n      },\n      inputSize: {\n        default: \"h-10\",\n        sm: \"h-9 px-2 text-xs\",\n        lg: \"h-11 px-4\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      inputSize: \"default\",\n    },\n  }\n)\n\nexport interface InputProps\n  extends React.InputHTMLAttributes<HTMLInputElement>,\n    VariantProps<typeof inputVariants> {\n  label?: string\n  error?: string\n  icon?: React.ReactNode\n}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ className, variant, inputSize, type, label, error, icon, ...props }, ref) => {\n    return (\n      <div className=\"w-full\">\n        {label && (\n          <label className=\"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 mb-2 block\">\n            {label}\n          </label>\n        )}\n        <div className=\"relative\">\n          {icon && (\n            <div className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground\">\n              {icon}\n            </div>\n          )}\n          <input\n            type={type}\n            className={cn(\n              inputVariants({ variant, inputSize }),\n              icon && \"pl-10\",\n              error && \"border-destructive focus-visible:ring-destructive\",\n              className\n            )}\n            ref={ref}\n            {...props}\n          />\n        </div>\n        {error && (\n          <p className=\"text-sm text-destructive mt-1\">{error}</p>\n        )}\n      </div>\n    )\n  }\n)\nInput.displayName = \"Input\"\n\nexport { Input, inputVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,2VACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,OAAO;YACP,QAAQ;QACV;QACA,WAAW;YACT,SAAS;YACT,IAAI;YACJ,IAAI;QACN;IACF;IACA,iBAAiB;QACf,SAAS;QACT,WAAW;IACb;AACF;AAWF,MAAM,sBAAQ,6JAAA,CAAA,aAAgB,MAC5B,QAAwE;QAAvE,EAAE,SAAS,EAAE,OAAO,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,OAAO;IACpE,qBACE,6LAAC;QAAI,WAAU;;YACZ,uBACC,6LAAC;gBAAM,WAAU;0BACd;;;;;;0BAGL,6LAAC;gBAAI,WAAU;;oBACZ,sBACC,6LAAC;wBAAI,WAAU;kCACZ;;;;;;kCAGL,6LAAC;wBACC,MAAM;wBACN,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,cAAc;4BAAE;4BAAS;wBAAU,IACnC,QAAQ,SACR,SAAS,qDACT;wBAEF,KAAK;wBACJ,GAAG,KAAK;;;;;;;;;;;;YAGZ,uBACC,6LAAC;gBAAE,WAAU;0BAAiC;;;;;;;;;;;;AAItD;;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 282, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Landing%20page/admybrand-ai-suite/src/components/layout/footer.tsx"], "sourcesContent": ["'use client'\n\nimport { But<PERSON> } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { motion } from 'framer-motion'\nimport {\n    ArrowRight,\n    Github,\n    Linkedin,\n    Mail,\n    MapPin,\n    Phone,\n    Sparkles,\n    Twitter,\n    Youtube\n} from 'lucide-react'\n\nconst footerLinks = {\n  product: [\n    { name: 'Features', href: '#features' },\n    { name: 'Pricing', href: '#pricing' },\n    { name: 'Integrations', href: '#integrations' },\n    { name: 'API Documentation', href: '#api' },\n    { name: 'Changelog', href: '#changelog' }\n  ],\n  company: [\n    { name: 'About Us', href: '#about' },\n    { name: 'Careers', href: '#careers' },\n    { name: 'Press Kit', href: '#press' },\n    { name: 'Contact', href: '#contact' },\n    { name: 'Partners', href: '#partners' }\n  ],\n  resources: [\n    { name: 'Blog', href: '#blog' },\n    { name: 'Help Center', href: '#help' },\n    { name: 'Community', href: '#community' },\n    { name: 'Webinars', href: '#webinars' },\n    { name: 'Case Studies', href: '#cases' }\n  ],\n  legal: [\n    { name: 'Privacy Policy', href: '#privacy' },\n    { name: 'Terms of Service', href: '#terms' },\n    { name: 'Cookie Policy', href: '#cookies' },\n    { name: 'GDPR', href: '#gdpr' },\n    { name: 'Security', href: '#security' }\n  ]\n}\n\nconst socialLinks = [\n  { name: 'Twitter', icon: Twitter, href: '#twitter' },\n  { name: 'LinkedIn', icon: Linkedin, href: '#linkedin' },\n  { name: 'GitHub', icon: Github, href: '#github' },\n  { name: 'YouTube', icon: Youtube, href: '#youtube' }\n]\n\nconst Footer = () => {\n  return (\n    <footer className=\"bg-gray-900 text-white\">\n      {/* Newsletter Section */}\n      <div className=\"border-b border-gray-800\">\n        <div className=\"max-w-7xl mx-auto px-2 sm:px-4 lg:px-6 py-16\">\n          <motion.div\n            initial={{ opacity: 0, y: 30 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8 }}\n            viewport={{ once: true }}\n            className=\"text-center max-w-2xl mx-auto\"\n          >\n            <div className=\"flex items-center justify-center mb-4\">\n              <Sparkles className=\"w-8 h-8 text-purple-400 mr-3\" />\n              <h3 className=\"text-3xl font-bold\">Stay Updated</h3>\n            </div>\n            <p className=\"text-gray-400 mb-8\">\n              Get the latest AI marketing insights, product updates, and exclusive tips delivered to your inbox.\n            </p>\n            <div className=\"flex flex-col sm:flex-row gap-4 max-w-md mx-auto\">\n              <Input\n                placeholder=\"Enter your email\"\n                className=\"flex-1 bg-gray-800 border-gray-700 text-white placeholder:text-gray-400\"\n              />\n              <Button variant=\"gradient\" className=\"group\">\n                Subscribe\n                <ArrowRight className=\"ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform\" />\n              </Button>\n            </div>\n            <p className=\"text-xs text-gray-500 mt-4\">\n              No spam. Unsubscribe at any time.\n            </p>\n          </motion.div>\n        </div>\n      </div>\n\n      {/* Main Footer Content */}\n      <div className=\"max-w-7xl mx-auto px-2 sm:px-4 lg:px-6 py-16\">\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-8\">\n          {/* Brand Section */}\n          <motion.div\n            initial={{ opacity: 0, y: 30 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8 }}\n            viewport={{ once: true }}\n            className=\"lg:col-span-2\"\n          >\n            <div className=\"flex items-center mb-4\">\n              <div className=\"w-10 h-10 bg-gradient-to-r from-purple-500 to-blue-500 rounded-lg flex items-center justify-center mr-3\">\n                <Sparkles className=\"w-6 h-6 text-white\" />\n              </div>\n              <span className=\"text-2xl font-bold\">ADmyBRAND</span>\n            </div>\n            <p className=\"text-gray-400 mb-6 max-w-sm\">\n              Transform your marketing with AI-powered tools that create, optimize, and scale your campaigns automatically.\n            </p>\n            \n            {/* Contact Info */}\n            <div className=\"space-y-3\">\n              <div className=\"flex items-center text-gray-400\">\n                <Mail className=\"w-4 h-4 mr-3\" />\n                <span className=\"text-sm\"><EMAIL></span>\n              </div>\n              <div className=\"flex items-center text-gray-400\">\n                <Phone className=\"w-4 h-4 mr-3\" />\n                <span className=\"text-sm\">+****************</span>\n              </div>\n              <div className=\"flex items-center text-gray-400\">\n                <MapPin className=\"w-4 h-4 mr-3\" />\n                <span className=\"text-sm\">San Francisco, CA</span>\n              </div>\n            </div>\n          </motion.div>\n\n          {/* Product Links */}\n          <motion.div\n            initial={{ opacity: 0, y: 30 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8, delay: 0.1 }}\n            viewport={{ once: true }}\n          >\n            <h4 className=\"text-lg font-semibold mb-4\">Product</h4>\n            <ul className=\"space-y-3\">\n              {footerLinks.product.map((link) => (\n                <li key={link.name}>\n                  <a\n                    href={link.href}\n                    className=\"text-gray-400 hover:text-white transition-colors duration-200 text-sm\"\n                  >\n                    {link.name}\n                  </a>\n                </li>\n              ))}\n            </ul>\n          </motion.div>\n\n          {/* Company Links */}\n          <motion.div\n            initial={{ opacity: 0, y: 30 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8, delay: 0.2 }}\n            viewport={{ once: true }}\n          >\n            <h4 className=\"text-lg font-semibold mb-4\">Company</h4>\n            <ul className=\"space-y-3\">\n              {footerLinks.company.map((link) => (\n                <li key={link.name}>\n                  <a\n                    href={link.href}\n                    className=\"text-gray-400 hover:text-white transition-colors duration-200 text-sm\"\n                  >\n                    {link.name}\n                  </a>\n                </li>\n              ))}\n            </ul>\n          </motion.div>\n\n          {/* Resources Links */}\n          <motion.div\n            initial={{ opacity: 0, y: 30 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8, delay: 0.3 }}\n            viewport={{ once: true }}\n          >\n            <h4 className=\"text-lg font-semibold mb-4\">Resources</h4>\n            <ul className=\"space-y-3\">\n              {footerLinks.resources.map((link) => (\n                <li key={link.name}>\n                  <a\n                    href={link.href}\n                    className=\"text-gray-400 hover:text-white transition-colors duration-200 text-sm\"\n                  >\n                    {link.name}\n                  </a>\n                </li>\n              ))}\n            </ul>\n          </motion.div>\n\n          {/* Legal Links */}\n          <motion.div\n            initial={{ opacity: 0, y: 30 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8, delay: 0.4 }}\n            viewport={{ once: true }}\n          >\n            <h4 className=\"text-lg font-semibold mb-4\">Legal</h4>\n            <ul className=\"space-y-3\">\n              {footerLinks.legal.map((link) => (\n                <li key={link.name}>\n                  <a\n                    href={link.href}\n                    className=\"text-gray-400 hover:text-white transition-colors duration-200 text-sm\"\n                  >\n                    {link.name}\n                  </a>\n                </li>\n              ))}\n            </ul>\n          </motion.div>\n        </div>\n      </div>\n\n      {/* Bottom Section */}\n      <div className=\"border-t border-gray-800\">\n        <div className=\"max-w-7xl mx-auto px-2 sm:px-4 lg:px-6 py-8\">\n          <div className=\"flex flex-col md:flex-row justify-between items-center\">\n            <motion.div\n              initial={{ opacity: 0, x: -30 }}\n              whileInView={{ opacity: 1, x: 0 }}\n              transition={{ duration: 0.8 }}\n              viewport={{ once: true }}\n              className=\"text-gray-400 text-sm mb-4 md:mb-0\"\n            >\n              © 2025 ADmyBRAND AI Suite. All rights reserved.\n            </motion.div>\n\n            {/* Social Links */}\n            <motion.div\n              initial={{ opacity: 0, x: 30 }}\n              whileInView={{ opacity: 1, x: 0 }}\n              transition={{ duration: 0.8 }}\n              viewport={{ once: true }}\n              className=\"flex space-x-4\"\n            >\n              {socialLinks.map((social) => (\n                <a\n                  key={social.name}\n                  href={social.href}\n                  className=\"w-10 h-10 bg-gray-800 rounded-lg flex items-center justify-center hover:bg-gray-700 transition-colors duration-200 group\"\n                  aria-label={social.name}\n                >\n                  <social.icon className=\"w-5 h-5 text-gray-400 group-hover:text-white transition-colors duration-200\" />\n                </a>\n              ))}\n            </motion.div>\n          </div>\n        </div>\n      </div>\n    </footer>\n  )\n}\n\nexport default Footer\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AALA;;;;;;AAiBA,MAAM,cAAc;IAClB,SAAS;QACP;YAAE,MAAM;YAAY,MAAM;QAAY;QACtC;YAAE,MAAM;YAAW,MAAM;QAAW;QACpC;YAAE,MAAM;YAAgB,MAAM;QAAgB;QAC9C;YAAE,MAAM;YAAqB,MAAM;QAAO;QAC1C;YAAE,MAAM;YAAa,MAAM;QAAa;KACzC;IACD,SAAS;QACP;YAAE,MAAM;YAAY,MAAM;QAAS;QACnC;YAAE,MAAM;YAAW,MAAM;QAAW;QACpC;YAAE,MAAM;YAAa,MAAM;QAAS;QACpC;YAAE,MAAM;YAAW,MAAM;QAAW;QACpC;YAAE,MAAM;YAAY,MAAM;QAAY;KACvC;IACD,WAAW;QACT;YAAE,MAAM;YAAQ,MAAM;QAAQ;QAC9B;YAAE,MAAM;YAAe,MAAM;QAAQ;QACrC;YAAE,MAAM;YAAa,MAAM;QAAa;QACxC;YAAE,MAAM;YAAY,MAAM;QAAY;QACtC;YAAE,MAAM;YAAgB,MAAM;QAAS;KACxC;IACD,OAAO;QACL;YAAE,MAAM;YAAkB,MAAM;QAAW;QAC3C;YAAE,MAAM;YAAoB,MAAM;QAAS;QAC3C;YAAE,MAAM;YAAiB,MAAM;QAAW;QAC1C;YAAE,MAAM;YAAQ,MAAM;QAAQ;QAC9B;YAAE,MAAM;YAAY,MAAM;QAAY;KACvC;AACH;AAEA,MAAM,cAAc;IAClB;QAAE,MAAM;QAAW,MAAM,2MAAA,CAAA,UAAO;QAAE,MAAM;IAAW;IACnD;QAAE,MAAM;QAAY,MAAM,6MAAA,CAAA,WAAQ;QAAE,MAAM;IAAY;IACtD;QAAE,MAAM;QAAU,MAAM,yMAAA,CAAA,SAAM;QAAE,MAAM;IAAU;IAChD;QAAE,MAAM;QAAW,MAAM,2MAAA,CAAA,UAAO;QAAE,MAAM;IAAW;CACpD;AAED,MAAM,SAAS;IACb,qBACE,6LAAC;QAAO,WAAU;;0BAEhB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,UAAU;4BAAE,MAAM;wBAAK;wBACvB,WAAU;;0CAEV,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,6LAAC;wCAAG,WAAU;kDAAqB;;;;;;;;;;;;0CAErC,6LAAC;gCAAE,WAAU;0CAAqB;;;;;;0CAGlC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,oIAAA,CAAA,QAAK;wCACJ,aAAY;wCACZ,WAAU;;;;;;kDAEZ,6LAAC,qIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAW,WAAU;;4CAAQ;0DAE3C,6LAAC,qNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;;;;;;;;;;;;;0CAG1B,6LAAC;gCAAE,WAAU;0CAA6B;;;;;;;;;;;;;;;;;;;;;;0BAQhD,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,UAAU;gCAAE,MAAM;4BAAK;4BACvB,WAAU;;8CAEV,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,6MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;sDAEtB,6LAAC;4CAAK,WAAU;sDAAqB;;;;;;;;;;;;8CAEvC,6LAAC;oCAAE,WAAU;8CAA8B;;;;;;8CAK3C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;8DAChB,6LAAC;oDAAK,WAAU;8DAAU;;;;;;;;;;;;sDAE5B,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,uMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;8DACjB,6LAAC;oDAAK,WAAU;8DAAU;;;;;;;;;;;;sDAE5B,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,6MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,6LAAC;oDAAK,WAAU;8DAAU;;;;;;;;;;;;;;;;;;;;;;;;sCAMhC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,UAAU;gCAAE,MAAM;4BAAK;;8CAEvB,6LAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,6LAAC;oCAAG,WAAU;8CACX,YAAY,OAAO,CAAC,GAAG,CAAC,CAAC,qBACxB,6LAAC;sDACC,cAAA,6LAAC;gDACC,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,IAAI;;;;;;2CALL,KAAK,IAAI;;;;;;;;;;;;;;;;sCAaxB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,UAAU;gCAAE,MAAM;4BAAK;;8CAEvB,6LAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,6LAAC;oCAAG,WAAU;8CACX,YAAY,OAAO,CAAC,GAAG,CAAC,CAAC,qBACxB,6LAAC;sDACC,cAAA,6LAAC;gDACC,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,IAAI;;;;;;2CALL,KAAK,IAAI;;;;;;;;;;;;;;;;sCAaxB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,UAAU;gCAAE,MAAM;4BAAK;;8CAEvB,6LAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,6LAAC;oCAAG,WAAU;8CACX,YAAY,SAAS,CAAC,GAAG,CAAC,CAAC,qBAC1B,6LAAC;sDACC,cAAA,6LAAC;gDACC,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,IAAI;;;;;;2CALL,KAAK,IAAI;;;;;;;;;;;;;;;;sCAaxB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,UAAU;gCAAE,MAAM;4BAAK;;8CAEvB,6LAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,6LAAC;oCAAG,WAAU;8CACX,YAAY,KAAK,CAAC,GAAG,CAAC,CAAC,qBACtB,6LAAC;sDACC,cAAA,6LAAC;gDACC,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,IAAI;;;;;;2CALL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAe5B,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG,CAAC;gCAAG;gCAC9B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,UAAU;gCAAI;gCAC5B,UAAU;oCAAE,MAAM;gCAAK;gCACvB,WAAU;0CACX;;;;;;0CAKD,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,UAAU;gCAAI;gCAC5B,UAAU;oCAAE,MAAM;gCAAK;gCACvB,WAAU;0CAET,YAAY,GAAG,CAAC,CAAC,uBAChB,6LAAC;wCAEC,MAAM,OAAO,IAAI;wCACjB,WAAU;wCACV,cAAY,OAAO,IAAI;kDAEvB,cAAA,6LAAC,OAAO,IAAI;4CAAC,WAAU;;;;;;uCALlB,OAAO,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAclC;KA3MM;uCA6MS", "debugId": null}}, {"offset": {"line": 1005, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Landing%20page/admybrand-ai-suite/src/components/layout/header.tsx"], "sourcesContent": ["'use client'\n\nimport { Button } from '@/components/ui/button'\nimport { AnimatePresence, motion } from 'framer-motion'\nimport { Menu, X } from 'lucide-react'\nimport { useEffect, useState } from 'react'\n\nconst navigationItems = [\n  { name: 'Features', href: '#features' },\n  { name: 'Demo', href: '#demo' },\n  { name: 'Pricing', href: '#pricing' },\n  { name: 'Resources', href: '#blog' },\n  { name: 'Contact', href: '#contact' }\n]\n\nconst Header = () => {\n  const [isScrolled, setIsScrolled] = useState(false)\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)\n\n  useEffect(() => {\n    const handleScroll = () => {\n      setIsScrolled(window.scrollY > 50)\n    }\n\n    window.addEventListener('scroll', handleScroll)\n    return () => window.removeEventListener('scroll', handleScroll)\n  }, [])\n\n  const scrollToSection = (href: string) => {\n    const element = document.querySelector(href)\n    if (element) {\n      element.scrollIntoView({ behavior: 'smooth' })\n    }\n    setIsMobileMenuOpen(false)\n  }\n\n  return (\n    <>\n      <motion.header\n        initial={{ y: -100 }}\n        animate={{ y: 0 }}\n        transition={{ duration: 0.6 }}\n        className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${\n          isScrolled\n            ? 'bg-black/90 backdrop-blur-md shadow-lg border-b border-gray-800'\n            : 'bg-black/50 backdrop-blur-sm'\n        }`}\n      >\n        <div className=\"w-full px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex items-center justify-between h-16 lg:h-20\">\n            {/* Logo */}\n            <motion.div\n              whileHover={{ scale: 1.05 }}\n              className=\"flex items-center cursor-pointer\"\n              onClick={() => window.scrollTo({ top: 0, behavior: 'smooth' })}\n            >\n              <div className=\"flex items-center gap-2\">\n                <div className=\"w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg flex items-center justify-center\">\n                  <span className=\"text-white font-bold text-sm\">A</span>\n                </div>\n                <span className=\"text-xl font-bold bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent\">\n                  ADmyBRAND\n                </span>\n              </div>\n            </motion.div>\n\n            {/* Desktop Navigation */}\n            <nav className=\"hidden lg:flex items-center space-x-8\">\n              {navigationItems.map((item) => (\n                <motion.button\n                  key={item.name}\n                  onClick={() => scrollToSection(item.href)}\n                  className=\"relative text-sm font-medium text-gray-300 hover:text-white transition-colors duration-200 px-3 py-2 rounded-lg hover:bg-white/5\"\n                  whileHover={{ y: -2 }}\n                  whileTap={{ y: 0 }}\n                >\n                  {item.name}\n                  <motion.div\n                    className=\"absolute bottom-0 left-0 right-0 h-0.5 bg-gradient-to-r from-blue-400 to-purple-400 rounded-full\"\n                    initial={{ scaleX: 0 }}\n                    whileHover={{ scaleX: 1 }}\n                    transition={{ duration: 0.2 }}\n                  />\n                </motion.button>\n              ))}\n            </nav>\n\n            {/* Desktop CTA Button */}\n            <div className=\"hidden lg:flex items-center\">\n              <Button className=\"bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-6 py-2 rounded-xl font-medium shadow-lg hover:shadow-blue-500/25 transition-all duration-300 border border-white/10\">\n                BOOK A DEMO\n              </Button>\n            </div>\n\n            {/* Mobile Menu Button */}\n            <motion.button\n              whileTap={{ scale: 0.95 }}\n              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}\n              className=\"lg:hidden p-2 rounded-lg text-white hover:bg-white/10 transition-colors duration-200\"\n            >\n              {isMobileMenuOpen ? (\n                <X className=\"w-6 h-6\" />\n              ) : (\n                <Menu className=\"w-6 h-6\" />\n              )}\n            </motion.button>\n          </div>\n        </div>\n      </motion.header>\n\n      {/* Mobile Menu */}\n      <AnimatePresence>\n        {isMobileMenuOpen && (\n          <motion.div\n            initial={{ opacity: 0, height: 0 }}\n            animate={{ opacity: 1, height: 'auto' }}\n            exit={{ opacity: 0, height: 0 }}\n            transition={{ duration: 0.3 }}\n            className=\"fixed top-16 lg:top-20 left-0 right-0 z-40 bg-black/95 backdrop-blur-md border-b border-gray-800 lg:hidden\"\n          >\n            <div className=\"max-w-5xl mx-auto px-4 sm:px-6 py-6\">\n              <nav className=\"space-y-4\">\n                {navigationItems.map((item, index) => (\n                  <motion.button\n                    key={item.name}\n                    initial={{ opacity: 0, x: -20 }}\n                    animate={{ opacity: 1, x: 0 }}\n                    transition={{ duration: 0.3, delay: index * 0.1 }}\n                    onClick={() => scrollToSection(item.href)}\n                    className=\"block w-full text-left text-lg font-medium text-gray-300 hover:text-white transition-colors duration-200 py-2\"\n                  >\n                    {item.name}\n                  </motion.button>\n                ))}\n              </nav>\n              \n              <div className=\"mt-6 pt-6 border-t border-gray-800\">\n                <Button className=\"w-full justify-center bg-blue-600 hover:bg-blue-700 text-white\">\n                  BOOK A DEMO\n                </Button>\n              </div>\n            </div>\n          </motion.div>\n        )}\n      </AnimatePresence>\n\n      {/* Overlay for mobile menu */}\n      <AnimatePresence>\n        {isMobileMenuOpen && (\n          <motion.div\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            exit={{ opacity: 0 }}\n            transition={{ duration: 0.3 }}\n            className=\"fixed inset-0 bg-black/20 backdrop-blur-sm z-30 lg:hidden\"\n            onClick={() => setIsMobileMenuOpen(false)}\n          />\n        )}\n      </AnimatePresence>\n    </>\n  )\n}\n\nexport default Header\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AACA;;;AALA;;;;;AAOA,MAAM,kBAAkB;IACtB;QAAE,MAAM;QAAY,MAAM;IAAY;IACtC;QAAE,MAAM;QAAQ,MAAM;IAAQ;IAC9B;QAAE,MAAM;QAAW,MAAM;IAAW;IACpC;QAAE,MAAM;QAAa,MAAM;IAAQ;IACnC;QAAE,MAAM;QAAW,MAAM;IAAW;CACrC;AAED,MAAM,SAAS;;IACb,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACR,MAAM;iDAAe;oBACnB,cAAc,OAAO,OAAO,GAAG;gBACjC;;YAEA,OAAO,gBAAgB,CAAC,UAAU;YAClC;oCAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;;QACpD;2BAAG,EAAE;IAEL,MAAM,kBAAkB,CAAC;QACvB,MAAM,UAAU,SAAS,aAAa,CAAC;QACvC,IAAI,SAAS;YACX,QAAQ,cAAc,CAAC;gBAAE,UAAU;YAAS;QAC9C;QACA,oBAAoB;IACtB;IAEA,qBACE;;0BACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;gBACZ,SAAS;oBAAE,GAAG,CAAC;gBAAI;gBACnB,SAAS;oBAAE,GAAG;gBAAE;gBAChB,YAAY;oBAAE,UAAU;gBAAI;gBAC5B,WAAW,AAAC,+DAIX,OAHC,aACI,oEACA;0BAGN,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,YAAY;oCAAE,OAAO;gCAAK;gCAC1B,WAAU;gCACV,SAAS,IAAM,OAAO,QAAQ,CAAC;wCAAE,KAAK;wCAAG,UAAU;oCAAS;0CAE5D,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAK,WAAU;0DAA+B;;;;;;;;;;;sDAEjD,6LAAC;4CAAK,WAAU;sDAA0F;;;;;;;;;;;;;;;;;0CAO9G,6LAAC;gCAAI,WAAU;0CACZ,gBAAgB,GAAG,CAAC,CAAC,qBACpB,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;wCAEZ,SAAS,IAAM,gBAAgB,KAAK,IAAI;wCACxC,WAAU;wCACV,YAAY;4CAAE,GAAG,CAAC;wCAAE;wCACpB,UAAU;4CAAE,GAAG;wCAAE;;4CAEhB,KAAK,IAAI;0DACV,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,WAAU;gDACV,SAAS;oDAAE,QAAQ;gDAAE;gDACrB,YAAY;oDAAE,QAAQ;gDAAE;gDACxB,YAAY;oDAAE,UAAU;gDAAI;;;;;;;uCAXzB,KAAK,IAAI;;;;;;;;;;0CAkBpB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;oCAAC,WAAU;8CAAyN;;;;;;;;;;;0CAM7O,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;gCACZ,UAAU;oCAAE,OAAO;gCAAK;gCACxB,SAAS,IAAM,oBAAoB,CAAC;gCACpC,WAAU;0CAET,iCACC,6LAAC,+LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;6FAEb,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQ1B,6LAAC,4LAAA,CAAA,kBAAe;0BACb,kCACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,QAAQ;oBAAE;oBACjC,SAAS;wBAAE,SAAS;wBAAG,QAAQ;oBAAO;oBACtC,MAAM;wBAAE,SAAS;wBAAG,QAAQ;oBAAE;oBAC9B,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,WAAU;8BAEV,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACZ,gBAAgB,GAAG,CAAC,CAAC,MAAM,sBAC1B,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;wCAEZ,SAAS;4CAAE,SAAS;4CAAG,GAAG,CAAC;wCAAG;wCAC9B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,UAAU;4CAAK,OAAO,QAAQ;wCAAI;wCAChD,SAAS,IAAM,gBAAgB,KAAK,IAAI;wCACxC,WAAU;kDAET,KAAK,IAAI;uCAPL,KAAK,IAAI;;;;;;;;;;0CAYpB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;oCAAC,WAAU;8CAAiE;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAU7F,6LAAC,4LAAA,CAAA,kBAAe;0BACb,kCACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;oBAAE;oBACtB,SAAS;wBAAE,SAAS;oBAAE;oBACtB,MAAM;wBAAE,SAAS;oBAAE;oBACnB,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,WAAU;oBACV,SAAS,IAAM,oBAAoB;;;;;;;;;;;;;AAM/C;GAlJM;KAAA;uCAoJS", "debugId": null}}, {"offset": {"line": 1352, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Landing%20page/admybrand-ai-suite/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        destructive:\n          \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\n        outline: \"text-foreground\",\n        success:\n          \"border-transparent bg-green-500 text-white hover:bg-green-600\",\n        warning:\n          \"border-transparent bg-yellow-500 text-white hover:bg-yellow-600\",\n        info:\n          \"border-transparent bg-blue-500 text-white hover:bg-blue-600\",\n        gradient:\n          \"border-transparent bg-gradient-to-r from-purple-500 to-blue-500 text-white\",\n        glass:\n          \"glass border-white/20 text-white backdrop-blur-md\",\n      },\n      size: {\n        default: \"px-2.5 py-0.5 text-xs\",\n        sm: \"px-2 py-0.5 text-xs\",\n        lg: \"px-3 py-1 text-sm\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface BadgeProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof badgeVariants> {}\n\nfunction Badge({ className, variant, size, ...props }: BadgeProps) {\n  return (\n    <div className={cn(badgeVariants({ variant, size }), className)} {...props} />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,0KACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SAAS;YACT,SACE;YACF,SACE;YACF,MACE;YACF,UACE;YACF,OACE;QACJ;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;QACN;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAOF,SAAS,MAAM,KAAkD;QAAlD,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,GAAG,OAAmB,GAAlD;IACb,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;YAAS;QAAK,IAAI;QAAa,GAAG,KAAK;;;;;;AAE9E;KAJS", "debugId": null}}, {"offset": {"line": 1413, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Landing%20page/admybrand-ai-suite/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst cardVariants = cva(\n  \"rounded-lg border bg-card text-card-foreground shadow-sm\",\n  {\n    variants: {\n      variant: {\n        default: \"border-border\",\n        glass: \"glass border-white/20 backdrop-blur-md\",\n        gradient: \"bg-gradient-to-br from-white to-gray-50 border-gray-200\",\n        elevated: \"shadow-lg hover:shadow-xl transition-shadow duration-300\",\n        interactive: \"hover:shadow-md transition-all duration-200 cursor-pointer hover:scale-[1.02]\",\n      },\n      padding: {\n        none: \"p-0\",\n        sm: \"p-4\",\n        default: \"p-6\",\n        lg: \"p-8\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      padding: \"default\",\n    },\n  }\n)\n\nexport interface CardProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof cardVariants> {}\n\nconst Card = React.forwardRef<HTMLDivElement, CardProps>(\n  ({ className, variant, padding, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn(cardVariants({ variant, padding, className }))}\n      {...props}\n    />\n  )\n)\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,eAAe,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACrB,4DACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,OAAO;YACP,UAAU;YACV,UAAU;YACV,aAAa;QACf;QACA,SAAS;YACP,MAAM;YACN,IAAI;YACJ,SAAS;YACT,IAAI;QACN;IACF;IACA,iBAAiB;QACf,SAAS;QACT,SAAS;IACX;AACF;AAOF,MAAM,qBAAO,6JAAA,CAAA,aAAgB,MAC3B,QAA4C;QAA3C,EAAE,SAAS,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,OAAO;yBACxC,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,aAAa;YAAE;YAAS;YAAS;QAAU;QACxD,GAAG,KAAK;;;;;;;;AAIf,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,6JAAA,CAAA,aAAgB,OAGjC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,6JAAA,CAAA,aAAgB,OAGhC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,6JAAA,CAAA,aAAgB,OAGtC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,6JAAA,CAAA,aAAgB,OAGlC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,6JAAA,CAAA,aAAgB,QAGjC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 1561, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Landing%20page/admybrand-ai-suite/src/components/sections/blog-resources.tsx"], "sourcesContent": ["'use client'\n\nimport { Badge } from '@/components/ui/badge'\nimport { Button } from '@/components/ui/button'\nimport { Card, CardContent } from '@/components/ui/card'\nimport { motion } from 'framer-motion'\nimport { BookOpen, Download, ExternalLink, Clock, User, ArrowRight } from 'lucide-react'\n\nconst blogPosts = [\n  {\n    title: \"10 AI Marketing Strategies That Actually Work in 2024\",\n    excerpt: \"Discover the latest AI marketing techniques that top brands use to increase conversions by 300%.\",\n    author: \"<PERSON>\",\n    readTime: \"5 min read\",\n    category: \"Strategy\",\n    image: \"/api/placeholder/400/250\",\n    featured: true\n  },\n  {\n    title: \"Complete Guide to Brand Voice AI\",\n    excerpt: \"Learn how to train AI to match your brand's unique voice and maintain consistency across all channels.\",\n    author: \"<PERSON> Chen\",\n    readTime: \"8 min read\",\n    category: \"Guide\",\n    image: \"/api/placeholder/400/250\"\n  },\n  {\n    title: \"ROI Calculator: Measuring AI Marketing Success\",\n    excerpt: \"Step-by-step framework to measure and optimize your AI marketing campaigns for maximum ROI.\",\n    author: \"<PERSON>\",\n    readTime: \"6 min read\",\n    category: \"Analytics\",\n    image: \"/api/placeholder/400/250\"\n  }\n]\n\nconst resources = [\n  {\n    title: \"AI Marketing Playbook 2024\",\n    description: \"Complete 50-page guide with templates, checklists, and strategies\",\n    type: \"PDF Guide\",\n    downloads: \"2.3K\",\n    icon: BookOpen\n  },\n  {\n    title: \"Brand Voice Training Templates\",\n    description: \"Ready-to-use templates for training AI on your brand voice\",\n    type: \"Template Pack\",\n    downloads: \"1.8K\",\n    icon: Download\n  },\n  {\n    title: \"ROI Tracking Spreadsheet\",\n    description: \"Excel template to track and measure your marketing ROI\",\n    type: \"Spreadsheet\",\n    downloads: \"1.5K\",\n    icon: Download\n  }\n]\n\nconst BlogResources = () => {\n  return (\n    <section className=\"py-24 bg-black\">\n      <div className=\"max-w-7xl mx-auto px-2 sm:px-4 lg:px-6\">\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          viewport={{ once: true }}\n          className=\"text-center mb-16\"\n        >\n          <Badge className=\"mb-4 bg-white/10 text-white border-white/20 backdrop-blur-sm\">\n            <BookOpen className=\"w-4 h-4 mr-2\" />\n            Resources & Insights\n          </Badge>\n          <h2 className=\"text-4xl md:text-5xl font-bold text-white mb-6\">\n            Learn from the\n            <span className=\"bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent\"> experts</span>\n          </h2>\n          <p className=\"text-xl text-gray-300 max-w-3xl mx-auto\">\n            Get actionable insights, templates, and strategies to master AI marketing\n          </p>\n        </motion.div>\n\n        {/* Blog Posts */}\n        <div className=\"mb-20\">\n          <motion.h3\n            initial={{ opacity: 0, x: -30 }}\n            whileInView={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.6 }}\n            viewport={{ once: true }}\n            className=\"text-2xl font-bold text-white mb-8\"\n          >\n            Latest Articles\n          </motion.h3>\n          \n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n            {blogPosts.map((post, index) => (\n              <motion.div\n                key={post.title}\n                initial={{ opacity: 0, y: 30 }}\n                whileInView={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.6, delay: index * 0.1 }}\n                viewport={{ once: true }}\n                whileHover={{ y: -5 }}\n                className={`group ${post.featured ? 'md:col-span-2 lg:col-span-1' : ''}`}\n              >\n                <Card className=\"bg-white/5 backdrop-blur-xl border border-white/10 rounded-2xl overflow-hidden hover:border-white/20 transition-all duration-300\">\n                  <div className=\"aspect-video bg-gradient-to-br from-gray-800 to-gray-900 relative overflow-hidden\">\n                    <div className=\"absolute inset-0 bg-gradient-to-br from-blue-500/20 to-purple-500/20\" />\n                    <div className=\"absolute top-4 left-4\">\n                      <Badge className=\"bg-white/20 text-white border-white/30 backdrop-blur-sm\">\n                        {post.category}\n                      </Badge>\n                    </div>\n                    {post.featured && (\n                      <div className=\"absolute top-4 right-4\">\n                        <Badge className=\"bg-yellow-500/20 text-yellow-400 border-yellow-500/30\">\n                          Featured\n                        </Badge>\n                      </div>\n                    )}\n                  </div>\n                  \n                  <CardContent className=\"p-6\">\n                    <h4 className=\"text-lg font-bold text-white mb-3 group-hover:text-blue-400 transition-colors\">\n                      {post.title}\n                    </h4>\n                    <p className=\"text-gray-300 mb-4 line-clamp-2\">\n                      {post.excerpt}\n                    </p>\n                    \n                    <div className=\"flex items-center justify-between text-sm text-gray-400 mb-4\">\n                      <div className=\"flex items-center gap-2\">\n                        <User className=\"w-4 h-4\" />\n                        <span>{post.author}</span>\n                      </div>\n                      <div className=\"flex items-center gap-2\">\n                        <Clock className=\"w-4 h-4\" />\n                        <span>{post.readTime}</span>\n                      </div>\n                    </div>\n                    \n                    <Button variant=\"ghost\" className=\"w-full text-blue-400 hover:text-white hover:bg-blue-500/20 group\">\n                      Read Article\n                      <ArrowRight className=\"w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform\" />\n                    </Button>\n                  </CardContent>\n                </Card>\n              </motion.div>\n            ))}\n          </div>\n        </div>\n\n        {/* Resources */}\n        <div>\n          <motion.h3\n            initial={{ opacity: 0, x: -30 }}\n            whileInView={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.6 }}\n            viewport={{ once: true }}\n            className=\"text-2xl font-bold text-white mb-8\"\n          >\n            Free Resources\n          </motion.h3>\n          \n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n            {resources.map((resource, index) => (\n              <motion.div\n                key={resource.title}\n                initial={{ opacity: 0, y: 30 }}\n                whileInView={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.6, delay: index * 0.1 }}\n                viewport={{ once: true }}\n                whileHover={{ y: -5 }}\n                className=\"group\"\n              >\n                <Card className=\"bg-gradient-to-br from-white/5 to-white/10 backdrop-blur-xl border border-white/10 rounded-2xl hover:border-white/20 transition-all duration-300 h-full\">\n                  <CardContent className=\"p-6 flex flex-col h-full\">\n                    <div className=\"flex items-center gap-4 mb-4\">\n                      <div className=\"w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-500 rounded-lg flex items-center justify-center\">\n                        <resource.icon className=\"w-6 h-6 text-white\" />\n                      </div>\n                      <div>\n                        <Badge className=\"bg-white/20 text-white border-white/30 text-xs\">\n                          {resource.type}\n                        </Badge>\n                      </div>\n                    </div>\n                    \n                    <h4 className=\"text-lg font-bold text-white mb-2 group-hover:text-blue-400 transition-colors\">\n                      {resource.title}\n                    </h4>\n                    <p className=\"text-gray-300 mb-4 flex-grow\">\n                      {resource.description}\n                    </p>\n                    \n                    <div className=\"flex items-center justify-between\">\n                      <span className=\"text-sm text-gray-400\">\n                        {resource.downloads} downloads\n                      </span>\n                      <Button size=\"sm\" className=\"bg-blue-600 hover:bg-blue-700 text-white\">\n                        <Download className=\"w-4 h-4 mr-2\" />\n                        Download\n                      </Button>\n                    </div>\n                  </CardContent>\n                </Card>\n              </motion.div>\n            ))}\n          </div>\n        </div>\n\n        {/* CTA */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          viewport={{ once: true }}\n          className=\"text-center mt-16\"\n        >\n          <Button className=\"bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-8 py-3 rounded-xl font-semibold\">\n            <ExternalLink className=\"w-5 h-5 mr-2\" />\n            Visit Our Blog\n          </Button>\n        </motion.div>\n      </div>\n    </section>\n  )\n}\n\nexport default BlogResources\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AANA;;;;;;;AAQA,MAAM,YAAY;IAChB;QACE,OAAO;QACP,SAAS;QACT,QAAQ;QACR,UAAU;QACV,UAAU;QACV,OAAO;QACP,UAAU;IACZ;IACA;QACE,OAAO;QACP,SAAS;QACT,QAAQ;QACR,UAAU;QACV,UAAU;QACV,OAAO;IACT;IACA;QACE,OAAO;QACP,SAAS;QACT,QAAQ;QACR,UAAU;QACV,UAAU;QACV,OAAO;IACT;CACD;AAED,MAAM,YAAY;IAChB;QACE,OAAO;QACP,aAAa;QACb,MAAM;QACN,WAAW;QACX,MAAM,iNAAA,CAAA,WAAQ;IAChB;IACA;QACE,OAAO;QACP,aAAa;QACb,MAAM;QACN,WAAW;QACX,MAAM,6MAAA,CAAA,WAAQ;IAChB;IACA;QACE,OAAO;QACP,aAAa;QACb,MAAM;QACN,WAAW;QACX,MAAM,6MAAA,CAAA,WAAQ;IAChB;CACD;AAED,MAAM,gBAAgB;IACpB,qBACE,6LAAC;QAAQ,WAAU;kBACjB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAEV,6LAAC,oIAAA,CAAA,QAAK;4BAAC,WAAU;;8CACf,6LAAC,iNAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;sCAGvC,6LAAC;4BAAG,WAAU;;gCAAiD;8CAE7D,6LAAC;oCAAK,WAAU;8CAA6E;;;;;;;;;;;;sCAE/F,6LAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;8BAMzD,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;4BACR,SAAS;gCAAE,SAAS;gCAAG,GAAG,CAAC;4BAAG;4BAC9B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,UAAU;gCAAE,MAAM;4BAAK;4BACvB,WAAU;sCACX;;;;;;sCAID,6LAAC;4BAAI,WAAU;sCACZ,UAAU,GAAG,CAAC,CAAC,MAAM,sBACpB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,YAAY;wCAAE,UAAU;wCAAK,OAAO,QAAQ;oCAAI;oCAChD,UAAU;wCAAE,MAAM;oCAAK;oCACvB,YAAY;wCAAE,GAAG,CAAC;oCAAE;oCACpB,WAAW,AAAC,SAA2D,OAAnD,KAAK,QAAQ,GAAG,gCAAgC;8CAEpE,cAAA,6LAAC,mIAAA,CAAA,OAAI;wCAAC,WAAU;;0DACd,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;;;;;kEACf,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC,oIAAA,CAAA,QAAK;4DAAC,WAAU;sEACd,KAAK,QAAQ;;;;;;;;;;;oDAGjB,KAAK,QAAQ,kBACZ,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC,oIAAA,CAAA,QAAK;4DAAC,WAAU;sEAAwD;;;;;;;;;;;;;;;;;0DAO/E,6LAAC,mIAAA,CAAA,cAAW;gDAAC,WAAU;;kEACrB,6LAAC;wDAAG,WAAU;kEACX,KAAK,KAAK;;;;;;kEAEb,6LAAC;wDAAE,WAAU;kEACV,KAAK,OAAO;;;;;;kEAGf,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC,qMAAA,CAAA,OAAI;wEAAC,WAAU;;;;;;kFAChB,6LAAC;kFAAM,KAAK,MAAM;;;;;;;;;;;;0EAEpB,6LAAC;gEAAI,WAAU;;kFACb,6LAAC,uMAAA,CAAA,QAAK;wEAAC,WAAU;;;;;;kFACjB,6LAAC;kFAAM,KAAK,QAAQ;;;;;;;;;;;;;;;;;;kEAIxB,6LAAC,qIAAA,CAAA,SAAM;wDAAC,SAAQ;wDAAQ,WAAU;;4DAAmE;0EAEnG,6LAAC,qNAAA,CAAA,aAAU;gEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;mCA9CvB,KAAK,KAAK;;;;;;;;;;;;;;;;8BAwDvB,6LAAC;;sCACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;4BACR,SAAS;gCAAE,SAAS;gCAAG,GAAG,CAAC;4BAAG;4BAC9B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,UAAU;gCAAE,MAAM;4BAAK;4BACvB,WAAU;sCACX;;;;;;sCAID,6LAAC;4BAAI,WAAU;sCACZ,UAAU,GAAG,CAAC,CAAC,UAAU,sBACxB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,YAAY;wCAAE,UAAU;wCAAK,OAAO,QAAQ;oCAAI;oCAChD,UAAU;wCAAE,MAAM;oCAAK;oCACvB,YAAY;wCAAE,GAAG,CAAC;oCAAE;oCACpB,WAAU;8CAEV,cAAA,6LAAC,mIAAA,CAAA,OAAI;wCAAC,WAAU;kDACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;4CAAC,WAAU;;8DACrB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC,SAAS,IAAI;gEAAC,WAAU;;;;;;;;;;;sEAE3B,6LAAC;sEACC,cAAA,6LAAC,oIAAA,CAAA,QAAK;gEAAC,WAAU;0EACd,SAAS,IAAI;;;;;;;;;;;;;;;;;8DAKpB,6LAAC;oDAAG,WAAU;8DACX,SAAS,KAAK;;;;;;8DAEjB,6LAAC;oDAAE,WAAU;8DACV,SAAS,WAAW;;;;;;8DAGvB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;;gEACb,SAAS,SAAS;gEAAC;;;;;;;sEAEtB,6LAAC,qIAAA,CAAA,SAAM;4DAAC,MAAK;4DAAK,WAAU;;8EAC1B,6LAAC,6MAAA,CAAA,WAAQ;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;;;;;;;;;;;;;;;;;;mCAjCxC,SAAS,KAAK;;;;;;;;;;;;;;;;8BA6C3B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;8BAEV,cAAA,6LAAC,qIAAA,CAAA,SAAM;wBAAC,WAAU;;0CAChB,6LAAC,yNAAA,CAAA,eAAY;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;;;;;;;;;;;AAOrD;KAzKM;uCA2KS", "debugId": null}}, {"offset": {"line": 2152, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Landing%20page/admybrand-ai-suite/src/components/sections/contact.tsx"], "sourcesContent": ["'use client'\n\nimport { Badge } from '@/components/ui/badge'\nimport { Button } from '@/components/ui/button'\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Input } from '@/components/ui/input'\nimport { motion } from 'framer-motion'\nimport { CheckCircle, Mail, MapPin, Phone, Send } from 'lucide-react'\nimport { useState } from 'react'\n\ninterface FormData {\n  name: string\n  email: string\n  company: string\n  message: string\n}\n\ninterface FormErrors {\n  name?: string\n  email?: string\n  company?: string\n  message?: string\n}\n\nconst Contact = () => {\n  const [formData, setFormData] = useState<FormData>({\n    name: '',\n    email: '',\n    company: '',\n    message: ''\n  })\n  const [errors, setErrors] = useState<FormErrors>({})\n  const [isSubmitting, setIsSubmitting] = useState(false)\n  const [isSubmitted, setIsSubmitted] = useState(false)\n\n  const validateForm = (): boolean => {\n    const newErrors: FormErrors = {}\n\n    if (!formData.name.trim()) {\n      newErrors.name = 'Name is required'\n    }\n\n    if (!formData.email.trim()) {\n      newErrors.email = 'Email is required'\n    } else if (!/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(formData.email)) {\n      newErrors.email = 'Please enter a valid email address'\n    }\n\n    if (!formData.company.trim()) {\n      newErrors.company = 'Company is required'\n    }\n\n    if (!formData.message.trim()) {\n      newErrors.message = 'Message is required'\n    } else if (formData.message.trim().length < 10) {\n      newErrors.message = 'Message must be at least 10 characters long'\n    }\n\n    setErrors(newErrors)\n    return Object.keys(newErrors).length === 0\n  }\n\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {\n    const { name, value } = e.target\n    setFormData(prev => ({ ...prev, [name]: value }))\n    \n    // Clear error when user starts typing\n    if (errors[name as keyof FormErrors]) {\n      setErrors(prev => ({ ...prev, [name]: undefined }))\n    }\n  }\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n    \n    if (!validateForm()) return\n\n    setIsSubmitting(true)\n\n    // Simulate API call\n    try {\n      await new Promise(resolve => setTimeout(resolve, 2000))\n      setIsSubmitted(true)\n      setFormData({ name: '', email: '', company: '', message: '' })\n    } catch (error) {\n      console.error('Error submitting form:', error)\n    } finally {\n      setIsSubmitting(false)\n    }\n  }\n\n  if (isSubmitted) {\n    return (\n      <section className=\"py-24 bg-black\">\n        <div className=\"max-w-6xl mx-auto px-2 sm:px-4 lg:px-6\">\n          <motion.div\n            initial={{ opacity: 0, scale: 0.8 }}\n            animate={{ opacity: 1, scale: 1 }}\n            transition={{ duration: 0.6 }}\n            className=\"text-center\"\n          >\n            <div className=\"w-20 h-20 bg-green-500/20 backdrop-blur-sm border border-green-500/30 rounded-full flex items-center justify-center mx-auto mb-6\">\n              <CheckCircle className=\"w-10 h-10 text-green-400\" />\n            </div>\n            <h2 className=\"text-3xl font-bold text-white mb-4\">\n              Thank you for reaching out!\n            </h2>\n            <p className=\"text-xl text-gray-300 mb-8\">\n              We've received your message and will get back to you within 24 hours.\n            </p>\n            <Button\n              onClick={() => setIsSubmitted(false)}\n              className=\"bg-white/10 hover:bg-white/20 text-white border-white/20\"\n            >\n              Send Another Message\n            </Button>\n          </motion.div>\n        </div>\n      </section>\n    )\n  }\n\n  return (\n    <section className=\"py-24 bg-black\">\n      <div className=\"max-w-7xl mx-auto px-2 sm:px-4 lg:px-6\">\n        {/* Section Header */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          viewport={{ once: true }}\n          className=\"text-center mb-16\"\n        >\n          <Badge className=\"mb-4 bg-white/10 text-white border-white/20 backdrop-blur-sm\">\n            Get in Touch\n          </Badge>\n          <h2 className=\"text-4xl md:text-5xl font-bold text-white mb-6\">\n            Ready to transform your\n            <span className=\"bg-gradient-to-r from-blue-400 to-cyan-400 bg-clip-text text-transparent\"> marketing?</span>\n          </h2>\n          <p className=\"text-xl text-gray-300 max-w-3xl mx-auto\">\n            Let's discuss how ADmyBRAND AI Suite can help you achieve your marketing goals.\n            Our team is here to answer your questions and provide a personalized demo.\n          </p>\n        </motion.div>\n\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-12\">\n          {/* Contact Form */}\n          <motion.div\n            initial={{ opacity: 0, x: -30 }}\n            whileInView={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.8 }}\n            viewport={{ once: true }}\n          >\n            <Card className=\"shadow-xl border border-white/10 bg-white/5 backdrop-blur-md\">\n              <CardHeader>\n                <CardTitle className=\"text-2xl font-bold text-white\">\n                  Send us a message\n                </CardTitle>\n              </CardHeader>\n              <CardContent>\n                <form onSubmit={handleSubmit} className=\"space-y-6\">\n                  <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                    <Input\n                      label=\"Full Name\"\n                      name=\"name\"\n                      value={formData.name}\n                      onChange={handleInputChange}\n                      error={errors.name}\n                      placeholder=\"John Doe\"\n                    />\n                    <Input\n                      label=\"Email Address\"\n                      name=\"email\"\n                      type=\"email\"\n                      value={formData.email}\n                      onChange={handleInputChange}\n                      error={errors.email}\n                      placeholder=\"<EMAIL>\"\n                    />\n                  </div>\n                  \n                  <Input\n                    label=\"Company\"\n                    name=\"company\"\n                    value={formData.company}\n                    onChange={handleInputChange}\n                    error={errors.company}\n                    placeholder=\"Your Company Name\"\n                  />\n\n                  <div>\n                    <label className=\"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 mb-2 block\">\n                      Message\n                    </label>\n                    <textarea\n                      name=\"message\"\n                      value={formData.message}\n                      onChange={handleInputChange}\n                      placeholder=\"Tell us about your marketing goals and how we can help...\"\n                      rows={5}\n                      className={`flex w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 resize-none ${\n                        errors.message ? 'border-destructive focus-visible:ring-destructive' : ''\n                      }`}\n                    />\n                    {errors.message && (\n                      <p className=\"text-sm text-destructive mt-1\">{errors.message}</p>\n                    )}\n                  </div>\n\n                  <Button\n                    type=\"submit\"\n                    className=\"w-full\"\n                    variant=\"gradient\"\n                    loading={isSubmitting}\n                    disabled={isSubmitting}\n                  >\n                    {isSubmitting ? 'Sending...' : 'Send Message'}\n                    <Send className=\"ml-2 h-4 w-4\" />\n                  </Button>\n                </form>\n              </CardContent>\n            </Card>\n          </motion.div>\n\n          {/* Contact Information */}\n          <motion.div\n            initial={{ opacity: 0, x: 30 }}\n            whileInView={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.8 }}\n            viewport={{ once: true }}\n            className=\"space-y-8\"\n          >\n            <div>\n              <h3 className=\"text-2xl font-bold text-white mb-6\">\n                Get in touch\n              </h3>\n              <p className=\"text-gray-300 mb-8\">\n                We're here to help you succeed. Reach out to us through any of these channels,\n                and we'll respond as quickly as possible.\n              </p>\n            </div>\n\n            <div className=\"space-y-6\">\n              <div className=\"flex items-start space-x-4\">\n                <div className=\"w-12 h-12 bg-purple-500/20 backdrop-blur-sm border border-purple-500/30 rounded-lg flex items-center justify-center flex-shrink-0\">\n                  <Mail className=\"w-6 h-6 text-purple-400\" />\n                </div>\n                <div>\n                  <h4 className=\"font-semibold text-white mb-1\">Email us</h4>\n                  <p className=\"text-blue-400\"><EMAIL></p>\n                  <p className=\"text-sm text-gray-400\">We'll respond within 24 hours</p>\n                </div>\n              </div>\n\n              <div className=\"flex items-start space-x-4\">\n                <div className=\"w-12 h-12 bg-blue-500/20 backdrop-blur-sm border border-blue-500/30 rounded-lg flex items-center justify-center flex-shrink-0\">\n                  <Phone className=\"w-6 h-6 text-blue-400\" />\n                </div>\n                <div>\n                  <h4 className=\"font-semibold text-white mb-1\">Call us</h4>\n                  <p className=\"text-blue-400\">+****************</p>\n                  <p className=\"text-sm text-gray-400\">Mon-Fri 9am-6pm PST</p>\n                </div>\n              </div>\n\n              <div className=\"flex items-start space-x-4\">\n                <div className=\"w-12 h-12 bg-green-500/20 backdrop-blur-sm border border-green-500/30 rounded-lg flex items-center justify-center flex-shrink-0\">\n                  <MapPin className=\"w-6 h-6 text-green-400\" />\n                </div>\n                <div>\n                  <h4 className=\"font-semibold text-white mb-1\">Visit us</h4>\n                  <p className=\"text-gray-300\">\n                    123 Innovation Drive<br />\n                    San Francisco, CA 94105\n                  </p>\n                </div>\n              </div>\n            </div>\n\n            {/* Quick Stats */}\n            <div className=\"bg-white/5 backdrop-blur-xl border border-white/10 rounded-xl p-6 mt-8\">\n              <h4 className=\"font-semibold text-white mb-4\">Why choose ADmyBRAND?</h4>\n              <div className=\"grid grid-cols-2 gap-4\">\n                <div className=\"text-center\">\n                  <div className=\"text-2xl font-bold text-purple-400\">24/7</div>\n                  <div className=\"text-sm text-gray-400\">Support</div>\n                </div>\n                <div className=\"text-center\">\n                  <div className=\"text-2xl font-bold text-blue-400\">99.9%</div>\n                  <div className=\"text-sm text-gray-400\">Uptime</div>\n                </div>\n                <div className=\"text-center\">\n                  <div className=\"text-2xl font-bold text-green-400\">50K+</div>\n                  <div className=\"text-sm text-gray-400\">Customers</div>\n                </div>\n                <div className=\"text-center\">\n                  <div className=\"text-2xl font-bold text-orange-400\">5★</div>\n                  <div className=\"text-sm text-gray-400\">Rating</div>\n                </div>\n              </div>\n            </div>\n          </motion.div>\n        </div>\n      </div>\n    </section>\n  )\n}\n\nexport default Contact\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;;;AARA;;;;;;;;AAwBA,MAAM,UAAU;;IACd,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY;QACjD,MAAM;QACN,OAAO;QACP,SAAS;QACT,SAAS;IACX;IACA,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc,CAAC;IAClD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,eAAe;QACnB,MAAM,YAAwB,CAAC;QAE/B,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,IAAI;YACzB,UAAU,IAAI,GAAG;QACnB;QAEA,IAAI,CAAC,SAAS,KAAK,CAAC,IAAI,IAAI;YAC1B,UAAU,KAAK,GAAG;QACpB,OAAO,IAAI,CAAC,6BAA6B,IAAI,CAAC,SAAS,KAAK,GAAG;YAC7D,UAAU,KAAK,GAAG;QACpB;QAEA,IAAI,CAAC,SAAS,OAAO,CAAC,IAAI,IAAI;YAC5B,UAAU,OAAO,GAAG;QACtB;QAEA,IAAI,CAAC,SAAS,OAAO,CAAC,IAAI,IAAI;YAC5B,UAAU,OAAO,GAAG;QACtB,OAAO,IAAI,SAAS,OAAO,CAAC,IAAI,GAAG,MAAM,GAAG,IAAI;YAC9C,UAAU,OAAO,GAAG;QACtB;QAEA,UAAU;QACV,OAAO,OAAO,IAAI,CAAC,WAAW,MAAM,KAAK;IAC3C;IAEA,MAAM,oBAAoB,CAAC;QACzB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;QAChC,YAAY,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,KAAK,EAAE;YAAM,CAAC;QAE/C,sCAAsC;QACtC,IAAI,MAAM,CAAC,KAAyB,EAAE;YACpC,UAAU,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,CAAC,KAAK,EAAE;gBAAU,CAAC;QACnD;IACF;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,CAAC,gBAAgB;QAErB,gBAAgB;QAEhB,oBAAoB;QACpB,IAAI;YACF,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YACjD,eAAe;YACf,YAAY;gBAAE,MAAM;gBAAI,OAAO;gBAAI,SAAS;gBAAI,SAAS;YAAG;QAC9D,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;QAC1C,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,IAAI,aAAa;QACf,qBACE,6LAAC;YAAQ,WAAU;sBACjB,cAAA,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,OAAO;oBAAI;oBAClC,SAAS;wBAAE,SAAS;wBAAG,OAAO;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,WAAU;;sCAEV,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,8NAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;;;;;;sCAEzB,6LAAC;4BAAG,WAAU;sCAAqC;;;;;;sCAGnD,6LAAC;4BAAE,WAAU;sCAA6B;;;;;;sCAG1C,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAS,IAAM,eAAe;4BAC9B,WAAU;sCACX;;;;;;;;;;;;;;;;;;;;;;IAOX;IAEA,qBACE,6LAAC;QAAQ,WAAU;kBACjB,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAEV,6LAAC,oIAAA,CAAA,QAAK;4BAAC,WAAU;sCAA+D;;;;;;sCAGhF,6LAAC;4BAAG,WAAU;;gCAAiD;8CAE7D,6LAAC;oCAAK,WAAU;8CAA2E;;;;;;;;;;;;sCAE7F,6LAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;8BAMzD,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG,CAAC;4BAAG;4BAC9B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,UAAU;gCAAE,MAAM;4BAAK;sCAEvB,cAAA,6LAAC,mIAAA,CAAA,OAAI;gCAAC,WAAU;;kDACd,6LAAC,mIAAA,CAAA,aAAU;kDACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;4CAAC,WAAU;sDAAgC;;;;;;;;;;;kDAIvD,6LAAC,mIAAA,CAAA,cAAW;kDACV,cAAA,6LAAC;4CAAK,UAAU;4CAAc,WAAU;;8DACtC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,oIAAA,CAAA,QAAK;4DACJ,OAAM;4DACN,MAAK;4DACL,OAAO,SAAS,IAAI;4DACpB,UAAU;4DACV,OAAO,OAAO,IAAI;4DAClB,aAAY;;;;;;sEAEd,6LAAC,oIAAA,CAAA,QAAK;4DACJ,OAAM;4DACN,MAAK;4DACL,MAAK;4DACL,OAAO,SAAS,KAAK;4DACrB,UAAU;4DACV,OAAO,OAAO,KAAK;4DACnB,aAAY;;;;;;;;;;;;8DAIhB,6LAAC,oIAAA,CAAA,QAAK;oDACJ,OAAM;oDACN,MAAK;oDACL,OAAO,SAAS,OAAO;oDACvB,UAAU;oDACV,OAAO,OAAO,OAAO;oDACrB,aAAY;;;;;;8DAGd,6LAAC;;sEACC,6LAAC;4DAAM,WAAU;sEAAwG;;;;;;sEAGzH,6LAAC;4DACC,MAAK;4DACL,OAAO,SAAS,OAAO;4DACvB,UAAU;4DACV,aAAY;4DACZ,MAAM;4DACN,WAAW,AAAC,uSAEX,OADC,OAAO,OAAO,GAAG,sDAAsD;;;;;;wDAG1E,OAAO,OAAO,kBACb,6LAAC;4DAAE,WAAU;sEAAiC,OAAO,OAAO;;;;;;;;;;;;8DAIhE,6LAAC,qIAAA,CAAA,SAAM;oDACL,MAAK;oDACL,WAAU;oDACV,SAAQ;oDACR,SAAS;oDACT,UAAU;;wDAET,eAAe,eAAe;sEAC/B,6LAAC,qMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQ1B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,UAAU;gCAAE,MAAM;4BAAK;4BACvB,WAAU;;8CAEV,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAAqC;;;;;;sDAGnD,6LAAC;4CAAE,WAAU;sDAAqB;;;;;;;;;;;;8CAMpC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;;;;;;8DAElB,6LAAC;;sEACC,6LAAC;4DAAG,WAAU;sEAAgC;;;;;;sEAC9C,6LAAC;4DAAE,WAAU;sEAAgB;;;;;;sEAC7B,6LAAC;4DAAE,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;sDAIzC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;;;;;;8DAEnB,6LAAC;;sEACC,6LAAC;4DAAG,WAAU;sEAAgC;;;;;;sEAC9C,6LAAC;4DAAE,WAAU;sEAAgB;;;;;;sEAC7B,6LAAC;4DAAE,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;sDAIzC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,6MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;;;;;;8DAEpB,6LAAC;;sEACC,6LAAC;4DAAG,WAAU;sEAAgC;;;;;;sEAC9C,6LAAC;4DAAE,WAAU;;gEAAgB;8EACP,6LAAC;;;;;gEAAK;;;;;;;;;;;;;;;;;;;;;;;;;8CAQlC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAgC;;;;;;sDAC9C,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEAAqC;;;;;;sEACpD,6LAAC;4DAAI,WAAU;sEAAwB;;;;;;;;;;;;8DAEzC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEAAmC;;;;;;sEAClD,6LAAC;4DAAI,WAAU;sEAAwB;;;;;;;;;;;;8DAEzC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEAAoC;;;;;;sEACnD,6LAAC;4DAAI,WAAU;sEAAwB;;;;;;;;;;;;8DAEzC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEAAqC;;;;;;sEACpD,6LAAC;4DAAI,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASzD;GA3RM;KAAA;uCA6RS", "debugId": null}}, {"offset": {"line": 2924, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Landing%20page/admybrand-ai-suite/src/components/sections/demo-video.tsx"], "sourcesContent": ["'use client'\n\nimport { Badge } from '@/components/ui/badge'\nimport { Button } from '@/components/ui/button'\nimport { motion } from 'framer-motion'\nimport { Play, Volume2, Maximize, Clock, Users, TrendingUp } from 'lucide-react'\nimport { useState } from 'react'\n\nconst DemoVideo = () => {\n  const [isPlaying, setIsPlaying] = useState(false)\n\n  const stats = [\n    { icon: Clock, value: '2 min', label: 'Quick Setup' },\n    { icon: Users, value: '50K+', label: 'Happy Users' },\n    { icon: TrendingUp, value: '300%', label: 'ROI Increase' }\n  ]\n\n  return (\n    <section className=\"py-24 bg-gradient-to-br from-black via-gray-900 to-black\">\n      <div className=\"max-w-7xl mx-auto px-2 sm:px-4 lg:px-6\">\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          viewport={{ once: true }}\n          className=\"text-center mb-16\"\n        >\n          <Badge className=\"mb-4 bg-white/10 text-white border-white/20 backdrop-blur-sm\">\n            <Play className=\"w-4 h-4 mr-2\" />\n            Product Demo\n          </Badge>\n          <h2 className=\"text-4xl md:text-5xl font-bold text-white mb-6\">\n            See ADmyBRAND\n            <span className=\"bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent\"> in action</span>\n          </h2>\n          <p className=\"text-xl text-gray-300 max-w-3xl mx-auto\">\n            Watch how our AI transforms your marketing workflow in just 2 minutes\n          </p>\n        </motion.div>\n\n        <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-12 items-center\">\n          {/* Video Player */}\n          <motion.div\n            initial={{ opacity: 0, scale: 0.9 }}\n            whileInView={{ opacity: 1, scale: 1 }}\n            transition={{ duration: 0.8 }}\n            viewport={{ once: true }}\n            className=\"lg:col-span-2\"\n          >\n            <div className=\"relative aspect-video rounded-2xl overflow-hidden bg-gradient-to-br from-gray-800 to-gray-900 border border-white/10 shadow-2xl\">\n              {/* Video Placeholder */}\n              <div className=\"absolute inset-0 bg-gradient-to-br from-blue-900/20 via-purple-900/20 to-pink-900/20\">\n                <div className=\"absolute inset-0 flex items-center justify-center\">\n                  {!isPlaying ? (\n                    <motion.button\n                      onClick={() => setIsPlaying(true)}\n                      className=\"group relative\"\n                      whileHover={{ scale: 1.1 }}\n                      whileTap={{ scale: 0.95 }}\n                    >\n                      <div className=\"w-20 h-20 bg-white/10 backdrop-blur-md border border-white/20 rounded-full flex items-center justify-center group-hover:bg-white/20 transition-all duration-300\">\n                        <Play className=\"w-8 h-8 text-white ml-1\" fill=\"currentColor\" />\n                      </div>\n                      <div className=\"absolute inset-0 rounded-full bg-blue-500/20 animate-ping\"></div>\n                    </motion.button>\n                  ) : (\n                    <div className=\"w-full h-full bg-black/50 flex items-center justify-center\">\n                      <div className=\"text-white text-center\">\n                        <div className=\"w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4\"></div>\n                        <p>Loading demo video...</p>\n                      </div>\n                    </div>\n                  )}\n                </div>\n              </div>\n\n              {/* Video Controls Overlay */}\n              {isPlaying && (\n                <motion.div\n                  initial={{ opacity: 0 }}\n                  animate={{ opacity: 1 }}\n                  className=\"absolute bottom-4 left-4 right-4 bg-black/50 backdrop-blur-md rounded-lg p-3 flex items-center justify-between\"\n                >\n                  <div className=\"flex items-center gap-3\">\n                    <button className=\"text-white hover:text-blue-400 transition-colors\">\n                      <Play className=\"w-5 h-5\" />\n                    </button>\n                    <button className=\"text-white hover:text-blue-400 transition-colors\">\n                      <Volume2 className=\"w-5 h-5\" />\n                    </button>\n                    <div className=\"text-white text-sm\">2:15 / 2:15</div>\n                  </div>\n                  <button className=\"text-white hover:text-blue-400 transition-colors\">\n                    <Maximize className=\"w-5 h-5\" />\n                  </button>\n                </motion.div>\n              )}\n\n              {/* Floating elements */}\n              <motion.div\n                className=\"absolute top-4 left-4 bg-green-500/20 backdrop-blur-md border border-green-500/30 rounded-lg px-3 py-2 text-sm text-white\"\n                animate={{ y: [0, -5, 0] }}\n                transition={{ duration: 2, repeat: Infinity }}\n              >\n                ✓ Campaign Created\n              </motion.div>\n\n              <motion.div\n                className=\"absolute top-4 right-4 bg-blue-500/20 backdrop-blur-md border border-blue-500/30 rounded-lg px-3 py-2 text-sm text-white\"\n                animate={{ y: [0, 5, 0] }}\n                transition={{ duration: 2.5, repeat: Infinity, delay: 0.5 }}\n              >\n                📊 Analytics Ready\n              </motion.div>\n\n              <motion.div\n                className=\"absolute bottom-20 left-1/2 transform -translate-x-1/2 bg-purple-500/20 backdrop-blur-md border border-purple-500/30 rounded-lg px-3 py-2 text-sm text-white\"\n                animate={{ scale: [1, 1.05, 1] }}\n                transition={{ duration: 2, repeat: Infinity, delay: 1 }}\n              >\n                🚀 Content Published\n              </motion.div>\n            </div>\n          </motion.div>\n\n          {/* Stats and CTA */}\n          <motion.div\n            initial={{ opacity: 0, x: 30 }}\n            whileInView={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.8, delay: 0.2 }}\n            viewport={{ once: true }}\n            className=\"space-y-8\"\n          >\n            {/* Stats */}\n            <div className=\"space-y-6\">\n              {stats.map((stat, index) => (\n                <motion.div\n                  key={stat.label}\n                  initial={{ opacity: 0, x: 20 }}\n                  whileInView={{ opacity: 1, x: 0 }}\n                  transition={{ duration: 0.6, delay: index * 0.1 }}\n                  viewport={{ once: true }}\n                  className=\"flex items-center gap-4 bg-white/5 backdrop-blur-md border border-white/10 rounded-xl p-4\"\n                >\n                  <div className=\"w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-500 rounded-lg flex items-center justify-center\">\n                    <stat.icon className=\"w-6 h-6 text-white\" />\n                  </div>\n                  <div>\n                    <div className=\"text-2xl font-bold text-white\">{stat.value}</div>\n                    <div className=\"text-gray-400 text-sm\">{stat.label}</div>\n                  </div>\n                </motion.div>\n              ))}\n            </div>\n\n            {/* CTA */}\n            <div className=\"space-y-4\">\n              <Button className=\"w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white py-3 rounded-xl font-semibold\">\n                Start Free Trial\n              </Button>\n              <Button variant=\"outline\" className=\"w-full border-white/20 text-white hover:bg-white/10 backdrop-blur-md py-3 rounded-xl\">\n                Schedule Demo Call\n              </Button>\n            </div>\n\n            {/* Trust indicators */}\n            <div className=\"text-center text-sm text-gray-400 space-y-2\">\n              <div className=\"flex items-center justify-center gap-2\">\n                <div className=\"w-2 h-2 bg-green-400 rounded-full\"></div>\n                <span>No credit card required</span>\n              </div>\n              <div className=\"flex items-center justify-center gap-2\">\n                <div className=\"w-2 h-2 bg-blue-400 rounded-full\"></div>\n                <span>Cancel anytime</span>\n              </div>\n            </div>\n          </motion.div>\n        </div>\n      </div>\n    </section>\n  )\n}\n\nexport default DemoVideo\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;;;AANA;;;;;;AAQA,MAAM,YAAY;;IAChB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,QAAQ;QACZ;YAAE,MAAM,uMAAA,CAAA,QAAK;YAAE,OAAO;YAAS,OAAO;QAAc;QACpD;YAAE,MAAM,uMAAA,CAAA,QAAK;YAAE,OAAO;YAAQ,OAAO;QAAc;QACnD;YAAE,MAAM,qNAAA,CAAA,aAAU;YAAE,OAAO;YAAQ,OAAO;QAAe;KAC1D;IAED,qBACE,6LAAC;QAAQ,WAAU;kBACjB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAA<PERSON>,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAEV,6LAAC,oIAAA,CAAA,QAAK;4BAAC,WAAU;;8CACf,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;sCAGnC,6LAAC;4BAAG,WAAU;;gCAAiD;8CAE7D,6LAAC;oCAAK,WAAU;8CAA6E;;;;;;;;;;;;sCAE/F,6LAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;8BAKzD,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,OAAO;4BAAI;4BAClC,aAAa;gCAAE,SAAS;gCAAG,OAAO;4BAAE;4BACpC,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,UAAU;gCAAE,MAAM;4BAAK;4BACvB,WAAU;sCAEV,cAAA,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;sDACZ,CAAC,0BACA,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;gDACZ,SAAS,IAAM,aAAa;gDAC5B,WAAU;gDACV,YAAY;oDAAE,OAAO;gDAAI;gDACzB,UAAU;oDAAE,OAAO;gDAAK;;kEAExB,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;4DAAC,WAAU;4DAA0B,MAAK;;;;;;;;;;;kEAEjD,6LAAC;wDAAI,WAAU;;;;;;;;;;;yGAGjB,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;;;;;sEACf,6LAAC;sEAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;oCAQZ,2BACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,SAAS;wCAAE;wCACtB,SAAS;4CAAE,SAAS;wCAAE;wCACtB,WAAU;;0DAEV,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAO,WAAU;kEAChB,cAAA,6LAAC,qMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;;;;;;kEAElB,6LAAC;wDAAO,WAAU;kEAChB,cAAA,6LAAC,+MAAA,CAAA,UAAO;4DAAC,WAAU;;;;;;;;;;;kEAErB,6LAAC;wDAAI,WAAU;kEAAqB;;;;;;;;;;;;0DAEtC,6LAAC;gDAAO,WAAU;0DAChB,cAAA,6LAAC,6MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;;;;;;;;;;;;kDAM1B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,WAAU;wCACV,SAAS;4CAAE,GAAG;gDAAC;gDAAG,CAAC;gDAAG;6CAAE;wCAAC;wCACzB,YAAY;4CAAE,UAAU;4CAAG,QAAQ;wCAAS;kDAC7C;;;;;;kDAID,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,WAAU;wCACV,SAAS;4CAAE,GAAG;gDAAC;gDAAG;gDAAG;6CAAE;wCAAC;wCACxB,YAAY;4CAAE,UAAU;4CAAK,QAAQ;4CAAU,OAAO;wCAAI;kDAC3D;;;;;;kDAID,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,WAAU;wCACV,SAAS;4CAAE,OAAO;gDAAC;gDAAG;gDAAM;6CAAE;wCAAC;wCAC/B,YAAY;4CAAE,UAAU;4CAAG,QAAQ;4CAAU,OAAO;wCAAE;kDACvD;;;;;;;;;;;;;;;;;sCAOL,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,UAAU;gCAAE,MAAM;4BAAK;4BACvB,WAAU;;8CAGV,6LAAC;oCAAI,WAAU;8CACZ,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4CAET,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAG;4CAC7B,aAAa;gDAAE,SAAS;gDAAG,GAAG;4CAAE;4CAChC,YAAY;gDAAE,UAAU;gDAAK,OAAO,QAAQ;4CAAI;4CAChD,UAAU;gDAAE,MAAM;4CAAK;4CACvB,WAAU;;8DAEV,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,KAAK,IAAI;wDAAC,WAAU;;;;;;;;;;;8DAEvB,6LAAC;;sEACC,6LAAC;4DAAI,WAAU;sEAAiC,KAAK,KAAK;;;;;;sEAC1D,6LAAC;4DAAI,WAAU;sEAAyB,KAAK,KAAK;;;;;;;;;;;;;2CAZ/C,KAAK,KAAK;;;;;;;;;;8CAmBrB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,qIAAA,CAAA,SAAM;4CAAC,WAAU;sDAAuI;;;;;;sDAGzJ,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,WAAU;sDAAuF;;;;;;;;;;;;8CAM7H,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;;;;;8DACf,6LAAC;8DAAK;;;;;;;;;;;;sDAER,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;;;;;8DACf,6LAAC;8DAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQtB;GA7KM;KAAA;uCA+KS", "debugId": null}}, {"offset": {"line": 3490, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Landing%20page/admybrand-ai-suite/src/components/sections/faq.tsx"], "sourcesContent": ["'use client'\n\nimport { Badge } from '@/components/ui/badge'\nimport { Card, CardContent } from '@/components/ui/card'\nimport { AnimatePresence, motion } from 'framer-motion'\nimport { ChevronDown, HelpCircle } from 'lucide-react'\nimport { useState } from 'react'\n\nconst faqs = [\n  {\n    question: \"How does ADmyBRAND AI understand my brand voice?\",\n    answer: \"Our AI analyzes your existing content, brand guidelines, and communication style to create a unique brand voice profile. It learns from your tone, vocabulary, messaging patterns, and values to ensure all generated content maintains consistency with your brand identity.\"\n  },\n  {\n    question: \"Can I integrate ADmyBRAND with my existing marketing tools?\",\n    answer: \"Yes! ADmyBRAND integrates with over 100+ popular marketing tools including HubSpot, Salesforce, Mailchimp, Hootsuite, Google Analytics, Facebook Ads Manager, and many more. We also provide API access for custom integrations.\"\n  },\n  {\n    question: \"What kind of content can the AI generate?\",\n    answer: \"Our AI can create blog posts, social media content, email campaigns, ad copy, product descriptions, press releases, video scripts, landing page copy, and much more. It adapts to different formats, lengths, and platforms while maintaining your brand voice.\"\n  },\n  {\n    question: \"How accurate is the AI-generated content?\",\n    answer: \"Our AI achieves 95%+ accuracy in brand voice matching and content quality. All content goes through multiple AI validation layers, and you always have full editorial control. The AI learns and improves from your feedback and edits.\"\n  },\n  {\n    question: \"Is my data secure with ADmyBRAND?\",\n    answer: \"Absolutely. We use enterprise-grade security with SOC 2 Type II compliance, end-to-end encryption, and GDPR compliance. Your data is never used to train models for other customers, and you maintain full ownership of all content and data.\"\n  },\n  {\n    question: \"Can I cancel my subscription anytime?\",\n    answer: \"Yes, you can cancel your subscription at any time with no cancellation fees. Your account will remain active until the end of your current billing period, and you'll retain access to all your generated content and data.\"\n  },\n  {\n    question: \"Do you offer custom AI training for enterprise clients?\",\n    answer: \"Yes! Our Enterprise plan includes custom AI model training specifically for your brand, industry, and use cases. We can train the AI on your proprietary data, create custom workflows, and provide dedicated support for implementation.\"\n  },\n  {\n    question: \"How quickly can I see results with ADmyBRAND?\",\n    answer: \"Most customers see immediate improvements in content creation speed (80% faster) and start seeing engagement improvements within the first week. Full ROI optimization typically occurs within 30-60 days as the AI learns and optimizes your campaigns.\"\n  }\n]\n\nconst FAQ = () => {\n  const [openIndex, setOpenIndex] = useState<number | null>(0)\n\n  const toggleFAQ = (index: number) => {\n    setOpenIndex(openIndex === index ? null : index)\n  }\n\n  return (\n    <section className=\"py-24 bg-black\">\n      <div className=\"max-w-6xl mx-auto px-2 sm:px-4 lg:px-6\">\n        {/* Section Header */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          viewport={{ once: true }}\n          className=\"text-center mb-16\"\n        >\n          <Badge className=\"mb-4 bg-white/10 text-white border-white/20 backdrop-blur-sm\">\n            <HelpCircle className=\"w-4 h-4 mr-2\" />\n            FAQ\n          </Badge>\n          <h2 className=\"text-4xl md:text-5xl font-bold text-white mb-6\">\n            Frequently asked\n            <span className=\"bg-gradient-to-r from-blue-400 to-cyan-400 bg-clip-text text-transparent\"> questions</span>\n          </h2>\n          <p className=\"text-xl text-gray-300 max-w-2xl mx-auto\">\n            Everything you need to know about ADmyBRAND AI Suite. Can't find the answer you're looking for?\n            <span className=\"text-blue-400 font-medium\"> Contact our support team.</span>\n          </p>\n        </motion.div>\n\n        {/* FAQ Items - Compact Grid Layout */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-3\">\n          {faqs.map((faq, index) => (\n            <motion.div\n              key={index}\n              initial={{ opacity: 0, y: 20 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.5, delay: index * 0.05 }}\n              viewport={{ once: true }}\n            >\n              <Card className=\"border border-white/10 hover:border-white/20 transition-colors duration-300 bg-white/5 backdrop-blur-md hover:bg-white/10 h-full\">\n                <CardContent className=\"p-0\">\n                  <button\n                    onClick={() => toggleFAQ(index)}\n                    className=\"w-full text-left p-4 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-inset\"\n                  >\n                    <div className=\"flex items-center justify-between\">\n                      <h3 className=\"text-sm font-semibold text-white pr-3 leading-tight\">\n                        {faq.question}\n                      </h3>\n                      <motion.div\n                        animate={{ rotate: openIndex === index ? 180 : 0 }}\n                        transition={{ duration: 0.3 }}\n                        className=\"flex-shrink-0\"\n                      >\n                        <ChevronDown className=\"w-4 h-4 text-gray-400\" />\n                      </motion.div>\n                    </div>\n                  </button>\n\n                  <AnimatePresence>\n                    {openIndex === index && (\n                      <motion.div\n                        initial={{ height: 0, opacity: 0 }}\n                        animate={{ height: \"auto\", opacity: 1 }}\n                        exit={{ height: 0, opacity: 0 }}\n                        transition={{ duration: 0.3 }}\n                        className=\"overflow-hidden\"\n                      >\n                        <div className=\"px-4 pb-4\">\n                          <p className=\"text-gray-300 leading-relaxed text-sm\">\n                            {faq.answer}\n                          </p>\n                        </div>\n                      </motion.div>\n                    )}\n                  </AnimatePresence>\n                </CardContent>\n              </Card>\n            </motion.div>\n          ))}\n        </div>\n\n        {/* Bottom CTA */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8, delay: 0.4 }}\n          viewport={{ once: true }}\n          className=\"text-center mt-16\"\n        >\n          <div className=\"bg-gradient-to-r from-purple-50 to-blue-50 rounded-2xl p-8\">\n            <h3 className=\"text-2xl font-bold text-gray-900 mb-4\">\n              Still have questions?\n            </h3>\n            <p className=\"text-gray-600 mb-6\">\n              Our support team is available 24/7 to help you get the most out of ADmyBRAND AI Suite.\n            </p>\n            <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n              <motion.button\n                whileHover={{ scale: 1.05 }}\n                whileTap={{ scale: 0.95 }}\n                className=\"px-6 py-3 bg-gradient-to-r from-purple-600 to-blue-600 text-white rounded-lg font-medium shadow-lg hover:shadow-xl transition-all duration-300\"\n              >\n                Contact Support\n              </motion.button>\n              <motion.button\n                whileHover={{ scale: 1.05 }}\n                whileTap={{ scale: 0.95 }}\n                className=\"px-6 py-3 border border-gray-300 text-gray-700 rounded-lg font-medium hover:bg-gray-50 transition-all duration-300\"\n              >\n                Schedule Demo\n              </motion.button>\n            </div>\n          </div>\n        </motion.div>\n      </div>\n    </section>\n  )\n}\n\nexport default FAQ\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AACA;AAAA;AACA;;;AANA;;;;;;AAQA,MAAM,OAAO;IACX;QACE,UAAU;QACV,QAAQ;IACV;IACA;QACE,UAAU;QACV,QAAQ;IACV;IACA;QACE,UAAU;QACV,QAAQ;IACV;IACA;QACE,UAAU;QACV,QAAQ;IACV;IACA;QACE,UAAU;QACV,QAAQ;IACV;IACA;QACE,UAAU;QACV,QAAQ;IACV;IACA;QACE,UAAU;QACV,QAAQ;IACV;IACA;QACE,UAAU;QACV,QAAQ;IACV;CACD;AAED,MAAM,MAAM;;IACV,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAE1D,MAAM,YAAY,CAAC;QACjB,aAAa,cAAc,QAAQ,OAAO;IAC5C;IAEA,qBACE,6LAAC;QAAQ,WAAU;kBACjB,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAEV,6LAAC,oIAAA,CAAA,QAAK;4BAAC,WAAU;;8CACf,6LAAC,iOAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;sCAGzC,6LAAC;4BAAG,WAAU;;gCAAiD;8CAE7D,6LAAC;oCAAK,WAAU;8CAA2E;;;;;;;;;;;;sCAE7F,6LAAC;4BAAE,WAAU;;gCAA0C;8CAErD,6LAAC;oCAAK,WAAU;8CAA4B;;;;;;;;;;;;;;;;;;8BAKhD,6LAAC;oBAAI,WAAU;8BACZ,KAAK,GAAG,CAAC,CAAC,KAAK,sBACd,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO,QAAQ;4BAAK;4BACjD,UAAU;gCAAE,MAAM;4BAAK;sCAEvB,cAAA,6LAAC,mIAAA,CAAA,OAAI;gCAAC,WAAU;0CACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;oCAAC,WAAU;;sDACrB,6LAAC;4CACC,SAAS,IAAM,UAAU;4CACzB,WAAU;sDAEV,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEACX,IAAI,QAAQ;;;;;;kEAEf,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wDACT,SAAS;4DAAE,QAAQ,cAAc,QAAQ,MAAM;wDAAE;wDACjD,YAAY;4DAAE,UAAU;wDAAI;wDAC5B,WAAU;kEAEV,cAAA,6LAAC,uNAAA,CAAA,cAAW;4DAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;sDAK7B,6LAAC,4LAAA,CAAA,kBAAe;sDACb,cAAc,uBACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,SAAS;oDAAE,QAAQ;oDAAG,SAAS;gDAAE;gDACjC,SAAS;oDAAE,QAAQ;oDAAQ,SAAS;gDAAE;gDACtC,MAAM;oDAAE,QAAQ;oDAAG,SAAS;gDAAE;gDAC9B,YAAY;oDAAE,UAAU;gDAAI;gDAC5B,WAAU;0DAEV,cAAA,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAE,WAAU;kEACV,IAAI,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2BArCpB;;;;;;;;;;8BAkDX,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;8BAEV,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAwC;;;;;;0CAGtD,6LAAC;gCAAE,WAAU;0CAAqB;;;;;;0CAGlC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;wCACZ,YAAY;4CAAE,OAAO;wCAAK;wCAC1B,UAAU;4CAAE,OAAO;wCAAK;wCACxB,WAAU;kDACX;;;;;;kDAGD,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;wCACZ,YAAY;4CAAE,OAAO;wCAAK;wCAC1B,UAAU;4CAAE,OAAO;wCAAK;wCACxB,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf;GAzHM;KAAA;uCA2HS", "debugId": null}}, {"offset": {"line": 3872, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Landing%20page/admybrand-ai-suite/src/components/sections/feature-showcase.tsx"], "sourcesContent": ["'use client'\n\nimport { Badge } from '@/components/ui/badge'\nimport { But<PERSON> } from '@/components/ui/button'\nimport { motion } from 'framer-motion'\nimport { BarChart3, Mail, Monitor, PenTool } from 'lucide-react'\nimport { useState } from 'react'\n\nconst FeatureShowcase = () => {\n  const [activeFeature, setActiveFeature] = useState(0)\n\n  const features = [\n    {\n      id: 0,\n      icon: Monitor,\n      name: \"AI Ad Creation\",\n      title: \"Effortless, On-Brand AI Ads — In Minutes\",\n      description: \"Turn any URL into stunning, brand-aligned video and image ads. Just paste your link, and <PERSON>mne<PERSON> instantly generates 5-10 ready-to-launch creatives tailored to your brand.\",\n      categories: [\"General\", \"Announcements\", \"Discounts\", \"Lifestyle Visuals\", \"Product Launches\", \"Testimonial Ads\", \"With People\"],\n      mockupType: \"ads\",\n      color: \"from-pink-500 to-rose-500\"\n    },\n    {\n      id: 1,\n      icon: PenTool,\n      name: \"AI Content Generator\",\n      title: \"Create Compelling Content — Instantly\",\n      description: \"Generate blog posts, social media content, and marketing copy that converts. Our AI understands your brand voice and creates content that resonates with your audience.\",\n      categories: [\"Blog Posts\", \"Social Media\", \"Email Copy\", \"Product Descriptions\", \"Headlines\", \"CTAs\"],\n      mockupType: \"content\",\n      color: \"from-purple-500 to-pink-500\"\n    },\n    {\n      id: 2,\n      icon: Mail,\n      name: \"Email Marketing AI\",\n      title: \"Personalized Email Campaigns — At Scale\",\n      description: \"Create email campaigns that convert. Generate subject lines, content, and optimal send times that increase open rates and drive revenue.\",\n      categories: [\"Welcome Series\", \"Product Updates\", \"Newsletters\", \"Promotional\", \"Abandoned Cart\", \"Re-engagement\"],\n      mockupType: \"email\",\n      color: \"from-indigo-500 to-purple-500\"\n    },\n    {\n      id: 3,\n      icon: BarChart3,\n      name: \"Analytics Dashboard\",\n      title: \"AI-Powered Marketing Insights — Real-Time\",\n      description: \"Get deep insights into your marketing performance with AI-powered analytics that provide actionable recommendations to improve your ROI.\",\n      categories: [\"Performance\", \"Audience\", \"Conversion\", \"Revenue\", \"Trends\", \"Predictions\"],\n      mockupType: \"analytics\",\n      color: \"from-yellow-500 to-orange-500\"\n    }\n  ]\n\n  const renderMockup = (feature) => {\n    switch (feature.mockupType) {\n      case 'ads':\n        return (\n          <div className=\"space-y-6\">\n            {/* Video Ad Preview */}\n            <div className=\"bg-white/90 backdrop-blur-sm rounded-xl p-6 shadow-2xl border border-white/30\">\n              <div className=\"flex items-center justify-between mb-4\">\n                <h4 className=\"font-semibold text-gray-800\">Video Ad Preview</h4>\n                <Badge className=\"bg-red-100 text-red-600\">Live Demo</Badge>\n              </div>\n              <div className=\"relative w-full h-48 bg-gradient-to-br from-purple-600 via-blue-600 to-cyan-500 rounded-lg overflow-hidden group cursor-pointer\">\n                {/* Video placeholder with play button */}\n                <div className=\"absolute inset-0 flex items-center justify-center\">\n                  <motion.div\n                    className=\"w-16 h-16 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center group-hover:bg-white/30 transition-all duration-300\"\n                    whileHover={{ scale: 1.1 }}\n                    whileTap={{ scale: 0.95 }}\n                  >\n                    <div className=\"w-0 h-0 border-l-[12px] border-l-white border-t-[8px] border-t-transparent border-b-[8px] border-b-transparent ml-1\"></div>\n                  </motion.div>\n                </div>\n                {/* Video content overlay */}\n                <div className=\"absolute bottom-4 left-4 right-4\">\n                  <h3 className=\"text-white font-bold text-lg mb-1\">Transform Your Business</h3>\n                  <p className=\"text-white/90 text-sm\">AI-powered marketing that delivers results</p>\n                </div>\n                {/* Video duration */}\n                <div className=\"absolute top-4 right-4 bg-black/50 text-white text-xs px-2 py-1 rounded\">0:30</div>\n              </div>\n            </div>\n\n            {/* Static Ad Examples Grid */}\n            <div className=\"grid grid-cols-2 lg:grid-cols-4 gap-4\">\n              {/* Baby Food Ad */}\n              <motion.div\n                className=\"bg-white/95 backdrop-blur-sm rounded-xl p-4 shadow-lg hover:shadow-xl transition-all duration-300 border border-white/40\"\n                whileHover={{ y: -5 }}\n              >\n                <div className=\"relative w-full h-32 bg-gradient-to-br from-orange-400 to-pink-500 rounded-lg mb-3 overflow-hidden\">\n                  <div className=\"absolute inset-0 bg-black/20\"></div>\n                  <div className=\"absolute bottom-2 left-2 right-2\">\n                    <span className=\"text-white font-bold text-sm block\">Meals Made For Your Baby</span>\n                  </div>\n                  <div className=\"absolute top-2 right-2 w-6 h-6 bg-orange-500 rounded-full flex items-center justify-center\">\n                    <span className=\"text-white text-xs font-bold\">$5</span>\n                  </div>\n                </div>\n                <p className=\"text-xs text-gray-600 mb-2\">Custom nutrition for your child</p>\n                <Button size=\"sm\" className=\"w-full bg-orange-500 hover:bg-orange-600 text-white text-xs\">Learn More</Button>\n              </motion.div>\n\n              {/* Investment App Ad */}\n              <motion.div\n                className=\"bg-white/95 backdrop-blur-sm rounded-xl p-4 shadow-lg hover:shadow-xl transition-all duration-300 border border-white/40\"\n                whileHover={{ y: -5 }}\n              >\n                <div className=\"relative w-full h-32 bg-gradient-to-br from-green-600 to-blue-600 rounded-lg mb-3 overflow-hidden\">\n                  <div className=\"absolute inset-0 bg-black/20\"></div>\n                  <div className=\"absolute bottom-2 left-2 right-2\">\n                    <span className=\"text-white font-bold text-sm block\">Investing Made For You</span>\n                  </div>\n                  <div className=\"absolute top-2 left-2 text-white text-xs\">📈 +12.5%</div>\n                </div>\n                <p className=\"text-xs text-gray-600 mb-2\">Simple, Easy-to-use app</p>\n                <Button size=\"sm\" className=\"w-full bg-green-600 hover:bg-green-700 text-white text-xs\">Learn More</Button>\n              </motion.div>\n\n              {/* Pet Services Ad */}\n              <motion.div\n                className=\"bg-white/95 backdrop-blur-sm rounded-xl p-4 shadow-lg hover:shadow-xl transition-all duration-300 border border-white/40\"\n                whileHover={{ y: -5 }}\n              >\n                <div className=\"relative w-full h-32 bg-gradient-to-br from-blue-400 to-purple-500 rounded-lg mb-3 overflow-hidden\">\n                  <div className=\"absolute inset-0 bg-black/20\"></div>\n                  <div className=\"absolute bottom-2 left-2 right-2\">\n                    <span className=\"text-white font-bold text-sm block\">Trusted by Local Pet Owners</span>\n                  </div>\n                  <div className=\"absolute top-2 right-2 text-white text-xs\">🐕 4.9★</div>\n                </div>\n                <p className=\"text-xs text-gray-600 mb-2\">Join happy pet families today</p>\n                <Button size=\"sm\" className=\"w-full bg-blue-600 hover:bg-blue-700 text-white text-xs\">Learn More</Button>\n              </motion.div>\n\n              {/* Adventure/Clothing Ad */}\n              <motion.div\n                className=\"bg-white/95 backdrop-blur-sm rounded-xl p-4 shadow-lg hover:shadow-xl transition-all duration-300 border border-white/40\"\n                whileHover={{ y: -5 }}\n              >\n                <div className=\"relative w-full h-32 bg-gradient-to-br from-gray-800 to-yellow-500 rounded-lg mb-3 overflow-hidden\">\n                  <div className=\"absolute inset-0 bg-black/30\"></div>\n                  <div className=\"absolute bottom-2 left-2 right-2\">\n                    <span className=\"text-white font-bold text-sm block\">Adventure Awaits</span>\n                  </div>\n                  <div className=\"absolute top-2 left-2 text-yellow-400 text-xs\">⚡ LIMITED</div>\n                </div>\n                <p className=\"text-xs text-gray-600 mb-2\">Miss it, miss the journey</p>\n                <Button size=\"sm\" className=\"w-full bg-gray-800 hover:bg-gray-900 text-white text-xs\">Learn More</Button>\n              </motion.div>\n            </div>\n          </div>\n        )\n      \n      case 'content':\n        return (\n          <div className=\"space-y-6\">\n            {/* Content Generation Interface */}\n            <div className=\"bg-white rounded-xl p-6 shadow-2xl\">\n              <div className=\"flex items-center justify-between mb-4\">\n                <h4 className=\"font-semibold text-gray-800\">AI Content Generator</h4>\n                <Badge className=\"bg-green-100 text-green-600\">Generating...</Badge>\n              </div>\n\n              {/* Input Section */}\n              <div className=\"bg-gray-50 rounded-lg p-4 mb-4\">\n                <label className=\"text-sm font-medium text-gray-700 block mb-2\">Topic:</label>\n                <div className=\"bg-white border rounded-lg p-3 text-sm text-gray-600\">\n                  \"AI marketing trends for small businesses\"\n                </div>\n              </div>\n\n              {/* Generated Content */}\n              <div className=\"space-y-4\">\n                <motion.div\n                  className=\"border-l-4 border-blue-500 pl-4 bg-blue-50 p-3 rounded-r-lg\"\n                  initial={{ opacity: 0, x: -20 }}\n                  animate={{ opacity: 1, x: 0 }}\n                  transition={{ delay: 0.2 }}\n                >\n                  <div className=\"flex items-center justify-between mb-2\">\n                    <h4 className=\"font-semibold text-gray-800\">Blog Post Title</h4>\n                    <span className=\"text-xs text-blue-600 bg-blue-100 px-2 py-1 rounded\">SEO Score: 95</span>\n                  </div>\n                  <p className=\"text-sm text-gray-700\">\"10 AI Marketing Trends That Will Transform Small Businesses in 2024\"</p>\n                </motion.div>\n\n                <motion.div\n                  className=\"border-l-4 border-green-500 pl-4 bg-green-50 p-3 rounded-r-lg\"\n                  initial={{ opacity: 0, x: -20 }}\n                  animate={{ opacity: 1, x: 0 }}\n                  transition={{ delay: 0.4 }}\n                >\n                  <div className=\"flex items-center justify-between mb-2\">\n                    <h4 className=\"font-semibold text-gray-800\">Social Media Caption</h4>\n                    <span className=\"text-xs text-green-600 bg-green-100 px-2 py-1 rounded\">Engagement: High</span>\n                  </div>\n                  <p className=\"text-sm text-gray-700\">\"🚀 Ready to 10x your marketing results? AI is changing the game for small businesses! Here's what you need to know... #AIMarketing #SmallBusiness\"</p>\n                </motion.div>\n\n                <motion.div\n                  className=\"border-l-4 border-purple-500 pl-4 bg-purple-50 p-3 rounded-r-lg\"\n                  initial={{ opacity: 0, x: -20 }}\n                  animate={{ opacity: 1, x: 0 }}\n                  transition={{ delay: 0.6 }}\n                >\n                  <div className=\"flex items-center justify-between mb-2\">\n                    <h4 className=\"font-semibold text-gray-800\">Email Subject Line</h4>\n                    <span className=\"text-xs text-purple-600 bg-purple-100 px-2 py-1 rounded\">Open Rate: 47%</span>\n                  </div>\n                  <p className=\"text-sm text-gray-700\">\"[Name], your competitors are using AI (are you?)\"</p>\n                </motion.div>\n              </div>\n            </div>\n          </div>\n        )\n      \n      case 'email':\n        return (\n          <div className=\"space-y-6\">\n            {/* Email Campaign Builder */}\n            <div className=\"bg-white rounded-xl p-6 shadow-2xl\">\n              <div className=\"flex items-center justify-between mb-4\">\n                <h4 className=\"font-semibold text-gray-800\">Email Campaign Builder</h4>\n                <Badge className=\"bg-blue-100 text-blue-600\">AI Optimized</Badge>\n              </div>\n\n              {/* Email Preview */}\n              <div className=\"border rounded-lg overflow-hidden mb-4\">\n                {/* Email Header */}\n                <div className=\"bg-gradient-to-r from-blue-600 to-purple-600 p-4 text-white\">\n                  <div className=\"flex items-center justify-between\">\n                    <span className=\"font-semibold\">ADmyBRAND AI Suite</span>\n                    <span className=\"text-xs bg-white/20 px-2 py-1 rounded\">Premium</span>\n                  </div>\n                </div>\n\n                {/* Email Content */}\n                <div className=\"p-4 bg-gray-50\">\n                  <h3 className=\"font-bold text-lg text-gray-800 mb-2\">Hi Sarah! 👋</h3>\n                  <p className=\"text-sm text-gray-700 mb-3\">\n                    Your marketing campaigns could be performing 250% better with AI optimization...\n                  </p>\n                  <div className=\"bg-white p-3 rounded-lg border mb-3\">\n                    <div className=\"flex items-center justify-between\">\n                      <span className=\"text-sm font-medium\">Your Current Performance:</span>\n                      <span className=\"text-xs text-gray-500\">Last 30 days</span>\n                    </div>\n                    <div className=\"grid grid-cols-3 gap-2 mt-2\">\n                      <div className=\"text-center\">\n                        <div className=\"text-lg font-bold text-red-600\">2.3%</div>\n                        <div className=\"text-xs text-gray-600\">Open Rate</div>\n                      </div>\n                      <div className=\"text-center\">\n                        <div className=\"text-lg font-bold text-orange-600\">0.8%</div>\n                        <div className=\"text-xs text-gray-600\">Click Rate</div>\n                      </div>\n                      <div className=\"text-center\">\n                        <div className=\"text-lg font-bold text-gray-600\">$1.2K</div>\n                        <div className=\"text-xs text-gray-600\">Revenue</div>\n                      </div>\n                    </div>\n                  </div>\n                  <Button className=\"w-full bg-gradient-to-r from-blue-600 to-purple-600 text-white\">\n                    Boost My Results with AI\n                  </Button>\n                </div>\n              </div>\n\n              {/* Performance Metrics */}\n              <div className=\"grid grid-cols-3 gap-4 text-center\">\n                <div className=\"bg-green-50 p-3 rounded-lg\">\n                  <div className=\"text-lg font-bold text-green-600\">47.8%</div>\n                  <div className=\"text-xs text-gray-600\">Predicted Open Rate</div>\n                </div>\n                <div className=\"bg-blue-50 p-3 rounded-lg\">\n                  <div className=\"text-lg font-bold text-blue-600\">12.4%</div>\n                  <div className=\"text-xs text-gray-600\">Predicted Click Rate</div>\n                </div>\n                <div className=\"bg-purple-50 p-3 rounded-lg\">\n                  <div className=\"text-lg font-bold text-purple-600\">$8.7K</div>\n                  <div className=\"text-xs text-gray-600\">Projected Revenue</div>\n                </div>\n              </div>\n            </div>\n          </div>\n        )\n      \n      case 'analytics':\n        return (\n          <div className=\"space-y-6\">\n            {/* Analytics Dashboard */}\n            <div className=\"bg-white rounded-xl p-6 shadow-2xl\">\n              <div className=\"flex items-center justify-between mb-6\">\n                <h4 className=\"font-semibold text-gray-800\">AI Analytics Dashboard</h4>\n                <div className=\"flex items-center gap-2\">\n                  <div className=\"w-2 h-2 bg-green-500 rounded-full animate-pulse\"></div>\n                  <span className=\"text-xs text-gray-600\">Live Data</span>\n                </div>\n              </div>\n\n              {/* Key Metrics */}\n              <div className=\"grid grid-cols-2 lg:grid-cols-4 gap-4 mb-6\">\n                <motion.div\n                  className=\"bg-gradient-to-br from-green-50 to-green-100 p-4 rounded-xl border border-green-200\"\n                  initial={{ opacity: 0, y: 20 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ delay: 0.1 }}\n                >\n                  <div className=\"text-2xl font-bold text-green-600\">+247%</div>\n                  <div className=\"text-sm text-gray-600\">Conversion Rate</div>\n                  <div className=\"text-xs text-green-600 mt-1\">↗ +12% this week</div>\n                </motion.div>\n\n                <motion.div\n                  className=\"bg-gradient-to-br from-blue-50 to-blue-100 p-4 rounded-xl border border-blue-200\"\n                  initial={{ opacity: 0, y: 20 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ delay: 0.2 }}\n                >\n                  <div className=\"text-2xl font-bold text-blue-600\">$127K</div>\n                  <div className=\"text-sm text-gray-600\">Revenue Generated</div>\n                  <div className=\"text-xs text-blue-600 mt-1\">↗ +34% this month</div>\n                </motion.div>\n\n                <motion.div\n                  className=\"bg-gradient-to-br from-purple-50 to-purple-100 p-4 rounded-xl border border-purple-200\"\n                  initial={{ opacity: 0, y: 20 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ delay: 0.3 }}\n                >\n                  <div className=\"text-2xl font-bold text-purple-600\">89%</div>\n                  <div className=\"text-sm text-gray-600\">Cost Reduction</div>\n                  <div className=\"text-xs text-purple-600 mt-1\">↗ Optimized</div>\n                </motion.div>\n\n                <motion.div\n                  className=\"bg-gradient-to-br from-orange-50 to-orange-100 p-4 rounded-xl border border-orange-200\"\n                  initial={{ opacity: 0, y: 20 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ delay: 0.4 }}\n                >\n                  <div className=\"text-2xl font-bold text-orange-600\">15min</div>\n                  <div className=\"text-sm text-gray-600\">Setup Time</div>\n                  <div className=\"text-xs text-orange-600 mt-1\">⚡ Instant</div>\n                </motion.div>\n              </div>\n\n              {/* Chart Placeholder */}\n              <div className=\"bg-gray-50 rounded-lg p-4 mb-4\">\n                <div className=\"flex items-center justify-between mb-3\">\n                  <span className=\"text-sm font-medium text-gray-700\">Performance Trend</span>\n                  <span className=\"text-xs text-gray-500\">Last 30 days</span>\n                </div>\n                <div className=\"h-32 bg-gradient-to-r from-blue-100 via-purple-100 to-pink-100 rounded-lg flex items-end justify-between p-4\">\n                  {/* Simulated chart bars */}\n                  {[40, 65, 45, 80, 60, 90, 75, 95].map((height, index) => (\n                    <motion.div\n                      key={index}\n                      className=\"bg-gradient-to-t from-blue-500 to-purple-500 rounded-t\"\n                      style={{ height: `${height}%`, width: '8%' }}\n                      initial={{ height: 0 }}\n                      animate={{ height: `${height}%` }}\n                      transition={{ delay: index * 0.1, duration: 0.5 }}\n                    />\n                  ))}\n                </div>\n              </div>\n\n              {/* AI Recommendations */}\n              <div className=\"bg-blue-50 border border-blue-200 rounded-lg p-4\">\n                <div className=\"flex items-center gap-2 mb-2\">\n                  <div className=\"w-4 h-4 bg-blue-500 rounded-full flex items-center justify-center\">\n                    <span className=\"text-white text-xs\">AI</span>\n                  </div>\n                  <span className=\"text-sm font-medium text-blue-800\">Smart Recommendations</span>\n                </div>\n                <p className=\"text-sm text-blue-700\">\n                  Increase your ad spend on Facebook by 23% and reduce Google Ads budget by 15% to optimize ROI.\n                </p>\n              </div>\n            </div>\n          </div>\n        )\n      \n      default:\n        return null\n    }\n  }\n\n  return (\n    <section className=\"py-24 bg-gradient-to-br from-gray-900 via-black to-gray-900\">\n      <div className=\"max-w-7xl mx-auto px-2 sm:px-4 lg:px-6\">\n        {/* Section Header */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          viewport={{ once: true }}\n          className=\"text-center mb-16\"\n        >\n          <Badge className=\"mb-4 bg-white/10 text-white border-white/20 backdrop-blur-sm\">\n            Feature Showcase\n          </Badge>\n          <h2 className=\"text-4xl md:text-5xl font-bold text-white mb-6\">\n            See Our AI Tools\n            <span className=\"bg-gradient-to-r from-blue-400 to-cyan-400 bg-clip-text text-transparent\"> In Action</span>\n          </h2>\n          <p className=\"text-xl text-gray-300 max-w-3xl mx-auto\">\n            Experience how our AI-powered features transform your marketing workflow with real examples and live demonstrations.\n          </p>\n        </motion.div>\n\n        {/* Glass Morphism Wrapper */}\n        <div className=\"relative bg-white/5 backdrop-blur-xl border border-white/10 rounded-3xl p-8 md:p-12 shadow-2xl\">\n          {/* Glass effect overlay */}\n          <div className=\"absolute inset-0 bg-gradient-to-br from-white/10 via-white/5 to-transparent rounded-3xl pointer-events-none\"></div>\n\n          {/* Content */}\n          <div className=\"relative z-10\">\n            {/* Feature Navigation */}\n            <div className=\"flex flex-wrap justify-center gap-4 mb-12\">\n              {features.map((feature, index) => (\n                <motion.button\n                  key={feature.id}\n                  onClick={() => setActiveFeature(index)}\n                  className={`flex items-center gap-2 px-6 py-3 rounded-full transition-all duration-300 ${\n                    activeFeature === index\n                      ? 'bg-white text-black shadow-lg'\n                      : 'bg-white/10 text-white hover:bg-white/20 border border-white/20'\n                  }`}\n                  whileHover={{ scale: 1.05 }}\n                  whileTap={{ scale: 0.95 }}\n                >\n                  <feature.icon className=\"w-4 h-4\" />\n                  <span className=\"font-medium\">{feature.name}</span>\n                </motion.button>\n              ))}\n            </div>\n\n            {/* Active Feature Display */}\n            <motion.div\n              key={activeFeature}\n              initial={{ opacity: 0, y: 30 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.5 }}\n              className=\"grid lg:grid-cols-2 gap-12 items-center\"\n            >\n              {/* Feature Info */}\n              <div className=\"space-y-6\">\n                <div>\n                  <h3 className=\"text-3xl md:text-4xl font-bold text-white mb-4\">\n                    {features[activeFeature].title}\n                  </h3>\n                  <p className=\"text-lg text-gray-300 leading-relaxed\">\n                    {features[activeFeature].description}\n                  </p>\n                </div>\n\n                <Button className=\"bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 text-lg\">\n                  Try Now for Free\n                </Button>\n              </div>\n\n              {/* Feature Mockup */}\n              <div className=\"relative\">\n                <motion.div\n                  initial={{ opacity: 0, scale: 0.9 }}\n                  animate={{ opacity: 1, scale: 1 }}\n                  transition={{ duration: 0.5, delay: 0.2 }}\n                  className=\"relative\"\n                >\n                  {/* Glass Morphism Container */}\n                  <div className=\"relative bg-white/10 backdrop-blur-xl border border-white/20 rounded-3xl p-8 shadow-2xl\">\n                    {/* Glass effect overlay */}\n                    <div className=\"absolute inset-0 bg-gradient-to-br from-white/20 via-white/10 to-transparent rounded-3xl\"></div>\n\n                    {/* Content */}\n                    <div className=\"relative z-10\">\n                      {renderMockup(features[activeFeature])}\n                    </div>\n\n                    {/* Additional glass shine effect */}\n                    <div className=\"absolute top-0 left-0 w-full h-full bg-gradient-to-br from-white/30 via-transparent to-transparent rounded-3xl opacity-50 pointer-events-none\"></div>\n                  </div>\n                </motion.div>\n              </div>\n            </motion.div>\n          </div>\n\n          {/* Additional glass shine effect for wrapper */}\n          <div className=\"absolute top-0 left-0 w-full h-full bg-gradient-to-br from-white/20 via-transparent to-transparent rounded-3xl opacity-30 pointer-events-none\"></div>\n        </div>\n      </div>\n    </section>\n  )\n}\n\nexport default FeatureShowcase\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;;;AANA;;;;;;AAQA,MAAM,kBAAkB;;IACtB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,MAAM,WAAW;QACf;YACE,IAAI;YACJ,MAAM,2MAAA,CAAA,UAAO;YACb,MAAM;YACN,OAAO;YACP,aAAa;YACb,YAAY;gBAAC;gBAAW;gBAAiB;gBAAa;gBAAqB;gBAAoB;gBAAmB;aAAc;YAChI,YAAY;YACZ,OAAO;QACT;QACA;YACE,IAAI;YACJ,MAAM,+MAAA,CAAA,UAAO;YACb,MAAM;YACN,OAAO;YACP,aAAa;YACb,YAAY;gBAAC;gBAAc;gBAAgB;gBAAc;gBAAwB;gBAAa;aAAO;YACrG,YAAY;YACZ,OAAO;QACT;QACA;YACE,IAAI;YACJ,MAAM,qMAAA,CAAA,OAAI;YACV,MAAM;YACN,OAAO;YACP,aAAa;YACb,YAAY;gBAAC;gBAAkB;gBAAmB;gBAAe;gBAAe;gBAAkB;aAAgB;YAClH,YAAY;YACZ,OAAO;QACT;QACA;YACE,IAAI;YACJ,MAAM,qNAAA,CAAA,YAAS;YACf,MAAM;YACN,OAAO;YACP,aAAa;YACb,YAAY;gBAAC;gBAAe;gBAAY;gBAAc;gBAAW;gBAAU;aAAc;YACzF,YAAY;YACZ,OAAO;QACT;KACD;IAED,MAAM,eAAe,CAAC;QACpB,OAAQ,QAAQ,UAAU;YACxB,KAAK;gBACH,qBACE,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAA8B;;;;;;sDAC5C,6LAAC,oIAAA,CAAA,QAAK;4CAAC,WAAU;sDAA0B;;;;;;;;;;;;8CAE7C,6LAAC;oCAAI,WAAU;;sDAEb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,WAAU;gDACV,YAAY;oDAAE,OAAO;gDAAI;gDACzB,UAAU;oDAAE,OAAO;gDAAK;0DAExB,cAAA,6LAAC;oDAAI,WAAU;;;;;;;;;;;;;;;;sDAInB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAAoC;;;;;;8DAClD,6LAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;sDAGvC,6LAAC;4CAAI,WAAU;sDAA0E;;;;;;;;;;;;;;;;;;sCAK7F,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,WAAU;oCACV,YAAY;wCAAE,GAAG,CAAC;oCAAE;;sDAEpB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;;;;;8DACf,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAK,WAAU;kEAAqC;;;;;;;;;;;8DAEvD,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAK,WAAU;kEAA+B;;;;;;;;;;;;;;;;;sDAGnD,6LAAC;4CAAE,WAAU;sDAA6B;;;;;;sDAC1C,6LAAC,qIAAA,CAAA,SAAM;4CAAC,MAAK;4CAAK,WAAU;sDAA8D;;;;;;;;;;;;8CAI5F,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,WAAU;oCACV,YAAY;wCAAE,GAAG,CAAC;oCAAE;;sDAEpB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;;;;;8DACf,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAK,WAAU;kEAAqC;;;;;;;;;;;8DAEvD,6LAAC;oDAAI,WAAU;8DAA2C;;;;;;;;;;;;sDAE5D,6LAAC;4CAAE,WAAU;sDAA6B;;;;;;sDAC1C,6LAAC,qIAAA,CAAA,SAAM;4CAAC,MAAK;4CAAK,WAAU;sDAA4D;;;;;;;;;;;;8CAI1F,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,WAAU;oCACV,YAAY;wCAAE,GAAG,CAAC;oCAAE;;sDAEpB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;;;;;8DACf,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAK,WAAU;kEAAqC;;;;;;;;;;;8DAEvD,6LAAC;oDAAI,WAAU;8DAA4C;;;;;;;;;;;;sDAE7D,6LAAC;4CAAE,WAAU;sDAA6B;;;;;;sDAC1C,6LAAC,qIAAA,CAAA,SAAM;4CAAC,MAAK;4CAAK,WAAU;sDAA0D;;;;;;;;;;;;8CAIxF,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,WAAU;oCACV,YAAY;wCAAE,GAAG,CAAC;oCAAE;;sDAEpB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;;;;;8DACf,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAK,WAAU;kEAAqC;;;;;;;;;;;8DAEvD,6LAAC;oDAAI,WAAU;8DAAgD;;;;;;;;;;;;sDAEjE,6LAAC;4CAAE,WAAU;sDAA6B;;;;;;sDAC1C,6LAAC,qIAAA,CAAA,SAAM;4CAAC,MAAK;4CAAK,WAAU;sDAA0D;;;;;;;;;;;;;;;;;;;;;;;;YAMhG,KAAK;gBACH,qBACE,6LAAC;oBAAI,WAAU;8BAEb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAA8B;;;;;;kDAC5C,6LAAC,oIAAA,CAAA,QAAK;wCAAC,WAAU;kDAA8B;;;;;;;;;;;;0CAIjD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAM,WAAU;kDAA+C;;;;;;kDAChE,6LAAC;wCAAI,WAAU;kDAAuD;;;;;;;;;;;;0CAMxE,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,WAAU;wCACV,SAAS;4CAAE,SAAS;4CAAG,GAAG,CAAC;wCAAG;wCAC9B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,OAAO;wCAAI;;0DAEzB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEAA8B;;;;;;kEAC5C,6LAAC;wDAAK,WAAU;kEAAsD;;;;;;;;;;;;0DAExE,6LAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;kDAGvC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,WAAU;wCACV,SAAS;4CAAE,SAAS;4CAAG,GAAG,CAAC;wCAAG;wCAC9B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,OAAO;wCAAI;;0DAEzB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEAA8B;;;;;;kEAC5C,6LAAC;wDAAK,WAAU;kEAAwD;;;;;;;;;;;;0DAE1E,6LAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;kDAGvC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,WAAU;wCACV,SAAS;4CAAE,SAAS;4CAAG,GAAG,CAAC;wCAAG;wCAC9B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,OAAO;wCAAI;;0DAEzB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEAA8B;;;;;;kEAC5C,6LAAC;wDAAK,WAAU;kEAA0D;;;;;;;;;;;;0DAE5E,6LAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAOjD,KAAK;gBACH,qBACE,6LAAC;oBAAI,WAAU;8BAEb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAA8B;;;;;;kDAC5C,6LAAC,oIAAA,CAAA,QAAK;wCAAC,WAAU;kDAA4B;;;;;;;;;;;;0CAI/C,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;8DAAgB;;;;;;8DAChC,6LAAC;oDAAK,WAAU;8DAAwC;;;;;;;;;;;;;;;;;kDAK5D,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAuC;;;;;;0DACrD,6LAAC;gDAAE,WAAU;0DAA6B;;;;;;0DAG1C,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,WAAU;0EAAsB;;;;;;0EACtC,6LAAC;gEAAK,WAAU;0EAAwB;;;;;;;;;;;;kEAE1C,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;kFAAiC;;;;;;kFAChD,6LAAC;wEAAI,WAAU;kFAAwB;;;;;;;;;;;;0EAEzC,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;kFAAoC;;;;;;kFACnD,6LAAC;wEAAI,WAAU;kFAAwB;;;;;;;;;;;;0EAEzC,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;kFAAkC;;;;;;kFACjD,6LAAC;wEAAI,WAAU;kFAAwB;;;;;;;;;;;;;;;;;;;;;;;;0DAI7C,6LAAC,qIAAA,CAAA,SAAM;gDAAC,WAAU;0DAAiE;;;;;;;;;;;;;;;;;;0CAOvF,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DAAmC;;;;;;0DAClD,6LAAC;gDAAI,WAAU;0DAAwB;;;;;;;;;;;;kDAEzC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DAAkC;;;;;;0DACjD,6LAAC;gDAAI,WAAU;0DAAwB;;;;;;;;;;;;kDAEzC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DAAoC;;;;;;0DACnD,6LAAC;gDAAI,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAOnD,KAAK;gBACH,qBACE,6LAAC;oBAAI,WAAU;8BAEb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAA8B;;;;;;kDAC5C,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;gDAAK,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;0CAK5C,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,WAAU;wCACV,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,OAAO;wCAAI;;0DAEzB,6LAAC;gDAAI,WAAU;0DAAoC;;;;;;0DACnD,6LAAC;gDAAI,WAAU;0DAAwB;;;;;;0DACvC,6LAAC;gDAAI,WAAU;0DAA8B;;;;;;;;;;;;kDAG/C,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,WAAU;wCACV,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,OAAO;wCAAI;;0DAEzB,6LAAC;gDAAI,WAAU;0DAAmC;;;;;;0DAClD,6LAAC;gDAAI,WAAU;0DAAwB;;;;;;0DACvC,6LAAC;gDAAI,WAAU;0DAA6B;;;;;;;;;;;;kDAG9C,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,WAAU;wCACV,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,OAAO;wCAAI;;0DAEzB,6LAAC;gDAAI,WAAU;0DAAqC;;;;;;0DACpD,6LAAC;gDAAI,WAAU;0DAAwB;;;;;;0DACvC,6LAAC;gDAAI,WAAU;0DAA+B;;;;;;;;;;;;kDAGhD,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,WAAU;wCACV,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,OAAO;wCAAI;;0DAEzB,6LAAC;gDAAI,WAAU;0DAAqC;;;;;;0DACpD,6LAAC;gDAAI,WAAU;0DAAwB;;;;;;0DACvC,6LAAC;gDAAI,WAAU;0DAA+B;;;;;;;;;;;;;;;;;;0CAKlD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;0DAAoC;;;;;;0DACpD,6LAAC;gDAAK,WAAU;0DAAwB;;;;;;;;;;;;kDAE1C,6LAAC;wCAAI,WAAU;kDAEZ;4CAAC;4CAAI;4CAAI;4CAAI;4CAAI;4CAAI;4CAAI;4CAAI;yCAAG,CAAC,GAAG,CAAC,CAAC,QAAQ,sBAC7C,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gDAET,WAAU;gDACV,OAAO;oDAAE,QAAQ,AAAC,GAAS,OAAP,QAAO;oDAAI,OAAO;gDAAK;gDAC3C,SAAS;oDAAE,QAAQ;gDAAE;gDACrB,SAAS;oDAAE,QAAQ,AAAC,GAAS,OAAP,QAAO;gDAAG;gDAChC,YAAY;oDAAE,OAAO,QAAQ;oDAAK,UAAU;gDAAI;+CAL3C;;;;;;;;;;;;;;;;0CAYb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAK,WAAU;8DAAqB;;;;;;;;;;;0DAEvC,6LAAC;gDAAK,WAAU;0DAAoC;;;;;;;;;;;;kDAEtD,6LAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;;;;;;;;;;;;YAQ/C;gBACE,OAAO;QACX;IACF;IAEA,qBACE,6LAAC;QAAQ,WAAU;kBACjB,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAEV,6LAAC,oIAAA,CAAA,QAAK;4BAAC,WAAU;sCAA+D;;;;;;sCAGhF,6LAAC;4BAAG,WAAU;;gCAAiD;8CAE7D,6LAAC;oCAAK,WAAU;8CAA2E;;;;;;;;;;;;sCAE7F,6LAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;8BAMzD,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;;;;;sCAGf,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;8CACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;4CAEZ,SAAS,IAAM,iBAAiB;4CAChC,WAAW,AAAC,8EAIX,OAHC,kBAAkB,QACd,kCACA;4CAEN,YAAY;gDAAE,OAAO;4CAAK;4CAC1B,UAAU;gDAAE,OAAO;4CAAK;;8DAExB,6LAAC,QAAQ,IAAI;oDAAC,WAAU;;;;;;8DACxB,6LAAC;oDAAK,WAAU;8DAAe,QAAQ,IAAI;;;;;;;2CAXtC,QAAQ,EAAE;;;;;;;;;;8CAiBrB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,UAAU;oCAAI;oCAC5B,WAAU;;sDAGV,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;;sEACC,6LAAC;4DAAG,WAAU;sEACX,QAAQ,CAAC,cAAc,CAAC,KAAK;;;;;;sEAEhC,6LAAC;4DAAE,WAAU;sEACV,QAAQ,CAAC,cAAc,CAAC,WAAW;;;;;;;;;;;;8DAIxC,6LAAC,qIAAA,CAAA,SAAM;oDAAC,WAAU;8DAA6D;;;;;;;;;;;;sDAMjF,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,SAAS;oDAAE,SAAS;oDAAG,OAAO;gDAAI;gDAClC,SAAS;oDAAE,SAAS;oDAAG,OAAO;gDAAE;gDAChC,YAAY;oDAAE,UAAU;oDAAK,OAAO;gDAAI;gDACxC,WAAU;0DAGV,cAAA,6LAAC;oDAAI,WAAU;;sEAEb,6LAAC;4DAAI,WAAU;;;;;;sEAGf,6LAAC;4DAAI,WAAU;sEACZ,aAAa,QAAQ,CAAC,cAAc;;;;;;sEAIvC,6LAAC;4DAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;mCAzChB;;;;;;;;;;;sCAiDT,6LAAC;4BAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;AAKzB;GA3eM;KAAA;uCA6eS", "debugId": null}}, {"offset": {"line": 5565, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Landing%20page/admybrand-ai-suite/src/components/sections/features.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { Badge } from '@/components/ui/badge'\r\nimport { motion } from 'framer-motion'\r\nimport {\r\n    BarChart3,\r\n    Mail,\r\n    MessageSquare,\r\n    Monitor,\r\n    PenTool,\r\n    Zap\r\n} from 'lucide-react'\r\n\r\nconst features = [\r\n  {\r\n    icon: PenTool,\r\n    title: \"Blog writer\",\r\n    description: \"A Blog Writer AI tool helps generate high-quality and SEO-optimized blog content quickly and efficiently.\",\r\n    badge: \"Most Popular\",\r\n    color: \"from-blue-500 to-cyan-500\",\r\n    bgGradient: \"from-blue-900/20 via-blue-800/10 to-cyan-900/20\"\r\n  },\r\n  {\r\n    icon: BarChart3,\r\n    title: \"Real-time analytics\",\r\n    description: \"Get real-time marketing analytics to make smarter, faster decisions on the fly.\",\r\n    badge: \"Analytics\",\r\n    color: \"from-purple-500 to-pink-500\",\r\n    bgGradient: \"from-purple-900/20 via-purple-800/10 to-pink-900/20\"\r\n  },\r\n  {\r\n    icon: Zap,\r\n    title: \"AI-driven automation\",\r\n    description: \"Streamline your marketing processes with AI that cuts down on manual work.\",\r\n    badge: \"Automation\",\r\n    color: \"from-indigo-500 to-purple-500\",\r\n    bgGradient: \"from-indigo-900/20 via-indigo-800/10 to-purple-900/20\"\r\n  },\r\n  {\r\n    icon: MessageSquare,\r\n    title: \"Brand Voice Analyzer\",\r\n    description: \"Maintain consistent messaging across all platforms. Our AI learns your brand's unique voice and ensures every piece of content matches your style.\",\r\n    badge: \"New\",\r\n    color: \"from-teal-500 to-blue-500\",\r\n    bgGradient: \"from-teal-900/20 via-teal-800/10 to-blue-900/20\"\r\n  },\r\n  {\r\n    icon: Mail,\r\n    title: \"Email Marketing AI\",\r\n    description: \"Personalize email campaigns at scale. Generate subject lines, content, and send times that increase open rates and conversions.\",\r\n    badge: \"Essential\",\r\n    color: \"from-green-500 to-teal-500\",\r\n    bgGradient: \"from-green-900/20 via-green-800/10 to-teal-900/20\"\r\n  },\r\n  {\r\n    icon: Monitor,\r\n    title: \"AI Ad Creation\",\r\n    description: \"Generate high-converting ads for all platforms instantly. Create compelling visuals, copy, and targeting strategies that drive results and maximize ROI.\",\r\n    badge: \"Creative\",\r\n    color: \"from-pink-500 to-rose-500\",\r\n    bgGradient: \"from-pink-900/20 via-pink-800/10 to-rose-900/20\"\r\n  }\r\n]\r\n\r\nconst Features = () => {\r\n  return (\r\n    <section className=\"pt-8 pb-24 bg-black\">\r\n      <div className=\"max-w-7xl mx-auto px-2 sm:px-4 lg:px-6\">\r\n        {/* Section Header */}\r\n        <motion.div\r\n          initial={{ opacity: 0, y: 30 }}\r\n          whileInView={{ opacity: 1, y: 0 }}\r\n          transition={{ duration: 0.8 }}\r\n          viewport={{ once: true }}\r\n          className=\"text-center mb-16\"\r\n        >\r\n          <Badge className=\"mb-4 bg-white/10 text-white border-white/20 backdrop-blur-sm\">\r\n            Powerful Features\r\n          </Badge>\r\n          <h2 className=\"text-4xl md:text-5xl font-bold text-white mb-6\">\r\n            Everything you need to\r\n            <span className=\"bg-gradient-to-r from-blue-400 to-cyan-400 bg-clip-text text-transparent\"> dominate marketing</span>\r\n          </h2>\r\n          <p className=\"text-xl text-gray-300 max-w-3xl mx-auto\">\r\n            Our comprehensive AI suite provides all the tools you need to create, optimize, and scale your marketing efforts with unprecedented efficiency.\r\n          </p>\r\n        </motion.div>\r\n\r\n        {/* Features Grid */}\r\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\r\n          {features.map((feature, index) => (\r\n            <motion.div\r\n              key={feature.title}\r\n              initial={{ opacity: 0, y: 30 }}\r\n              whileInView={{ opacity: 1, y: 0 }}\r\n              transition={{ duration: 0.6, delay: index * 0.1 }}\r\n              viewport={{ once: true }}\r\n              whileHover={{ y: -8, scale: 1.02 }}\r\n              className=\"group\"\r\n            >\r\n              <div className={`relative h-full rounded-2xl border border-white/10 shadow-2xl hover:shadow-3xl transition-all duration-500 overflow-hidden bg-gradient-to-br ${feature.bgGradient} backdrop-blur-xl hover:border-white/20`}>\r\n                {/* Animated background gradient */}\r\n                <div className=\"absolute inset-0 bg-gradient-to-br from-white/5 via-transparent to-white/10 pointer-events-none\" />\r\n                <div className={`absolute inset-0 bg-gradient-to-br ${feature.color} opacity-0 group-hover:opacity-10 transition-opacity duration-500`} />\r\n\r\n                {/* Content */}\r\n                <div className=\"relative z-10 p-6 h-full flex flex-col\">\r\n                  {/* Header with Icon and Badge */}\r\n                  <div className=\"flex items-start justify-between mb-4\">\r\n                    <motion.div\r\n                      className={`p-3 rounded-xl bg-gradient-to-br ${feature.color} shadow-lg`}\r\n                      whileHover={{ scale: 1.1, rotate: 5 }}\r\n                      transition={{ duration: 0.2 }}\r\n                    >\r\n                      <feature.icon className=\"w-6 h-6 text-white\" />\r\n                    </motion.div>\r\n                    <Badge className=\"text-xs bg-white/20 text-white border-white/30 backdrop-blur-sm px-2 py-1\">\r\n                      {feature.badge}\r\n                    </Badge>\r\n                  </div>\r\n\r\n                  {/* Title */}\r\n                  <h3 className=\"text-lg font-bold text-white mb-3 group-hover:text-transparent group-hover:bg-gradient-to-r group-hover:from-blue-400 group-hover:to-cyan-400 group-hover:bg-clip-text transition-all duration-300\">\r\n                    {feature.title}\r\n                  </h3>\r\n\r\n                  {/* Description */}\r\n                  <p className=\"text-gray-300 text-sm leading-relaxed flex-grow\">\r\n                    {feature.description}\r\n                  </p>\r\n\r\n                  {/* Hover Effect */}\r\n                  <div className=\"mt-6 opacity-0 group-hover:opacity-100 transition-all duration-300 transform translate-y-2 group-hover:translate-y-0\">\r\n                    <div className=\"text-blue-400 text-sm font-medium flex items-center\">\r\n                      Learn more\r\n                      <motion.div\r\n                        className=\"ml-2\"\r\n                        animate={{ x: [0, 4, 0] }}\r\n                        transition={{ duration: 1.5, repeat: Infinity }}\r\n                      >\r\n                        →\r\n                      </motion.div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                {/* Animated border effect */}\r\n                <div className=\"absolute inset-0 rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-500\">\r\n                  <div className={`absolute inset-0 rounded-3xl bg-gradient-to-r ${feature.color} opacity-20 blur-xl`} />\r\n                </div>\r\n              </div>\r\n            </motion.div>\r\n          ))}\r\n        </div>\r\n\r\n        {/* Bottom CTA */}\r\n        <motion.div\r\n          initial={{ opacity: 0, y: 30 }}\r\n          whileInView={{ opacity: 1, y: 0 }}\r\n          transition={{ duration: 0.8, delay: 0.4 }}\r\n          viewport={{ once: true }}\r\n          className=\"text-center mt-16\"\r\n        >\r\n          <p className=\"text-gray-600 mb-6\">\r\n            Ready to transform your marketing with AI?\r\n          </p>\r\n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\r\n            <motion.button\r\n              whileHover={{ scale: 1.05 }}\r\n              whileTap={{ scale: 0.95 }}\r\n              className=\"px-8 py-3 bg-gradient-to-r from-purple-600 to-blue-600 text-white rounded-lg font-medium shadow-lg hover:shadow-xl transition-all duration-300\"\r\n            >\r\n              Start Free Trial\r\n            </motion.button>\r\n            <motion.button\r\n              whileHover={{ scale: 1.05 }}\r\n              whileTap={{ scale: 0.95 }}\r\n              className=\"px-8 py-3 border border-gray-300 text-gray-700 rounded-lg font-medium hover:bg-gray-50 transition-all duration-300\"\r\n            >\r\n              View All Features\r\n            </motion.button>\r\n          </div>\r\n        </motion.div>\r\n      </div>\r\n    </section>\r\n  )\r\n}\r\n\r\nexport default Features\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAJA;;;;;AAaA,MAAM,WAAW;IACf;QACE,MAAM,+MAAA,CAAA,UAAO;QACb,OAAO;QACP,aAAa;QACb,OAAO;QACP,OAAO;QACP,YAAY;IACd;IACA;QACE,MAAM,qNAAA,CAAA,YAAS;QACf,OAAO;QACP,aAAa;QACb,OAAO;QACP,OAAO;QACP,YAAY;IACd;IACA;QACE,MAAM,mMAAA,CAAA,MAAG;QACT,OAAO;QACP,aAAa;QACb,OAAO;QACP,OAAO;QACP,YAAY;IACd;IACA;QACE,MAAM,2NAAA,CAAA,gBAAa;QACnB,OAAO;QACP,aAAa;QACb,OAAO;QACP,OAAO;QACP,YAAY;IACd;IACA;QACE,MAAM,qMAAA,CAAA,OAAI;QACV,OAAO;QACP,aAAa;QACb,OAAO;QACP,OAAO;QACP,YAAY;IACd;IACA;QACE,MAAM,2MAAA,CAAA,UAAO;QACb,OAAO;QACP,aAAa;QACb,OAAO;QACP,OAAO;QACP,YAAY;IACd;CACD;AAED,MAAM,WAAW;IACf,qBACE,6LAAC;QAAQ,WAAU;kBACjB,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAEV,6LAAC,oIAAA,CAAA,QAAK;4BAAC,WAAU;sCAA+D;;;;;;sCAGhF,6LAAC;4BAAG,WAAU;;gCAAiD;8CAE7D,6LAAC;oCAAK,WAAU;8CAA2E;;;;;;;;;;;;sCAE7F,6LAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;8BAMzD,6LAAC;oBAAI,WAAU;8BACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO,QAAQ;4BAAI;4BAChD,UAAU;gCAAE,MAAM;4BAAK;4BACvB,YAAY;gCAAE,GAAG,CAAC;gCAAG,OAAO;4BAAK;4BACjC,WAAU;sCAEV,cAAA,6LAAC;gCAAI,WAAW,AAAC,gJAAkK,OAAnB,QAAQ,UAAU,EAAC;;kDAEjL,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAI,WAAW,AAAC,sCAAmD,OAAd,QAAQ,KAAK,EAAC;;;;;;kDAGpE,6LAAC;wCAAI,WAAU;;0DAEb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wDACT,WAAW,AAAC,oCAAiD,OAAd,QAAQ,KAAK,EAAC;wDAC7D,YAAY;4DAAE,OAAO;4DAAK,QAAQ;wDAAE;wDACpC,YAAY;4DAAE,UAAU;wDAAI;kEAE5B,cAAA,6LAAC,QAAQ,IAAI;4DAAC,WAAU;;;;;;;;;;;kEAE1B,6LAAC,oIAAA,CAAA,QAAK;wDAAC,WAAU;kEACd,QAAQ,KAAK;;;;;;;;;;;;0DAKlB,6LAAC;gDAAG,WAAU;0DACX,QAAQ,KAAK;;;;;;0DAIhB,6LAAC;gDAAE,WAAU;0DACV,QAAQ,WAAW;;;;;;0DAItB,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;;wDAAsD;sEAEnE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4DACT,WAAU;4DACV,SAAS;gEAAE,GAAG;oEAAC;oEAAG;oEAAG;iEAAE;4DAAC;4DACxB,YAAY;gEAAE,UAAU;gEAAK,QAAQ;4DAAS;sEAC/C;;;;;;;;;;;;;;;;;;;;;;;kDAQP,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAW,AAAC,iDAA8D,OAAd,QAAQ,KAAK,EAAC;;;;;;;;;;;;;;;;;2BAxD9E,QAAQ,KAAK;;;;;;;;;;8BAgExB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAEV,6LAAC;4BAAE,WAAU;sCAAqB;;;;;;sCAGlC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;oCACZ,YAAY;wCAAE,OAAO;oCAAK;oCAC1B,UAAU;wCAAE,OAAO;oCAAK;oCACxB,WAAU;8CACX;;;;;;8CAGD,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;oCACZ,YAAY;wCAAE,OAAO;oCAAK;oCAC1B,UAAU;wCAAE,OAAO;oCAAK;oCACxB,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb;KA1HM;uCA4HS", "debugId": null}}, {"offset": {"line": 5958, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Landing%20page/admybrand-ai-suite/src/components/ui/advanced-animations.tsx"], "sourcesContent": ["'use client'\n\nimport { motion, useScroll, useTransform, useSpring, useInView } from 'framer-motion'\nimport { useRef, useEffect, useState } from 'react'\n\n// Parallax Text Component\nexport const ParallaxText = ({ children, baseVelocity = 100 }: { children: string; baseVelocity?: number }) => {\n  const baseX = useTransform(useScroll().scrollY, [0, 1000], [0, baseVelocity])\n  const x = useSpring(baseX, { damping: 50, stiffness: 400 })\n\n  return (\n    <div className=\"overflow-hidden whitespace-nowrap flex\">\n      <motion.div\n        className=\"flex whitespace-nowrap\"\n        style={{ x }}\n      >\n        <span className=\"block mr-8 text-6xl font-bold text-white/10\">\n          {children} {children} {children} {children}\n        </span>\n      </motion.div>\n    </div>\n  )\n}\n\n// Magnetic Button Component\nexport const MagneticButton = ({ children, className = \"\" }: { children: React.ReactNode; className?: string }) => {\n  const ref = useRef<HTMLButtonElement>(null)\n  const [position, setPosition] = useState({ x: 0, y: 0 })\n\n  const handleMouseMove = (e: React.MouseEvent) => {\n    if (!ref.current) return\n    \n    const rect = ref.current.getBoundingClientRect()\n    const centerX = rect.left + rect.width / 2\n    const centerY = rect.top + rect.height / 2\n    \n    const deltaX = (e.clientX - centerX) * 0.15\n    const deltaY = (e.clientY - centerY) * 0.15\n    \n    setPosition({ x: deltaX, y: deltaY })\n  }\n\n  const handleMouseLeave = () => {\n    setPosition({ x: 0, y: 0 })\n  }\n\n  return (\n    <motion.button\n      ref={ref}\n      className={className}\n      onMouseMove={handleMouseMove}\n      onMouseLeave={handleMouseLeave}\n      animate={{ x: position.x, y: position.y }}\n      transition={{ type: \"spring\", stiffness: 300, damping: 30 }}\n    >\n      {children}\n    </motion.button>\n  )\n}\n\n// Scroll Progress Indicator\nexport const ScrollProgress = () => {\n  const { scrollYProgress } = useScroll()\n  const scaleX = useSpring(scrollYProgress, { stiffness: 100, damping: 30, restDelta: 0.001 })\n\n  return (\n    <motion.div\n      className=\"fixed top-0 left-0 right-0 h-1 bg-gradient-to-r from-blue-500 to-purple-500 z-50 origin-left\"\n      style={{ scaleX }}\n    />\n  )\n}\n\n// Morphing Shape Component\nexport const MorphingShape = ({ className = \"\" }: { className?: string }) => {\n  return (\n    <motion.div\n      className={`absolute ${className}`}\n      animate={{\n        borderRadius: [\"20%\", \"50%\", \"30%\", \"40%\", \"20%\"],\n        rotate: [0, 90, 180, 270, 360],\n      }}\n      transition={{\n        duration: 20,\n        repeat: Infinity,\n        ease: \"linear\"\n      }}\n    />\n  )\n}\n\n// Text Reveal Animation\nexport const TextReveal = ({ children, className = \"\" }: { children: string; className?: string }) => {\n  const ref = useRef(null)\n  const isInView = useInView(ref, { once: true })\n\n  const words = children.split(\" \")\n\n  return (\n    <div ref={ref} className={className}>\n      {words.map((word, index) => (\n        <motion.span\n          key={index}\n          className=\"inline-block mr-2\"\n          initial={{ opacity: 0, y: 50 }}\n          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 50 }}\n          transition={{\n            duration: 0.6,\n            delay: index * 0.1,\n            ease: [0.25, 0.46, 0.45, 0.94]\n          }}\n        >\n          {word}\n        </motion.span>\n      ))}\n    </div>\n  )\n}\n\n// Floating Elements\nexport const FloatingElements = () => {\n  const elements = Array.from({ length: 6 }, (_, i) => i)\n\n  return (\n    <div className=\"absolute inset-0 overflow-hidden pointer-events-none\">\n      {elements.map((i) => (\n        <motion.div\n          key={i}\n          className=\"absolute w-4 h-4 bg-blue-500/20 rounded-full\"\n          style={{\n            left: `${Math.random() * 100}%`,\n            top: `${Math.random() * 100}%`,\n          }}\n          animate={{\n            y: [0, -30, 0],\n            x: [0, Math.random() * 20 - 10, 0],\n            opacity: [0.3, 0.8, 0.3],\n          }}\n          transition={{\n            duration: 3 + Math.random() * 2,\n            repeat: Infinity,\n            delay: Math.random() * 2,\n            ease: \"easeInOut\"\n          }}\n        />\n      ))}\n    </div>\n  )\n}\n\n// Stagger Container\nexport const StaggerContainer = ({ children, className = \"\" }: { children: React.ReactNode; className?: string }) => {\n  return (\n    <motion.div\n      className={className}\n      initial=\"hidden\"\n      whileInView=\"visible\"\n      viewport={{ once: true }}\n      variants={{\n        hidden: {},\n        visible: {\n          transition: {\n            staggerChildren: 0.1\n          }\n        }\n      }}\n    >\n      {children}\n    </motion.div>\n  )\n}\n\n// Stagger Item\nexport const StaggerItem = ({ children, className = \"\" }: { children: React.ReactNode; className?: string }) => {\n  return (\n    <motion.div\n      className={className}\n      variants={{\n        hidden: { opacity: 0, y: 30 },\n        visible: { opacity: 1, y: 0 }\n      }}\n      transition={{ duration: 0.6, ease: [0.25, 0.46, 0.45, 0.94] }}\n    >\n      {children}\n    </motion.div>\n  )\n}\n\n// 3D Card Effect\nexport const Card3D = ({ children, className = \"\" }: { children: React.ReactNode; className?: string }) => {\n  const ref = useRef<HTMLDivElement>(null)\n  const [rotateX, setRotateX] = useState(0)\n  const [rotateY, setRotateY] = useState(0)\n\n  const handleMouseMove = (e: React.MouseEvent) => {\n    if (!ref.current) return\n\n    const rect = ref.current.getBoundingClientRect()\n    const centerX = rect.left + rect.width / 2\n    const centerY = rect.top + rect.height / 2\n\n    const rotateXValue = (e.clientY - centerY) / 10\n    const rotateYValue = (centerX - e.clientX) / 10\n\n    setRotateX(rotateXValue)\n    setRotateY(rotateYValue)\n  }\n\n  const handleMouseLeave = () => {\n    setRotateX(0)\n    setRotateY(0)\n  }\n\n  return (\n    <motion.div\n      ref={ref}\n      className={`perspective-1000 ${className}`}\n      onMouseMove={handleMouseMove}\n      onMouseLeave={handleMouseLeave}\n      animate={{\n        rotateX,\n        rotateY,\n      }}\n      transition={{ type: \"spring\", stiffness: 300, damping: 30 }}\n      style={{\n        transformStyle: \"preserve-3d\",\n      }}\n    >\n      {children}\n    </motion.div>\n  )\n}\n\n// Liquid Button\nexport const LiquidButton = ({ children, className = \"\" }: { children: React.ReactNode; className?: string }) => {\n  return (\n    <motion.button\n      className={`relative overflow-hidden ${className}`}\n      whileHover=\"hover\"\n      whileTap=\"tap\"\n      variants={{\n        hover: { scale: 1.05 },\n        tap: { scale: 0.95 }\n      }}\n    >\n      <motion.div\n        className=\"absolute inset-0 bg-gradient-to-r from-blue-500 to-purple-500\"\n        variants={{\n          hover: {\n            borderRadius: [\"20%\", \"50%\", \"30%\", \"40%\", \"20%\"],\n          }\n        }}\n        transition={{\n          duration: 2,\n          repeat: Infinity,\n          ease: \"linear\"\n        }}\n      />\n      <span className=\"relative z-10\">{children}</span>\n    </motion.button>\n  )\n}\n\n// Typewriter Effect\nexport const TypewriterText = ({ text, className = \"\" }: { text: string; className?: string }) => {\n  const [displayText, setDisplayText] = useState(\"\")\n  const [currentIndex, setCurrentIndex] = useState(0)\n\n  useEffect(() => {\n    if (currentIndex < text.length) {\n      const timeout = setTimeout(() => {\n        setDisplayText(prev => prev + text[currentIndex])\n        setCurrentIndex(prev => prev + 1)\n      }, 100)\n      return () => clearTimeout(timeout)\n    }\n  }, [currentIndex, text])\n\n  return (\n    <span className={className}>\n      {displayText}\n      <motion.span\n        animate={{ opacity: [1, 0] }}\n        transition={{ duration: 0.8, repeat: Infinity }}\n        className=\"inline-block w-0.5 h-6 bg-blue-500 ml-1\"\n      />\n    </span>\n  )\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAEA;AAAA;AAAA;AAAA;AAAA;AACA;;;AAHA;;;AAMO,MAAM,eAAe;QAAC,EAAE,QAAQ,EAAE,eAAe,GAAG,EAA+C;;IACxG,MAAM,QAAQ,CAAA,GAAA,+KAAA,CAAA,eAAY,AAAD,EAAE,CAAA,GAAA,4KAAA,CAAA,YAAS,AAAD,IAAI,OAAO,EAAE;QAAC;QAAG;KAAK,EAAE;QAAC;QAAG;KAAa;IAC5E,MAAM,IAAI,CAAA,GAAA,4KAAA,CAAA,YAAS,AAAD,EAAE,OAAO;QAAE,SAAS;QAAI,WAAW;IAAI;IAEzD,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;YACT,WAAU;YACV,OAAO;gBAAE;YAAE;sBAEX,cAAA,6LAAC;gBAAK,WAAU;;oBACb;oBAAS;oBAAE;oBAAS;oBAAE;oBAAS;oBAAE;;;;;;;;;;;;;;;;;AAK5C;GAhBa;;QACG,+KAAA,CAAA,eAAY;QAChB,4KAAA,CAAA,YAAS;;;KAFR;AAmBN,MAAM,iBAAiB;QAAC,EAAE,QAAQ,EAAE,YAAY,EAAE,EAAqD;;IAC5G,MAAM,MAAM,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAqB;IACtC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QAAE,GAAG;QAAG,GAAG;IAAE;IAEtD,MAAM,kBAAkB,CAAC;QACvB,IAAI,CAAC,IAAI,OAAO,EAAE;QAElB,MAAM,OAAO,IAAI,OAAO,CAAC,qBAAqB;QAC9C,MAAM,UAAU,KAAK,IAAI,GAAG,KAAK,KAAK,GAAG;QACzC,MAAM,UAAU,KAAK,GAAG,GAAG,KAAK,MAAM,GAAG;QAEzC,MAAM,SAAS,CAAC,EAAE,OAAO,GAAG,OAAO,IAAI;QACvC,MAAM,SAAS,CAAC,EAAE,OAAO,GAAG,OAAO,IAAI;QAEvC,YAAY;YAAE,GAAG;YAAQ,GAAG;QAAO;IACrC;IAEA,MAAM,mBAAmB;QACvB,YAAY;YAAE,GAAG;YAAG,GAAG;QAAE;IAC3B;IAEA,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;QACZ,KAAK;QACL,WAAW;QACX,aAAa;QACb,cAAc;QACd,SAAS;YAAE,GAAG,SAAS,CAAC;YAAE,GAAG,SAAS,CAAC;QAAC;QACxC,YAAY;YAAE,MAAM;YAAU,WAAW;YAAK,SAAS;QAAG;kBAEzD;;;;;;AAGP;IAjCa;MAAA;AAoCN,MAAM,iBAAiB;;IAC5B,MAAM,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,4KAAA,CAAA,YAAS,AAAD;IACpC,MAAM,SAAS,CAAA,GAAA,4KAAA,CAAA,YAAS,AAAD,EAAE,iBAAiB;QAAE,WAAW;QAAK,SAAS;QAAI,WAAW;IAAM;IAE1F,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAU;QACV,OAAO;YAAE;QAAO;;;;;;AAGtB;IAVa;;QACiB,4KAAA,CAAA,YAAS;QACtB,4KAAA,CAAA,YAAS;;;MAFb;AAaN,MAAM,gBAAgB;QAAC,EAAE,YAAY,EAAE,EAA0B;IACtE,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAW,AAAC,YAAqB,OAAV;QACvB,SAAS;YACP,cAAc;gBAAC;gBAAO;gBAAO;gBAAO;gBAAO;aAAM;YACjD,QAAQ;gBAAC;gBAAG;gBAAI;gBAAK;gBAAK;aAAI;QAChC;QACA,YAAY;YACV,UAAU;YACV,QAAQ;YACR,MAAM;QACR;;;;;;AAGN;MAfa;AAkBN,MAAM,aAAa;QAAC,EAAE,QAAQ,EAAE,YAAY,EAAE,EAA4C;;IAC/F,MAAM,MAAM,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IACnB,MAAM,WAAW,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE,KAAK;QAAE,MAAM;IAAK;IAE7C,MAAM,QAAQ,SAAS,KAAK,CAAC;IAE7B,qBACE,6LAAC;QAAI,KAAK;QAAK,WAAW;kBACvB,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,6LAAC,6LAAA,CAAA,SAAM,CAAC,IAAI;gBAEV,WAAU;gBACV,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS,WAAW;oBAAE,SAAS;oBAAG,GAAG;gBAAE,IAAI;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC/D,YAAY;oBACV,UAAU;oBACV,OAAO,QAAQ;oBACf,MAAM;wBAAC;wBAAM;wBAAM;wBAAM;qBAAK;gBAChC;0BAEC;eAVI;;;;;;;;;;AAef;IAzBa;;QAEM,gLAAA,CAAA,YAAS;;;MAFf;AA4BN,MAAM,mBAAmB;IAC9B,MAAM,WAAW,MAAM,IAAI,CAAC;QAAE,QAAQ;IAAE,GAAG,CAAC,GAAG,IAAM;IAErD,qBACE,6LAAC;QAAI,WAAU;kBACZ,SAAS,GAAG,CAAC,CAAC,kBACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBAET,WAAU;gBACV,OAAO;oBACL,MAAM,AAAC,GAAsB,OAApB,KAAK,MAAM,KAAK,KAAI;oBAC7B,KAAK,AAAC,GAAsB,OAApB,KAAK,MAAM,KAAK,KAAI;gBAC9B;gBACA,SAAS;oBACP,GAAG;wBAAC;wBAAG,CAAC;wBAAI;qBAAE;oBACd,GAAG;wBAAC;wBAAG,KAAK,MAAM,KAAK,KAAK;wBAAI;qBAAE;oBAClC,SAAS;wBAAC;wBAAK;wBAAK;qBAAI;gBAC1B;gBACA,YAAY;oBACV,UAAU,IAAI,KAAK,MAAM,KAAK;oBAC9B,QAAQ;oBACR,OAAO,KAAK,MAAM,KAAK;oBACvB,MAAM;gBACR;eAhBK;;;;;;;;;;AAqBf;MA5Ba;AA+BN,MAAM,mBAAmB;QAAC,EAAE,QAAQ,EAAE,YAAY,EAAE,EAAqD;IAC9G,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAW;QACX,SAAQ;QACR,aAAY;QACZ,UAAU;YAAE,MAAM;QAAK;QACvB,UAAU;YACR,QAAQ,CAAC;YACT,SAAS;gBACP,YAAY;oBACV,iBAAiB;gBACnB;YACF;QACF;kBAEC;;;;;;AAGP;MAnBa;AAsBN,MAAM,cAAc;QAAC,EAAE,QAAQ,EAAE,YAAY,EAAE,EAAqD;IACzG,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAW;QACX,UAAU;YACR,QAAQ;gBAAE,SAAS;gBAAG,GAAG;YAAG;YAC5B,SAAS;gBAAE,SAAS;gBAAG,GAAG;YAAE;QAC9B;QACA,YAAY;YAAE,UAAU;YAAK,MAAM;gBAAC;gBAAM;gBAAM;gBAAM;aAAK;QAAC;kBAE3D;;;;;;AAGP;MAba;AAgBN,MAAM,SAAS;QAAC,EAAE,QAAQ,EAAE,YAAY,EAAE,EAAqD;;IACpG,MAAM,MAAM,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,MAAM,kBAAkB,CAAC;QACvB,IAAI,CAAC,IAAI,OAAO,EAAE;QAElB,MAAM,OAAO,IAAI,OAAO,CAAC,qBAAqB;QAC9C,MAAM,UAAU,KAAK,IAAI,GAAG,KAAK,KAAK,GAAG;QACzC,MAAM,UAAU,KAAK,GAAG,GAAG,KAAK,MAAM,GAAG;QAEzC,MAAM,eAAe,CAAC,EAAE,OAAO,GAAG,OAAO,IAAI;QAC7C,MAAM,eAAe,CAAC,UAAU,EAAE,OAAO,IAAI;QAE7C,WAAW;QACX,WAAW;IACb;IAEA,MAAM,mBAAmB;QACvB,WAAW;QACX,WAAW;IACb;IAEA,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,KAAK;QACL,WAAW,AAAC,oBAA6B,OAAV;QAC/B,aAAa;QACb,cAAc;QACd,SAAS;YACP;YACA;QACF;QACA,YAAY;YAAE,MAAM;YAAU,WAAW;YAAK,SAAS;QAAG;QAC1D,OAAO;YACL,gBAAgB;QAClB;kBAEC;;;;;;AAGP;IA1Ca;MAAA;AA6CN,MAAM,eAAe;QAAC,EAAE,QAAQ,EAAE,YAAY,EAAE,EAAqD;IAC1G,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;QACZ,WAAW,AAAC,4BAAqC,OAAV;QACvC,YAAW;QACX,UAAS;QACT,UAAU;YACR,OAAO;gBAAE,OAAO;YAAK;YACrB,KAAK;gBAAE,OAAO;YAAK;QACrB;;0BAEA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,UAAU;oBACR,OAAO;wBACL,cAAc;4BAAC;4BAAO;4BAAO;4BAAO;4BAAO;yBAAM;oBACnD;gBACF;gBACA,YAAY;oBACV,UAAU;oBACV,QAAQ;oBACR,MAAM;gBACR;;;;;;0BAEF,6LAAC;gBAAK,WAAU;0BAAiB;;;;;;;;;;;;AAGvC;MA3Ba;AA8BN,MAAM,iBAAiB;QAAC,EAAE,IAAI,EAAE,YAAY,EAAE,EAAwC;;IAC3F,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,IAAI,eAAe,KAAK,MAAM,EAAE;gBAC9B,MAAM,UAAU;wDAAW;wBACzB;gEAAe,CAAA,OAAQ,OAAO,IAAI,CAAC,aAAa;;wBAChD;gEAAgB,CAAA,OAAQ,OAAO;;oBACjC;uDAAG;gBACH;gDAAO,IAAM,aAAa;;YAC5B;QACF;mCAAG;QAAC;QAAc;KAAK;IAEvB,qBACE,6LAAC;QAAK,WAAW;;YACd;0BACD,6LAAC,6LAAA,CAAA,SAAM,CAAC,IAAI;gBACV,SAAS;oBAAE,SAAS;wBAAC;wBAAG;qBAAE;gBAAC;gBAC3B,YAAY;oBAAE,UAAU;oBAAK,QAAQ;gBAAS;gBAC9C,WAAU;;;;;;;;;;;;AAIlB;IAxBa;OAAA", "debugId": null}}, {"offset": {"line": 6478, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Landing%20page/admybrand-ai-suite/src/components/sections/hero.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { FloatingElements } from '@/components/ui/advanced-animations'\r\nimport { Button } from '@/components/ui/button'\r\nimport { motion } from 'framer-motion'\r\nimport { Spark<PERSON> } from 'lucide-react'\r\n\r\nconst Hero = () => {\r\n  return (\r\n    <section className=\"relative min-h-screen bg-gradient-to-br from-black via-gray-900 to-black flex flex-col overflow-hidden\">\r\n      {/* Animated background elements */}\r\n      <div className=\"absolute inset-0 overflow-hidden\">\r\n        <div className=\"absolute top-1/4 left-1/4 w-96 h-96 bg-blue-500/10 rounded-full blur-3xl animate-pulse\"></div>\r\n        <div className=\"absolute bottom-1/4 right-1/4 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl animate-pulse delay-1000\"></div>\r\n        <div className=\"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[800px] h-[800px] bg-gradient-conic from-blue-500/5 via-purple-500/5 to-blue-500/5 rounded-full blur-3xl animate-spin-slow\"></div>\r\n        <FloatingElements />\r\n      </div>\r\n\r\n      {/* Image Section - Modern Glass Card */}\r\n      <div className=\"flex items-center justify-center px-2 sm:px-4 lg:px-6 pt-28 pb-4 relative z-10\">\r\n        <motion.div\r\n          initial={{ opacity: 0, scale: 0.9, y: 20 }}\r\n          animate={{ opacity: 1, scale: 1, y: 0 }}\r\n          transition={{ duration: 1, ease: \"easeOut\" }}\r\n          className=\"relative w-full max-w-7xl h-[45vh] rounded-2xl overflow-hidden shadow-2xl border border-white/10 bg-white/5 backdrop-blur-xl\"\r\n        >\r\n          <div\r\n            className=\"absolute inset-0 bg-cover bg-center bg-no-repeat\"\r\n            style={{\r\n              backgroundImage: \"url('/office-background.jpg')\",\r\n            }}\r\n          />\r\n          {/* Modern gradient overlay */}\r\n          <div className=\"absolute inset-0 bg-gradient-to-br from-black/30 via-transparent to-purple-900/20\" />\r\n\r\n          {/* Floating badge */}\r\n          <motion.div\r\n            initial={{ opacity: 0, y: -20 }}\r\n            animate={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 0.8, delay: 0.5 }}\r\n            className=\"absolute top-4 left-4 bg-white/10 backdrop-blur-md border border-white/20 rounded-full px-4 py-2 flex items-center gap-2\"\r\n          >\r\n            <Sparkles className=\"w-4 h-4 text-yellow-400\" />\r\n            <span className=\"text-white text-sm font-medium\">AI-Powered</span>\r\n          </motion.div>\r\n        </motion.div>\r\n      </div>\r\n\r\n      {/* Modern floating notification cards */}\r\n      <motion.div\r\n        className=\"absolute top-32 left-[15%] bg-gradient-to-r from-blue-500/20 to-purple-500/20 backdrop-blur-md rounded-xl px-4 py-3 text-sm text-white border border-white/20 z-20 shadow-lg\"\r\n        animate={{\r\n          y: [0, -8, 0],\r\n          opacity: [0.8, 1, 0.8],\r\n          scale: [1, 1.02, 1],\r\n        }}\r\n        transition={{\r\n          duration: 3,\r\n          repeat: Infinity,\r\n          repeatType: \"reverse\",\r\n        }}\r\n      >\r\n        <div className=\"flex items-center gap-2\">\r\n          <div className=\"w-2 h-2 bg-green-400 rounded-full animate-pulse\"></div>\r\n          <span className=\"font-medium\">27 menus analyzed</span>\r\n        </div>\r\n      </motion.div>\r\n\r\n      <motion.div\r\n        className=\"absolute top-40 right-[15%] bg-gradient-to-r from-purple-500/20 to-pink-500/20 backdrop-blur-md rounded-xl px-4 py-3 text-sm text-white border border-white/20 z-20 shadow-lg\"\r\n        animate={{\r\n          y: [0, 8, 0],\r\n          opacity: [0.8, 1, 0.8],\r\n          scale: [1, 1.02, 1],\r\n        }}\r\n        transition={{\r\n          duration: 4,\r\n          repeat: Infinity,\r\n          repeatType: \"reverse\",\r\n          delay: 1,\r\n        }}\r\n      >\r\n        <div className=\"flex items-center gap-2\">\r\n          <div className=\"w-2 h-2 bg-orange-400 rounded-full animate-pulse\"></div>\r\n          <span className=\"font-medium\">4 churn risks detected</span>\r\n        </div>\r\n      </motion.div>\r\n\r\n      <motion.div\r\n        className=\"absolute top-48 left-[20%] bg-gradient-to-r from-green-500/20 to-blue-500/20 backdrop-blur-md rounded-xl px-4 py-3 text-sm text-white border border-white/20 z-20 shadow-lg\"\r\n        animate={{\r\n          y: [0, -6, 0],\r\n          opacity: [0.8, 1, 0.8],\r\n          scale: [1, 1.02, 1],\r\n        }}\r\n        transition={{\r\n          duration: 5,\r\n          repeat: Infinity,\r\n          repeatType: \"reverse\",\r\n          delay: 2,\r\n        }}\r\n      >\r\n        <div className=\"flex items-center gap-2\">\r\n          <div className=\"w-2 h-2 bg-blue-400 rounded-full animate-pulse\"></div>\r\n          <span className=\"font-medium\">13 emails automated</span>\r\n        </div>\r\n      </motion.div>\r\n\r\n      <motion.div\r\n        className=\"absolute top-56 right-[20%] bg-gradient-to-r from-yellow-500/20 to-orange-500/20 backdrop-blur-md rounded-xl px-4 py-3 text-sm text-white border border-white/20 z-20 shadow-lg\"\r\n        animate={{\r\n          y: [0, 10, 0],\r\n          opacity: [0.8, 1, 0.8],\r\n          scale: [1, 1.02, 1],\r\n        }}\r\n        transition={{\r\n          duration: 3.5,\r\n          repeat: Infinity,\r\n          repeatType: \"reverse\",\r\n          delay: 0.5,\r\n        }}\r\n      >\r\n        <div className=\"flex items-center gap-2\">\r\n          <div className=\"w-2 h-2 bg-purple-400 rounded-full animate-pulse\"></div>\r\n          <span className=\"font-medium\">34 orders processed</span>\r\n        </div>\r\n      </motion.div>\r\n\r\n      {/* Text Content Section Below Image */}\r\n      <div className=\"flex-1 flex flex-col justify-center px-2 sm:px-4 lg:px-6 pb-8 relative z-10\">\r\n        <div className=\"max-w-7xl mx-auto text-center\">\r\n          {/* Badge */}\r\n          <motion.div\r\n            initial={{ opacity: 0, y: 20 }}\r\n            animate={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 0.6 }}\r\n            className=\"inline-flex items-center gap-2 bg-white/10 backdrop-blur-md border border-white/20 rounded-full px-4 py-2 mb-6\"\r\n          >\r\n            <Sparkles className=\"w-4 h-4 text-yellow-400\" />\r\n            <span className=\"text-white text-sm font-medium\">Next-Gen AI Marketing</span>\r\n          </motion.div>\r\n\r\n          {/* Main Heading with Gradient Text */}\r\n          <motion.h1\r\n            initial={{ opacity: 0, y: 30 }}\r\n            animate={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 0.8, delay: 0.2 }}\r\n            className=\"text-3xl md:text-5xl lg:text-6xl font-bold mb-6 leading-tight\"\r\n          >\r\n            <span className=\"text-white\">All-in-One AI Marketing Suite</span>\r\n            <br />\r\n            <span className=\"bg-gradient-to-r from-blue-400 via-purple-400 to-pink-400 bg-clip-text text-transparent\">\r\n              to Grow Your Brand\r\n            </span>\r\n          </motion.h1>\r\n\r\n          {/* Subtitle */}\r\n          <motion.p\r\n            initial={{ opacity: 0, y: 30 }}\r\n            animate={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 0.8, delay: 0.4 }}\r\n            className=\"text-lg md:text-xl text-gray-300 mb-8 max-w-2xl mx-auto leading-relaxed\"\r\n          >\r\n            Automate content, campaigns, and customer insights — all in one\r\n            <span className=\"text-blue-400 font-semibold\"> intelligent marketing platform</span>.\r\n          </motion.p>\r\n\r\n          {/* CTA Buttons */}\r\n          <motion.div\r\n            initial={{ opacity: 0, y: 30 }}\r\n            animate={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 0.8, delay: 0.6 }}\r\n            className=\"flex flex-col sm:flex-row gap-4 justify-center items-center\"\r\n          >\r\n            <Button\r\n              size=\"lg\"\r\n              className=\"bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-8 py-4 text-lg font-semibold rounded-xl shadow-2xl transition-all duration-300 transform hover:scale-105 hover:shadow-blue-500/25 border border-white/10\"\r\n            >\r\n              <span className=\"flex items-center gap-2\">\r\n                BOOK A DEMO\r\n                <motion.div\r\n                  animate={{ x: [0, 4, 0] }}\r\n                  transition={{ duration: 1.5, repeat: Infinity }}\r\n                >\r\n                  →\r\n                </motion.div>\r\n              </span>\r\n            </Button>\r\n            <Button\r\n              variant=\"outline\"\r\n              size=\"lg\"\r\n              className=\"border-white/20 text-white hover:bg-white/10 backdrop-blur-md px-8 py-4 text-lg font-semibold rounded-xl transition-all duration-300 hover:border-white/30\"\r\n            >\r\n              Get Started Free\r\n            </Button>\r\n          </motion.div>\r\n\r\n          {/* Trust indicators */}\r\n          <motion.div\r\n            initial={{ opacity: 0 }}\r\n            animate={{ opacity: 1 }}\r\n            transition={{ duration: 0.8, delay: 0.8 }}\r\n            className=\"mt-8 flex flex-wrap justify-center items-center gap-6 text-sm text-gray-400\"\r\n          >\r\n            <div className=\"flex items-center gap-2\">\r\n              <div className=\"w-2 h-2 bg-green-400 rounded-full\"></div>\r\n              <span>No credit card required</span>\r\n            </div>\r\n            <div className=\"flex items-center gap-2\">\r\n              <div className=\"w-2 h-2 bg-blue-400 rounded-full\"></div>\r\n              <span>14-day free trial</span>\r\n            </div>\r\n            <div className=\"flex items-center gap-2\">\r\n              <div className=\"w-2 h-2 bg-purple-400 rounded-full\"></div>\r\n              <span>Setup in 5 minutes</span>\r\n            </div>\r\n          </motion.div>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  )\r\n}\r\n\r\nexport default Hero\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAOA,MAAM,OAAO;IACX,qBACE,6LAAC;QAAQ,WAAU;;0BAEjB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC,qJAAA,CAAA,mBAAgB;;;;;;;;;;;0BAInB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,OAAO;wBAAK,GAAG;oBAAG;oBACzC,SAAS;wBAAE,SAAS;wBAAG,OAAO;wBAAG,GAAG;oBAAE;oBACtC,YAAY;wBAAE,UAAU;wBAAG,MAAM;oBAAU;oBAC3C,WAAU;;sCAEV,6LAAC;4BACC,WAAU;4BACV,OAAO;gCACL,iBAAiB;4BACnB;;;;;;sCAGF,6LAAC;4BAAI,WAAU;;;;;;sCAGf,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG,CAAC;4BAAG;4BAC9B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,WAAU;;8CAEV,6LAAC,6MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;8CACpB,6LAAC;oCAAK,WAAU;8CAAiC;;;;;;;;;;;;;;;;;;;;;;;0BAMvD,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBACP,GAAG;wBAAC;wBAAG,CAAC;wBAAG;qBAAE;oBACb,SAAS;wBAAC;wBAAK;wBAAG;qBAAI;oBACtB,OAAO;wBAAC;wBAAG;wBAAM;qBAAE;gBACrB;gBACA,YAAY;oBACV,UAAU;oBACV,QAAQ;oBACR,YAAY;gBACd;0BAEA,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAK,WAAU;sCAAc;;;;;;;;;;;;;;;;;0BAIlC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBACP,GAAG;wBAAC;wBAAG;wBAAG;qBAAE;oBACZ,SAAS;wBAAC;wBAAK;wBAAG;qBAAI;oBACtB,OAAO;wBAAC;wBAAG;wBAAM;qBAAE;gBACrB;gBACA,YAAY;oBACV,UAAU;oBACV,QAAQ;oBACR,YAAY;oBACZ,OAAO;gBACT;0BAEA,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAK,WAAU;sCAAc;;;;;;;;;;;;;;;;;0BAIlC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBACP,GAAG;wBAAC;wBAAG,CAAC;wBAAG;qBAAE;oBACb,SAAS;wBAAC;wBAAK;wBAAG;qBAAI;oBACtB,OAAO;wBAAC;wBAAG;wBAAM;qBAAE;gBACrB;gBACA,YAAY;oBACV,UAAU;oBACV,QAAQ;oBACR,YAAY;oBACZ,OAAO;gBACT;0BAEA,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAK,WAAU;sCAAc;;;;;;;;;;;;;;;;;0BAIlC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBACP,GAAG;wBAAC;wBAAG;wBAAI;qBAAE;oBACb,SAAS;wBAAC;wBAAK;wBAAG;qBAAI;oBACtB,OAAO;wBAAC;wBAAG;wBAAM;qBAAE;gBACrB;gBACA,YAAY;oBACV,UAAU;oBACV,QAAQ;oBACR,YAAY;oBACZ,OAAO;gBACT;0BAEA,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAK,WAAU;sCAAc;;;;;;;;;;;;;;;;;0BAKlC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,WAAU;;8CAEV,6LAAC,6MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;8CACpB,6LAAC;oCAAK,WAAU;8CAAiC;;;;;;;;;;;;sCAInD,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;4BACR,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,WAAU;;8CAEV,6LAAC;oCAAK,WAAU;8CAAa;;;;;;8CAC7B,6LAAC;;;;;8CACD,6LAAC;oCAAK,WAAU;8CAA0F;;;;;;;;;;;;sCAM5G,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;4BACP,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,WAAU;;gCACX;8CAEC,6LAAC;oCAAK,WAAU;8CAA8B;;;;;;gCAAsC;;;;;;;sCAItF,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,WAAU;;8CAEV,6LAAC,qIAAA,CAAA,SAAM;oCACL,MAAK;oCACL,WAAU;8CAEV,cAAA,6LAAC;wCAAK,WAAU;;4CAA0B;0DAExC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,SAAS;oDAAE,GAAG;wDAAC;wDAAG;wDAAG;qDAAE;gDAAC;gDACxB,YAAY;oDAAE,UAAU;oDAAK,QAAQ;gDAAS;0DAC/C;;;;;;;;;;;;;;;;;8CAKL,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;sCAMH,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;4BAAE;4BACtB,SAAS;gCAAE,SAAS;4BAAE;4BACtB,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,WAAU;;8CAEV,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;sDAAK;;;;;;;;;;;;8CAER,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;sDAAK;;;;;;;;;;;;8CAER,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;sDAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOpB;KAtNM;uCAwNS", "debugId": null}}, {"offset": {"line": 7128, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Landing%20page/admybrand-ai-suite/src/components/sections/logo-slider.tsx"], "sourcesContent": ["'use client'\n\nimport { motion } from 'framer-motion'\nimport { useState } from 'react'\n\nconst LogoSlider = () => {\n  const [isPaused, setIsPaused] = useState(false)\n\n  // AI Marketing and Tech companies\n  const logos = [\n    { name: 'HubSpot', style: 'normal', weight: 'semibold' },\n    { name: 'Mailchi<PERSON>', style: 'normal', weight: 'medium' },\n    { name: 'Salesforce', style: 'normal', weight: 'bold' },\n    { name: '<PERSON><PERSON>', style: 'normal', weight: 'medium' },\n    { name: 'Hootsuite', style: 'italic', weight: 'light' },\n    { name: 'Buffer', style: 'normal', weight: 'semibold' },\n    { name: '<PERSON>va', style: 'normal', weight: 'bold' },\n    { name: 'Klaviyo', style: 'normal', weight: 'medium' },\n    { name: '<PERSON>', style: 'italic', weight: 'normal' },\n    { name: 'Copy.ai', style: 'normal', weight: 'semibold' },\n    { name: 'Drift', style: 'normal', weight: 'bold' },\n    { name: 'Intercom', style: 'normal', weight: 'medium' },\n  ]\n\n  // Duplicate logos for seamless infinite scroll\n  const duplicatedLogos = [...logos, ...logos]\n\n  return (\n    <section className=\"py-4 bg-black\">\n      <div className=\"max-w-7xl mx-auto px-2 sm:px-4 lg:px-6\">\n\n\n        {/* Logo Slider Container */}\n        <div className=\"relative overflow-hidden py-8\">\n          {/* Enhanced gradient overlays */}\n          <div className=\"absolute left-0 top-0 bottom-0 w-32 bg-gradient-to-r from-black via-black/80 to-transparent z-10\" />\n          <div className=\"absolute right-0 top-0 bottom-0 w-32 bg-gradient-to-l from-black via-black/80 to-transparent z-10\" />\n\n          {/* Subtle background glow */}\n          <div className=\"absolute inset-0 bg-gradient-to-r from-transparent via-blue-500/5 to-transparent\" />\n          \n          {/* Scrolling logos */}\n          <motion.div\n            className=\"flex items-center gap-16 md:gap-20 lg:gap-24\"\n            animate={isPaused ? {} : {\n              x: [0, -50 + '%'],\n            }}\n            transition={{\n              x: {\n                repeat: Infinity,\n                repeatType: \"loop\",\n                duration: 40,\n                ease: \"linear\",\n              },\n            }}\n            style={{\n              width: `${duplicatedLogos.length * 240}px`,\n            }}\n            onHoverStart={() => setIsPaused(true)}\n            onHoverEnd={() => setIsPaused(false)}\n          >\n            {duplicatedLogos.map((logo, index) => (\n              <motion.div\n                key={`${logo.name}-${index}`}\n                className=\"flex-shrink-0 w-40 h-20 md:w-48 md:h-24 flex items-center justify-center\"\n                whileHover={{\n                  scale: 1.05,\n                  y: -3,\n                }}\n                transition={{ duration: 0.4, ease: \"easeOut\" }}\n              >\n                {/* Glassmorphism logo container */}\n                <div className=\"relative w-full h-full flex items-center justify-center group cursor-pointer\">\n                  {/* Glassmorphism background - always visible */}\n                  <div className=\"absolute inset-0 bg-gradient-to-br from-white/10 via-white/5 to-white/10 backdrop-blur-md rounded-2xl border border-white/20 shadow-xl group-hover:shadow-2xl group-hover:shadow-blue-500/20 transition-all duration-500\" />\n\n                  {/* Inner glow effect */}\n                  <div className=\"absolute inset-0 bg-gradient-to-br from-blue-500/10 via-transparent to-purple-500/10 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-500\" />\n\n                  {/* Subtle border highlight */}\n                  <div className=\"absolute inset-0 rounded-2xl border border-gradient-to-br from-blue-400/30 via-transparent to-purple-400/30 opacity-0 group-hover:opacity-100 transition-opacity duration-500\" />\n\n                  {/* Logo text */}\n                  <span\n                    className={`\n                      relative z-10 text-gray-300 text-base md:text-lg\n                      group-hover:text-white transition-all duration-500\n                      whitespace-nowrap tracking-wide px-4 py-2\n                      ${logo.style === 'italic' ? 'italic' : ''}\n                      ${logo.weight === 'light' ? 'font-light' : ''}\n                      ${logo.weight === 'normal' ? 'font-normal' : ''}\n                      ${logo.weight === 'medium' ? 'font-medium' : ''}\n                      ${logo.weight === 'semibold' ? 'font-semibold' : ''}\n                      ${logo.weight === 'bold' ? 'font-bold' : ''}\n                      ${logo.weight === 'black' ? 'font-black' : ''}\n                    `}\n                    style={{\n                      filter: 'drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3))',\n                      transition: 'all 0.5s ease',\n                    }}\n                    onMouseEnter={(e) => {\n                      e.target.style.filter = 'drop-shadow(0 0 12px rgba(59, 130, 246, 0.8)) drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3))';\n                    }}\n                    onMouseLeave={(e) => {\n                      e.target.style.filter = 'drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3))';\n                    }}\n                  >\n                    {logo.name}\n                  </span>\n                </div>\n\n                {/* Uncomment this when you have actual logo images */}\n                {/*\n                <img\n                  src={logo.url}\n                  alt={logo.name}\n                  className=\"w-full h-full object-contain filter grayscale hover:grayscale-0 transition-all duration-300 opacity-60 hover:opacity-100\"\n                />\n                */}\n              </motion.div>\n            ))}\n          </motion.div>\n        </div>\n      </div>\n    </section>\n  )\n}\n\nexport default LogoSlider\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAKA,MAAM,aAAa;;IACjB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,kCAAkC;IAClC,MAAM,QAAQ;QACZ;YAAE,MAAM;YAAW,OAAO;YAAU,QAAQ;QAAW;QACvD;YAAE,MAAM;YAAa,OAAO;YAAU,QAAQ;QAAS;QACvD;YAAE,MAAM;YAAc,OAAO;YAAU,QAAQ;QAAO;QACtD;YAAE,MAAM;YAAW,OAAO;YAAU,QAAQ;QAAS;QACrD;YAAE,MAAM;YAAa,OAAO;YAAU,QAAQ;QAAQ;QACtD;YAAE,MAAM;YAAU,OAAO;YAAU,QAAQ;QAAW;QACtD;YAAE,MAAM;YAAS,OAAO;YAAU,QAAQ;QAAO;QACjD;YAAE,MAAM;YAAW,OAAO;YAAU,QAAQ;QAAS;QACrD;YAAE,MAAM;YAAU,OAAO;YAAU,QAAQ;QAAS;QACpD;YAAE,MAAM;YAAW,OAAO;YAAU,QAAQ;QAAW;QACvD;YAAE,MAAM;YAAS,OAAO;YAAU,QAAQ;QAAO;QACjD;YAAE,MAAM;YAAY,OAAO;YAAU,QAAQ;QAAS;KACvD;IAED,+CAA+C;IAC/C,MAAM,kBAAkB;WAAI;WAAU;KAAM;IAE5C,qBACE,6LAAC;QAAQ,WAAU;kBACjB,cAAA,6LAAC;YAAI,WAAU;sBAIb,cAAA,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;kCAGf,6LAAC;wBAAI,WAAU;;;;;;kCAGf,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS,WAAW,CAAC,IAAI;4BACvB,GAAG;gCAAC;gCAAG,CAAC,KAAK;6BAAI;wBACnB;wBACA,YAAY;4BACV,GAAG;gCACD,QAAQ;gCACR,YAAY;gCACZ,UAAU;gCACV,MAAM;4BACR;wBACF;wBACA,OAAO;4BACL,OAAO,AAAC,GAA+B,OAA7B,gBAAgB,MAAM,GAAG,KAAI;wBACzC;wBACA,cAAc,IAAM,YAAY;wBAChC,YAAY,IAAM,YAAY;kCAE7B,gBAAgB,GAAG,CAAC,CAAC,MAAM,sBAC1B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,WAAU;gCACV,YAAY;oCACV,OAAO;oCACP,GAAG,CAAC;gCACN;gCACA,YAAY;oCAAE,UAAU;oCAAK,MAAM;gCAAU;0CAG7C,cAAA,6LAAC;oCAAI,WAAU;;sDAEb,6LAAC;4CAAI,WAAU;;;;;;sDAGf,6LAAC;4CAAI,WAAU;;;;;;sDAGf,6LAAC;4CAAI,WAAU;;;;;;sDAGf,6LAAC;4CACC,WAAW,AAAC,8OAKR,OADA,KAAK,KAAK,KAAK,WAAW,WAAW,IAAG,4BAExC,OADA,KAAK,MAAM,KAAK,UAAU,eAAe,IAAG,4BAE5C,OADA,KAAK,MAAM,KAAK,WAAW,gBAAgB,IAAG,4BAE9C,OADA,KAAK,MAAM,KAAK,WAAW,gBAAgB,IAAG,4BAE9C,OADA,KAAK,MAAM,KAAK,aAAa,kBAAkB,IAAG,4BAElD,OADA,KAAK,MAAM,KAAK,SAAS,cAAc,IAAG,4BACE,OAA5C,KAAK,MAAM,KAAK,UAAU,eAAe,IAAG;4CAEhD,OAAO;gDACL,QAAQ;gDACR,YAAY;4CACd;4CACA,cAAc,CAAC;gDACb,EAAE,MAAM,CAAC,KAAK,CAAC,MAAM,GAAG;4CAC1B;4CACA,cAAc,CAAC;gDACb,EAAE,MAAM,CAAC,KAAK,CAAC,MAAM,GAAG;4CAC1B;sDAEC,KAAK,IAAI;;;;;;;;;;;;+BA5CT,AAAC,GAAe,OAAb,KAAK,IAAI,EAAC,KAAS,OAAN;;;;;;;;;;;;;;;;;;;;;;;;;;AA+DrC;GAzHM;KAAA;uCA2HS", "debugId": null}}, {"offset": {"line": 7358, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Landing%20page/admybrand-ai-suite/src/components/sections/pricing-calculator.tsx"], "sourcesContent": ["'use client'\n\nimport { Badge } from '@/components/ui/badge'\nimport { Button } from '@/components/ui/button'\nimport { Card, CardContent, CardHeader } from '@/components/ui/card'\nimport { motion } from 'framer-motion'\nimport { Calculator, Users, FileText, Mail, BarChart3, Zap } from 'lucide-react'\nimport { useState } from 'react'\n\nconst PricingCalculator = () => {\n  const [teamSize, setTeamSize] = useState(5)\n  const [contentVolume, setContentVolume] = useState(1000)\n  const [emailContacts, setEmailContacts] = useState(1000)\n  const [socialPlatforms, setSocialPlatforms] = useState(3)\n\n  // Calculate recommended plan and pricing\n  const calculateRecommendation = () => {\n    let score = 0\n    \n    if (teamSize > 10) score += 2\n    else if (teamSize > 5) score += 1\n    \n    if (contentVolume > 5000) score += 2\n    else if (contentVolume > 1000) score += 1\n    \n    if (emailContacts > 2000) score += 2\n    else if (emailContacts > 500) score += 1\n    \n    if (socialPlatforms > 5) score += 2\n    else if (socialPlatforms > 3) score += 1\n\n    if (score >= 6) return { plan: 'Enterprise', price: 199, savings: 50 }\n    if (score >= 3) return { plan: 'Professional', price: 79, savings: 30 }\n    return { plan: 'Starter', price: 29, savings: 10 }\n  }\n\n  const recommendation = calculateRecommendation()\n\n  return (\n    <section className=\"py-24 bg-gradient-to-br from-black via-gray-900 to-black\">\n      <div className=\"max-w-7xl mx-auto px-2 sm:px-4 lg:px-6\">\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          viewport={{ once: true }}\n          className=\"text-center mb-16\"\n        >\n          <Badge className=\"mb-4 bg-white/10 text-white border-white/20 backdrop-blur-sm\">\n            <Calculator className=\"w-4 h-4 mr-2\" />\n            Pricing Calculator\n          </Badge>\n          <h2 className=\"text-4xl md:text-5xl font-bold text-white mb-6\">\n            Find your\n            <span className=\"bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent\"> perfect plan</span>\n          </h2>\n          <p className=\"text-xl text-gray-300 max-w-3xl mx-auto\">\n            Answer a few questions and we'll recommend the best plan for your needs\n          </p>\n        </motion.div>\n\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-12\">\n          {/* Calculator Form */}\n          <motion.div\n            initial={{ opacity: 0, x: -30 }}\n            whileInView={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.8 }}\n            viewport={{ once: true }}\n          >\n            <Card className=\"bg-white/5 backdrop-blur-xl border border-white/10 rounded-2xl\">\n              <CardHeader>\n                <h3 className=\"text-2xl font-bold text-white mb-2\">Tell us about your business</h3>\n                <p className=\"text-gray-300\">Customize the calculator to get accurate recommendations</p>\n              </CardHeader>\n              <CardContent className=\"space-y-8\">\n                {/* Team Size */}\n                <div>\n                  <div className=\"flex items-center gap-3 mb-4\">\n                    <Users className=\"w-5 h-5 text-blue-400\" />\n                    <label className=\"text-white font-medium\">Team Size: {teamSize} people</label>\n                  </div>\n                  <input\n                    type=\"range\"\n                    min=\"1\"\n                    max=\"50\"\n                    value={teamSize}\n                    onChange={(e) => setTeamSize(Number(e.target.value))}\n                    className=\"w-full h-2 bg-white/20 rounded-lg appearance-none cursor-pointer slider\"\n                  />\n                  <div className=\"flex justify-between text-sm text-gray-400 mt-2\">\n                    <span>1</span>\n                    <span>50+</span>\n                  </div>\n                </div>\n\n                {/* Content Volume */}\n                <div>\n                  <div className=\"flex items-center gap-3 mb-4\">\n                    <FileText className=\"w-5 h-5 text-green-400\" />\n                    <label className=\"text-white font-medium\">Monthly Content: {contentVolume.toLocaleString()} words</label>\n                  </div>\n                  <input\n                    type=\"range\"\n                    min=\"500\"\n                    max=\"20000\"\n                    step=\"500\"\n                    value={contentVolume}\n                    onChange={(e) => setContentVolume(Number(e.target.value))}\n                    className=\"w-full h-2 bg-white/20 rounded-lg appearance-none cursor-pointer slider\"\n                  />\n                  <div className=\"flex justify-between text-sm text-gray-400 mt-2\">\n                    <span>500</span>\n                    <span>20K+</span>\n                  </div>\n                </div>\n\n                {/* Email Contacts */}\n                <div>\n                  <div className=\"flex items-center gap-3 mb-4\">\n                    <Mail className=\"w-5 h-5 text-purple-400\" />\n                    <label className=\"text-white font-medium\">Email Contacts: {emailContacts.toLocaleString()}</label>\n                  </div>\n                  <input\n                    type=\"range\"\n                    min=\"100\"\n                    max=\"10000\"\n                    step=\"100\"\n                    value={emailContacts}\n                    onChange={(e) => setEmailContacts(Number(e.target.value))}\n                    className=\"w-full h-2 bg-white/20 rounded-lg appearance-none cursor-pointer slider\"\n                  />\n                  <div className=\"flex justify-between text-sm text-gray-400 mt-2\">\n                    <span>100</span>\n                    <span>10K+</span>\n                  </div>\n                </div>\n\n                {/* Social Platforms */}\n                <div>\n                  <div className=\"flex items-center gap-3 mb-4\">\n                    <BarChart3 className=\"w-5 h-5 text-orange-400\" />\n                    <label className=\"text-white font-medium\">Social Platforms: {socialPlatforms}</label>\n                  </div>\n                  <input\n                    type=\"range\"\n                    min=\"1\"\n                    max=\"10\"\n                    value={socialPlatforms}\n                    onChange={(e) => setSocialPlatforms(Number(e.target.value))}\n                    className=\"w-full h-2 bg-white/20 rounded-lg appearance-none cursor-pointer slider\"\n                  />\n                  <div className=\"flex justify-between text-sm text-gray-400 mt-2\">\n                    <span>1</span>\n                    <span>10+</span>\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n          </motion.div>\n\n          {/* Recommendation */}\n          <motion.div\n            initial={{ opacity: 0, x: 30 }}\n            whileInView={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.8, delay: 0.2 }}\n            viewport={{ once: true }}\n          >\n            <Card className=\"bg-gradient-to-br from-blue-500/10 to-purple-500/10 backdrop-blur-xl border border-white/20 rounded-2xl\">\n              <CardHeader>\n                <div className=\"flex items-center gap-3 mb-4\">\n                  <Zap className=\"w-6 h-6 text-yellow-400\" />\n                  <h3 className=\"text-2xl font-bold text-white\">Recommended Plan</h3>\n                </div>\n              </CardHeader>\n              <CardContent>\n                <div className=\"text-center mb-8\">\n                  <div className=\"text-5xl font-bold text-white mb-2\">\n                    {recommendation.plan}\n                  </div>\n                  <div className=\"text-3xl font-bold text-blue-400 mb-4\">\n                    ${recommendation.price}<span className=\"text-lg text-gray-400\">/month</span>\n                  </div>\n                  <Badge className=\"bg-green-500/20 text-green-400 border-green-500/30\">\n                    Save ${recommendation.savings}/month vs competitors\n                  </Badge>\n                </div>\n\n                <div className=\"space-y-4 mb-8\">\n                  <div className=\"flex items-center gap-3 text-gray-300\">\n                    <div className=\"w-2 h-2 bg-blue-400 rounded-full\"></div>\n                    <span>Perfect for {teamSize} team members</span>\n                  </div>\n                  <div className=\"flex items-center gap-3 text-gray-300\">\n                    <div className=\"w-2 h-2 bg-green-400 rounded-full\"></div>\n                    <span>Handles {contentVolume.toLocaleString()} words/month</span>\n                  </div>\n                  <div className=\"flex items-center gap-3 text-gray-300\">\n                    <div className=\"w-2 h-2 bg-purple-400 rounded-full\"></div>\n                    <span>Manages {emailContacts.toLocaleString()} email contacts</span>\n                  </div>\n                  <div className=\"flex items-center gap-3 text-gray-300\">\n                    <div className=\"w-2 h-2 bg-orange-400 rounded-full\"></div>\n                    <span>Supports {socialPlatforms} social platforms</span>\n                  </div>\n                </div>\n\n                <Button className=\"w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white py-3 rounded-xl font-semibold\">\n                  Start Free Trial\n                </Button>\n              </CardContent>\n            </Card>\n          </motion.div>\n        </div>\n      </div>\n    </section>\n  )\n}\n\nexport default PricingCalculator\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;;;AAPA;;;;;;;AASA,MAAM,oBAAoB;;IACxB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,yCAAyC;IACzC,MAAM,0BAA0B;QAC9B,IAAI,QAAQ;QAEZ,IAAI,WAAW,IAAI,SAAS;aACvB,IAAI,WAAW,GAAG,SAAS;QAEhC,IAAI,gBAAgB,MAAM,SAAS;aAC9B,IAAI,gBAAgB,MAAM,SAAS;QAExC,IAAI,gBAAgB,MAAM,SAAS;aAC9B,IAAI,gBAAgB,KAAK,SAAS;QAEvC,IAAI,kBAAkB,GAAG,SAAS;aAC7B,IAAI,kBAAkB,GAAG,SAAS;QAEvC,IAAI,SAAS,GAAG,OAAO;YAAE,MAAM;YAAc,OAAO;YAAK,SAAS;QAAG;QACrE,IAAI,SAAS,GAAG,OAAO;YAAE,MAAM;YAAgB,OAAO;YAAI,SAAS;QAAG;QACtE,OAAO;YAAE,MAAM;YAAW,OAAO;YAAI,SAAS;QAAG;IACnD;IAEA,MAAM,iBAAiB;IAEvB,qBACE,6LAAC;QAAQ,WAAU;kBACjB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAEV,6LAAC,oIAAA,CAAA,QAAK;4BAAC,WAAU;;8CACf,6LAAC,iNAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;sCAGzC,6LAAC;4BAAG,WAAU;;gCAAiD;8CAE7D,6LAAC;oCAAK,WAAU;8CAA6E;;;;;;;;;;;;sCAE/F,6LAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;8BAKzD,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG,CAAC;4BAAG;4BAC9B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,UAAU;gCAAE,MAAM;4BAAK;sCAEvB,cAAA,6LAAC,mIAAA,CAAA,OAAI;gCAAC,WAAU;;kDACd,6LAAC,mIAAA,CAAA,aAAU;;0DACT,6LAAC;gDAAG,WAAU;0DAAqC;;;;;;0DACnD,6LAAC;gDAAE,WAAU;0DAAgB;;;;;;;;;;;;kDAE/B,6LAAC,mIAAA,CAAA,cAAW;wCAAC,WAAU;;0DAErB,6LAAC;;kEACC,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,uMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;0EACjB,6LAAC;gEAAM,WAAU;;oEAAyB;oEAAY;oEAAS;;;;;;;;;;;;;kEAEjE,6LAAC;wDACC,MAAK;wDACL,KAAI;wDACJ,KAAI;wDACJ,OAAO;wDACP,UAAU,CAAC,IAAM,YAAY,OAAO,EAAE,MAAM,CAAC,KAAK;wDAClD,WAAU;;;;;;kEAEZ,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;0EAAK;;;;;;0EACN,6LAAC;0EAAK;;;;;;;;;;;;;;;;;;0DAKV,6LAAC;;kEACC,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,iNAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;0EACpB,6LAAC;gEAAM,WAAU;;oEAAyB;oEAAkB,cAAc,cAAc;oEAAG;;;;;;;;;;;;;kEAE7F,6LAAC;wDACC,MAAK;wDACL,KAAI;wDACJ,KAAI;wDACJ,MAAK;wDACL,OAAO;wDACP,UAAU,CAAC,IAAM,iBAAiB,OAAO,EAAE,MAAM,CAAC,KAAK;wDACvD,WAAU;;;;;;kEAEZ,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;0EAAK;;;;;;0EACN,6LAAC;0EAAK;;;;;;;;;;;;;;;;;;0DAKV,6LAAC;;kEACC,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,qMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;0EAChB,6LAAC;gEAAM,WAAU;;oEAAyB;oEAAiB,cAAc,cAAc;;;;;;;;;;;;;kEAEzF,6LAAC;wDACC,MAAK;wDACL,KAAI;wDACJ,KAAI;wDACJ,MAAK;wDACL,OAAO;wDACP,UAAU,CAAC,IAAM,iBAAiB,OAAO,EAAE,MAAM,CAAC,KAAK;wDACvD,WAAU;;;;;;kEAEZ,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;0EAAK;;;;;;0EACN,6LAAC;0EAAK;;;;;;;;;;;;;;;;;;0DAKV,6LAAC;;kEACC,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,qNAAA,CAAA,YAAS;gEAAC,WAAU;;;;;;0EACrB,6LAAC;gEAAM,WAAU;;oEAAyB;oEAAmB;;;;;;;;;;;;;kEAE/D,6LAAC;wDACC,MAAK;wDACL,KAAI;wDACJ,KAAI;wDACJ,OAAO;wDACP,UAAU,CAAC,IAAM,mBAAmB,OAAO,EAAE,MAAM,CAAC,KAAK;wDACzD,WAAU;;;;;;kEAEZ,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;0EAAK;;;;;;0EACN,6LAAC;0EAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQhB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,UAAU;gCAAE,MAAM;4BAAK;sCAEvB,cAAA,6LAAC,mIAAA,CAAA,OAAI;gCAAC,WAAU;;kDACd,6LAAC,mIAAA,CAAA,aAAU;kDACT,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,mMAAA,CAAA,MAAG;oDAAC,WAAU;;;;;;8DACf,6LAAC;oDAAG,WAAU;8DAAgC;;;;;;;;;;;;;;;;;kDAGlD,6LAAC,mIAAA,CAAA,cAAW;;0DACV,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACZ,eAAe,IAAI;;;;;;kEAEtB,6LAAC;wDAAI,WAAU;;4DAAwC;4DACnD,eAAe,KAAK;0EAAC,6LAAC;gEAAK,WAAU;0EAAwB;;;;;;;;;;;;kEAEjE,6LAAC,oIAAA,CAAA,QAAK;wDAAC,WAAU;;4DAAqD;4DAC7D,eAAe,OAAO;4DAAC;;;;;;;;;;;;;0DAIlC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;;;;;0EACf,6LAAC;;oEAAK;oEAAa;oEAAS;;;;;;;;;;;;;kEAE9B,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;;;;;0EACf,6LAAC;;oEAAK;oEAAS,cAAc,cAAc;oEAAG;;;;;;;;;;;;;kEAEhD,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;;;;;0EACf,6LAAC;;oEAAK;oEAAS,cAAc,cAAc;oEAAG;;;;;;;;;;;;;kEAEhD,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;;;;;0EACf,6LAAC;;oEAAK;oEAAU;oEAAgB;;;;;;;;;;;;;;;;;;;0DAIpC,6LAAC,qIAAA,CAAA,SAAM;gDAAC,WAAU;0DAAuI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUzK;GA/MM;KAAA;uCAiNS", "debugId": null}}, {"offset": {"line": 8109, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Landing%20page/admybrand-ai-suite/src/components/ui/avatar.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst avatarVariants = cva(\n  \"relative flex shrink-0 overflow-hidden rounded-full\",\n  {\n    variants: {\n      size: {\n        sm: \"h-8 w-8\",\n        default: \"h-10 w-10\",\n        lg: \"h-12 w-12\",\n        xl: \"h-16 w-16\",\n        \"2xl\": \"h-20 w-20\",\n      },\n    },\n    defaultVariants: {\n      size: \"default\",\n    },\n  }\n)\n\nexport interface AvatarProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof avatarVariants> {\n  src?: string\n  alt?: string\n  fallback?: string\n}\n\nconst Avatar = React.forwardRef<HTMLDivElement, AvatarProps>(\n  ({ className, size, src, alt, fallback, ...props }, ref) => {\n    const [imageError, setImageError] = React.useState(false)\n\n    const handleImageError = () => {\n      setImageError(true)\n    }\n\n    return (\n      <div\n        ref={ref}\n        className={cn(avatarVariants({ size, className }))}\n        {...props}\n      >\n        {src && !imageError ? (\n          <img\n            className=\"aspect-square h-full w-full object-cover\"\n            src={src}\n            alt={alt}\n            onError={handleImageError}\n          />\n        ) : (\n          <div className=\"flex h-full w-full items-center justify-center rounded-full bg-muted\">\n            <span className=\"text-sm font-medium text-muted-foreground\">\n              {fallback || alt?.charAt(0)?.toUpperCase() || \"?\"}\n            </span>\n          </div>\n        )}\n      </div>\n    )\n  }\n)\nAvatar.displayName = \"Avatar\"\n\nexport { Avatar, avatarVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,uDACA;IACE,UAAU;QACR,MAAM;YACJ,IAAI;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,OAAO;QACT;IACF;IACA,iBAAiB;QACf,MAAM;IACR;AACF;AAWF,MAAM,uBAAS,GAAA,6JAAA,CAAA,aAAgB,SAC7B,QAAoD;QAAnD,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,QAAQ,EAAE,GAAG,OAAO;QAuBzB;;IAtBvB,MAAM,CAAC,YAAY,cAAc,GAAG,6JAAA,CAAA,WAAc,CAAC;IAEnD,MAAM,mBAAmB;QACvB,cAAc;IAChB;IAEA,qBACE,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAM;QAAU;QAC9C,GAAG,KAAK;kBAER,OAAO,CAAC,2BACP,6LAAC;YACC,WAAU;YACV,KAAK;YACL,KAAK;YACL,SAAS;;;;;qEAGX,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAK,WAAU;0BACb,aAAY,gBAAA,2BAAA,cAAA,IAAK,MAAM,CAAC,gBAAZ,kCAAA,YAAgB,WAAW,OAAM;;;;;;;;;;;;;;;;AAM1D;;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 8196, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Landing%20page/admybrand-ai-suite/src/components/sections/testimonials.tsx"], "sourcesContent": ["'use client'\n\nimport { Avatar } from '@/components/ui/avatar'\nimport { Badge } from '@/components/ui/badge'\nimport { <PERSON><PERSON> } from '@/components/ui/button'\nimport { Card, CardContent } from '@/components/ui/card'\nimport { AnimatePresence, motion } from 'framer-motion'\nimport { ChevronLeft, ChevronRight, Quote, Star } from 'lucide-react'\nimport { useEffect, useState } from 'react'\n\nconst testimonials = [\n  {\n    id: 1,\n    name: \"<PERSON>\",\n    role: \"Marketing Director\",\n    company: \"TechFlow Inc.\",\n    avatar: \"https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face\",\n    rating: 5,\n    content: \"ADmyBRAND AI Suite transformed our marketing completely. We've seen a 300% increase in engagement and our content creation time has been cut by 80%. The AI understands our brand voice perfectly.\",\n    results: \"300% engagement increase\",\n    logo: \"🚀\"\n  },\n  {\n    id: 2,\n    name: \"<PERSON>\",\n    role: \"CEO\",\n    company: \"GrowthLab\",\n    avatar: \"https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face\",\n    rating: 5,\n    content: \"The ROI we've achieved with ADmyBRAND is incredible. Our ad campaigns are now 10x more effective, and the automated optimization saves us countless hours every week.\",\n    results: \"10x ad effectiveness\",\n    logo: \"📈\"\n  },\n  {\n    id: 3,\n    name: \"Emily Watson\",\n    role: \"Content Manager\",\n    company: \"Creative Studios\",\n    avatar: \"https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face\",\n    rating: 5,\n    content: \"As a small team, we needed something that could scale with us. ADmyBRAND's AI tools let us compete with much larger agencies. The quality of generated content is outstanding.\",\n    results: \"5x content output\",\n    logo: \"🎨\"\n  },\n  {\n    id: 4,\n    name: \"David Kim\",\n    role: \"Digital Marketing Lead\",\n    company: \"E-commerce Plus\",\n    avatar: \"https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face\",\n    rating: 5,\n    content: \"The email marketing automation alone has paid for itself. Our open rates increased by 150% and conversions by 200%. The AI personalization is game-changing.\",\n    results: \"200% conversion increase\",\n    logo: \"💼\"\n  },\n  {\n    id: 5,\n    name: \"Lisa Thompson\",\n    role: \"Brand Manager\",\n    company: \"Fashion Forward\",\n    avatar: \"https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=150&h=150&fit=crop&crop=face\",\n    rating: 5,\n    content: \"Managing multiple brand voices was a nightmare before ADmyBRAND. Now our AI maintains perfect consistency across all our brands while adapting to each unique style.\",\n    results: \"Perfect brand consistency\",\n    logo: \"👗\"\n  },\n  {\n    id: 6,\n    name: \"Alex Johnson\",\n    role: \"Startup Founder\",\n    company: \"InnovateTech\",\n    avatar: \"https://images.unsplash.com/photo-1560250097-0b93528c311a?w=150&h=150&fit=crop&crop=face\",\n    rating: 5,\n    content: \"Starting with limited marketing budget, ADmyBRAND helped us achieve enterprise-level results. We've grown from 0 to 100K followers in just 6 months.\",\n    results: \"0 to 100K followers\",\n    logo: \"💡\"\n  }\n]\n\nconst Testimonials = () => {\n  const [currentIndex, setCurrentIndex] = useState(0)\n  const [isAutoPlaying, setIsAutoPlaying] = useState(true)\n\n  // Auto-play functionality\n  useEffect(() => {\n    if (!isAutoPlaying) return\n\n    const interval = setInterval(() => {\n      setCurrentIndex((prev) => (prev + 1) % testimonials.length)\n    }, 5000)\n\n    return () => clearInterval(interval)\n  }, [isAutoPlaying])\n\n  const nextTestimonial = () => {\n    setCurrentIndex((prev) => (prev + 1) % testimonials.length)\n  }\n\n  const prevTestimonial = () => {\n    setCurrentIndex((prev) => (prev - 1 + testimonials.length) % testimonials.length)\n  }\n\n  const goToTestimonial = (index: number) => {\n    setCurrentIndex(index)\n  }\n\n  return (\n    <section className=\"py-24 bg-black\">\n      <div className=\"max-w-7xl mx-auto px-2 sm:px-4 lg:px-6\">\n        {/* Section Header */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          viewport={{ once: true }}\n          className=\"text-center mb-16\"\n        >\n          <Badge className=\"mb-4 bg-white/10 text-white border-white/20 backdrop-blur-sm\">\n            Customer Success\n          </Badge>\n          <h2 className=\"text-4xl md:text-5xl font-bold text-white mb-6\">\n            Loved by\n            <span className=\"bg-gradient-to-r from-blue-400 to-cyan-400 bg-clip-text text-transparent\"> marketing teams</span>\n            <br />worldwide\n          </h2>\n          <p className=\"text-xl text-gray-300 max-w-3xl mx-auto\">\n            Join thousands of businesses that have transformed their marketing with ADmyBRAND AI Suite.\n          </p>\n        </motion.div>\n\n        {/* Main Testimonial Display */}\n        <div className=\"relative mb-12\">\n          <div \n            className=\"flex items-center justify-center\"\n            onMouseEnter={() => setIsAutoPlaying(false)}\n            onMouseLeave={() => setIsAutoPlaying(true)}\n          >\n            <AnimatePresence mode=\"wait\">\n              <motion.div\n                key={currentIndex}\n                initial={{ opacity: 0, x: 100 }}\n                animate={{ opacity: 1, x: 0 }}\n                exit={{ opacity: 0, x: -100 }}\n                transition={{ duration: 0.5 }}\n                className=\"w-full max-w-4xl\"\n              >\n                <Card className=\"border border-white/10 shadow-2xl bg-white/5 backdrop-blur-md\">\n                  <CardContent className=\"p-8 md:p-12\">\n                    <div className=\"flex flex-col md:flex-row items-center gap-8\">\n                      {/* Quote Icon */}\n                      <div className=\"flex-shrink-0\">\n                        <div className=\"w-16 h-16 bg-white/10 backdrop-blur-sm border border-white/20 rounded-full flex items-center justify-center\">\n                          <Quote className=\"w-8 h-8 text-white\" />\n                        </div>\n                      </div>\n\n                      {/* Content */}\n                      <div className=\"flex-1 text-center md:text-left\">\n                        {/* Stars */}\n                        <div className=\"flex justify-center md:justify-start mb-4\">\n                          {[...Array(testimonials[currentIndex].rating)].map((_, i) => (\n                            <Star key={i} className=\"w-5 h-5 text-yellow-400 fill-current\" />\n                          ))}\n                        </div>\n\n                        {/* Quote */}\n                        <blockquote className=\"text-xl md:text-2xl text-white mb-6 leading-relaxed\">\n                          \"{testimonials[currentIndex].content}\"\n                        </blockquote>\n\n                        {/* Results Badge */}\n                        <Badge className=\"mb-6 bg-blue-500/20 text-blue-400 border-blue-500/30\">\n                          {testimonials[currentIndex].results}\n                        </Badge>\n\n                        {/* Author Info */}\n                        <div className=\"flex items-center justify-center md:justify-start gap-4\">\n                          <Avatar\n                            src={testimonials[currentIndex].avatar}\n                            alt={testimonials[currentIndex].name}\n                            size=\"lg\"\n                          />\n                          <div>\n                            <div className=\"font-semibold text-white\">\n                              {testimonials[currentIndex].name}\n                            </div>\n                            <div className=\"text-gray-300\">\n                              {testimonials[currentIndex].role}\n                            </div>\n                            <div className=\"flex items-center gap-2 text-sm text-gray-400\">\n                              <span>{testimonials[currentIndex].logo}</span>\n                              {testimonials[currentIndex].company}\n                            </div>\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                  </CardContent>\n                </Card>\n              </motion.div>\n            </AnimatePresence>\n          </div>\n\n          {/* Navigation Buttons */}\n          <Button\n            variant=\"ghost\"\n            size=\"icon\"\n            onClick={prevTestimonial}\n            className=\"absolute left-4 top-1/2 transform -translate-y-1/2 bg-white/80 backdrop-blur-sm shadow-lg hover:bg-white\"\n          >\n            <ChevronLeft className=\"w-6 h-6\" />\n          </Button>\n          <Button\n            variant=\"ghost\"\n            size=\"icon\"\n            onClick={nextTestimonial}\n            className=\"absolute right-4 top-1/2 transform -translate-y-1/2 bg-white/80 backdrop-blur-sm shadow-lg hover:bg-white\"\n          >\n            <ChevronRight className=\"w-6 h-6\" />\n          </Button>\n        </div>\n\n        {/* Dots Indicator */}\n        <div className=\"flex justify-center space-x-2 mb-12\">\n          {testimonials.map((_, index) => (\n            <button\n              key={index}\n              onClick={() => goToTestimonial(index)}\n              className={`w-3 h-3 rounded-full transition-all duration-300 ${\n                index === currentIndex\n                  ? 'bg-purple-600 scale-125'\n                  : 'bg-gray-300 hover:bg-gray-400'\n              }`}\n            />\n          ))}\n        </div>\n\n        {/* Stats Section */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          viewport={{ once: true }}\n          className=\"grid grid-cols-2 md:grid-cols-4 gap-8 text-center\"\n        >\n          <div>\n            <div className=\"text-3xl md:text-4xl font-bold text-purple-600 mb-2\">50K+</div>\n            <div className=\"text-gray-600\">Happy Customers</div>\n          </div>\n          <div>\n            <div className=\"text-3xl md:text-4xl font-bold text-blue-600 mb-2\">98%</div>\n            <div className=\"text-gray-600\">Satisfaction Rate</div>\n          </div>\n          <div>\n            <div className=\"text-3xl md:text-4xl font-bold text-green-600 mb-2\">5M+</div>\n            <div className=\"text-gray-600\">Content Pieces</div>\n          </div>\n          <div>\n            <div className=\"text-3xl md:text-4xl font-bold text-orange-600 mb-2\">24/7</div>\n            <div className=\"text-gray-600\">Support</div>\n          </div>\n        </motion.div>\n      </div>\n    </section>\n  )\n}\n\nexport default Testimonials\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;;;AARA;;;;;;;;AAUA,MAAM,eAAe;IACnB;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,SAAS;QACT,QAAQ;QACR,QAAQ;QACR,SAAS;QACT,SAAS;QACT,MAAM;IACR;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,SAAS;QACT,QAAQ;QACR,QAAQ;QACR,SAAS;QACT,SAAS;QACT,MAAM;IACR;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,SAAS;QACT,QAAQ;QACR,QAAQ;QACR,SAAS;QACT,SAAS;QACT,MAAM;IACR;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,SAAS;QACT,QAAQ;QACR,QAAQ;QACR,SAAS;QACT,SAAS;QACT,MAAM;IACR;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,SAAS;QACT,QAAQ;QACR,QAAQ;QACR,SAAS;QACT,SAAS;QACT,MAAM;IACR;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,SAAS;QACT,QAAQ;QACR,QAAQ;QACR,SAAS;QACT,SAAS;QACT,MAAM;IACR;CACD;AAED,MAAM,eAAe;;IACnB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,0BAA0B;IAC1B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,IAAI,CAAC,eAAe;YAEpB,MAAM,WAAW;mDAAY;oBAC3B;2DAAgB,CAAC,OAAS,CAAC,OAAO,CAAC,IAAI,aAAa,MAAM;;gBAC5D;kDAAG;YAEH;0CAAO,IAAM,cAAc;;QAC7B;iCAAG;QAAC;KAAc;IAElB,MAAM,kBAAkB;QACtB,gBAAgB,CAAC,OAAS,CAAC,OAAO,CAAC,IAAI,aAAa,MAAM;IAC5D;IAEA,MAAM,kBAAkB;QACtB,gBAAgB,CAAC,OAAS,CAAC,OAAO,IAAI,aAAa,MAAM,IAAI,aAAa,MAAM;IAClF;IAEA,MAAM,kBAAkB,CAAC;QACvB,gBAAgB;IAClB;IAEA,qBACE,6LAAC;QAAQ,WAAU;kBACjB,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAEV,6LAAC,oIAAA,CAAA,QAAK;4BAAC,WAAU;sCAA+D;;;;;;sCAGhF,6LAAC;4BAAG,WAAU;;gCAAiD;8CAE7D,6LAAC;oCAAK,WAAU;8CAA2E;;;;;;8CAC3F,6LAAC;;;;;gCAAK;;;;;;;sCAER,6LAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;8BAMzD,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BACC,WAAU;4BACV,cAAc,IAAM,iBAAiB;4BACrC,cAAc,IAAM,iBAAiB;sCAErC,cAAA,6LAAC,4LAAA,CAAA,kBAAe;gCAAC,MAAK;0CACpB,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAI;oCAC9B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,MAAM;wCAAE,SAAS;wCAAG,GAAG,CAAC;oCAAI;oCAC5B,YAAY;wCAAE,UAAU;oCAAI;oCAC5B,WAAU;8CAEV,cAAA,6LAAC,mIAAA,CAAA,OAAI;wCAAC,WAAU;kDACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;4CAAC,WAAU;sDACrB,cAAA,6LAAC;gDAAI,WAAU;;kEAEb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;;;;;;;;;;;kEAKrB,6LAAC;wDAAI,WAAU;;0EAEb,6LAAC;gEAAI,WAAU;0EACZ;uEAAI,MAAM,YAAY,CAAC,aAAa,CAAC,MAAM;iEAAE,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrD,6LAAC,qMAAA,CAAA,OAAI;wEAAS,WAAU;uEAAb;;;;;;;;;;0EAKf,6LAAC;gEAAW,WAAU;;oEAAsD;oEACxE,YAAY,CAAC,aAAa,CAAC,OAAO;oEAAC;;;;;;;0EAIvC,6LAAC,oIAAA,CAAA,QAAK;gEAAC,WAAU;0EACd,YAAY,CAAC,aAAa,CAAC,OAAO;;;;;;0EAIrC,6LAAC;gEAAI,WAAU;;kFACb,6LAAC,qIAAA,CAAA,SAAM;wEACL,KAAK,YAAY,CAAC,aAAa,CAAC,MAAM;wEACtC,KAAK,YAAY,CAAC,aAAa,CAAC,IAAI;wEACpC,MAAK;;;;;;kFAEP,6LAAC;;0FACC,6LAAC;gFAAI,WAAU;0FACZ,YAAY,CAAC,aAAa,CAAC,IAAI;;;;;;0FAElC,6LAAC;gFAAI,WAAU;0FACZ,YAAY,CAAC,aAAa,CAAC,IAAI;;;;;;0FAElC,6LAAC;gFAAI,WAAU;;kGACb,6LAAC;kGAAM,YAAY,CAAC,aAAa,CAAC,IAAI;;;;;;oFACrC,YAAY,CAAC,aAAa,CAAC,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;mCApD5C;;;;;;;;;;;;;;;sCAiEX,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,SAAS;4BACT,WAAU;sCAEV,cAAA,6LAAC,uNAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;;;;;;sCAEzB,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,SAAS;4BACT,WAAU;sCAEV,cAAA,6LAAC,yNAAA,CAAA,eAAY;gCAAC,WAAU;;;;;;;;;;;;;;;;;8BAK5B,6LAAC;oBAAI,WAAU;8BACZ,aAAa,GAAG,CAAC,CAAC,GAAG,sBACpB,6LAAC;4BAEC,SAAS,IAAM,gBAAgB;4BAC/B,WAAW,AAAC,oDAIX,OAHC,UAAU,eACN,4BACA;2BALD;;;;;;;;;;8BAYX,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAEV,6LAAC;;8CACC,6LAAC;oCAAI,WAAU;8CAAsD;;;;;;8CACrE,6LAAC;oCAAI,WAAU;8CAAgB;;;;;;;;;;;;sCAEjC,6LAAC;;8CACC,6LAAC;oCAAI,WAAU;8CAAoD;;;;;;8CACnE,6LAAC;oCAAI,WAAU;8CAAgB;;;;;;;;;;;;sCAEjC,6LAAC;;8CACC,6LAAC;oCAAI,WAAU;8CAAqD;;;;;;8CACpE,6LAAC;oCAAI,WAAU;8CAAgB;;;;;;;;;;;;sCAEjC,6LAAC;;8CACC,6LAAC;oCAAI,WAAU;8CAAsD;;;;;;8CACrE,6LAAC;oCAAI,WAAU;8CAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM3C;GA1LM;KAAA;uCA4LS", "debugId": null}}, {"offset": {"line": 8777, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Landing%20page/admybrand-ai-suite/src/components/ui/floating-action-button.tsx"], "sourcesContent": ["'use client'\n\nimport { motion, AnimatePresence } from 'framer-motion'\nimport { MessageCircle, X, Phone, Mail, Calendar } from 'lucide-react'\nimport { useState } from 'react'\n\nconst FloatingActionButton = () => {\n  const [isOpen, setIsOpen] = useState(false)\n\n  const actions = [\n    {\n      icon: Calendar,\n      label: 'Book Demo',\n      color: 'from-blue-500 to-purple-500',\n      action: () => console.log('Book demo')\n    },\n    {\n      icon: Phone,\n      label: 'Call Us',\n      color: 'from-green-500 to-teal-500',\n      action: () => console.log('Call us')\n    },\n    {\n      icon: Mail,\n      label: 'Email',\n      color: 'from-orange-500 to-red-500',\n      action: () => console.log('Email')\n    }\n  ]\n\n  return (\n    <div className=\"fixed bottom-6 right-6 z-50\">\n      {/* Action buttons */}\n      <AnimatePresence>\n        {isOpen && (\n          <motion.div\n            initial={{ opacity: 0, scale: 0.8 }}\n            animate={{ opacity: 1, scale: 1 }}\n            exit={{ opacity: 0, scale: 0.8 }}\n            transition={{ duration: 0.2 }}\n            className=\"absolute bottom-16 right-0 space-y-3\"\n          >\n            {actions.map((action, index) => (\n              <motion.button\n                key={action.label}\n                initial={{ opacity: 0, x: 20 }}\n                animate={{ opacity: 1, x: 0 }}\n                exit={{ opacity: 0, x: 20 }}\n                transition={{ duration: 0.2, delay: index * 0.05 }}\n                onClick={action.action}\n                className={`flex items-center gap-3 bg-gradient-to-r ${action.color} text-white px-4 py-3 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 group`}\n                whileHover={{ scale: 1.05 }}\n                whileTap={{ scale: 0.95 }}\n              >\n                <action.icon className=\"w-5 h-5\" />\n                <span className=\"text-sm font-medium whitespace-nowrap\">\n                  {action.label}\n                </span>\n              </motion.button>\n            ))}\n          </motion.div>\n        )}\n      </AnimatePresence>\n\n      {/* Main FAB */}\n      <motion.button\n        onClick={() => setIsOpen(!isOpen)}\n        className=\"w-14 h-14 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center\"\n        whileHover={{ scale: 1.1 }}\n        whileTap={{ scale: 0.9 }}\n        animate={{ rotate: isOpen ? 45 : 0 }}\n      >\n        {isOpen ? (\n          <X className=\"w-6 h-6\" />\n        ) : (\n          <MessageCircle className=\"w-6 h-6\" />\n        )}\n      </motion.button>\n    </div>\n  )\n}\n\nexport default FloatingActionButton\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;;;AAJA;;;;AAMA,MAAM,uBAAuB;;IAC3B,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,MAAM,UAAU;QACd;YACE,MAAM,6MAAA,CAAA,WAAQ;YACd,OAAO;YACP,OAAO;YACP,QAAQ,IAAM,QAAQ,GAAG,CAAC;QAC5B;QACA;YACE,MAAM,uMAAA,CAAA,QAAK;YACX,OAAO;YACP,OAAO;YACP,QAAQ,IAAM,QAAQ,GAAG,CAAC;QAC5B;QACA;YACE,MAAM,qMAAA,CAAA,OAAI;YACV,OAAO;YACP,OAAO;YACP,QAAQ,IAAM,QAAQ,GAAG,CAAC;QAC5B;KACD;IAED,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC,4LAAA,CAAA,kBAAe;0BACb,wBACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,OAAO;oBAAI;oBAClC,SAAS;wBAAE,SAAS;wBAAG,OAAO;oBAAE;oBAChC,MAAM;wBAAE,SAAS;wBAAG,OAAO;oBAAI;oBAC/B,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,WAAU;8BAET,QAAQ,GAAG,CAAC,CAAC,QAAQ,sBACpB,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;4BAEZ,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,MAAM;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC1B,YAAY;gCAAE,UAAU;gCAAK,OAAO,QAAQ;4BAAK;4BACjD,SAAS,OAAO,MAAM;4BACtB,WAAW,AAAC,4CAAwD,OAAb,OAAO,KAAK,EAAC;4BACpE,YAAY;gCAAE,OAAO;4BAAK;4BAC1B,UAAU;gCAAE,OAAO;4BAAK;;8CAExB,6LAAC,OAAO,IAAI;oCAAC,WAAU;;;;;;8CACvB,6LAAC;oCAAK,WAAU;8CACb,OAAO,KAAK;;;;;;;2BAZV,OAAO,KAAK;;;;;;;;;;;;;;;0BAqB3B,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;gBACZ,SAAS,IAAM,UAAU,CAAC;gBAC1B,WAAU;gBACV,YAAY;oBAAE,OAAO;gBAAI;gBACzB,UAAU;oBAAE,OAAO;gBAAI;gBACvB,SAAS;oBAAE,QAAQ,SAAS,KAAK;gBAAE;0BAElC,uBACC,6LAAC,+LAAA,CAAA,IAAC;oBAAC,WAAU;;;;;6EAEb,6LAAC,2NAAA,CAAA,gBAAa;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAKnC;GA1EM;KAAA;uCA4ES", "debugId": null}}]}